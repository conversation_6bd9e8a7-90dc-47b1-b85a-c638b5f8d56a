# OSS 图片URL类型说明

## 🔗 URL类型对比

### 1. 签名URL（当前使用）
**特点：**
- ✅ 适用于私有Bucket
- ✅ 安全性高
- ❌ 有过期时间（现已设置为10年）
- ❌ URL较长，包含签名参数

**示例：**
```
https://mockpix-oss-bucket.oss-cn-guangzhou.aliyuncs.com/images/xxx.png?Expires=1755855359&OSSAccessKeyId=xxx&Signature=xxx
```

### 2. 永久URL（推荐用于分享）
**特点：**
- ✅ 永久有效，无过期时间
- ✅ URL简洁美观
- ❌ 需要Bucket设置为"公共读"
- ⚠️ 安全性相对较低（任何人都能访问）

**示例：**
```
https://mockpix-oss-bucket.oss-cn-guangzhou.aliyuncs.com/images/xxx.png
```

## 🛠️ 如何获得永久URL

### 方案1：修改Bucket权限（推荐）

1. **登录OSS控制台**：https://oss.console.aliyun.com/
2. **进入Bucket**：`mockpix-oss-bucket`
3. **修改权限**：概览 > 基础设置 > 读写权限
4. **选择权限**：改为"公共读"

**权限说明：**
- **私有**：只有拥有者能访问，需要签名URL
- **公共读**：任何人都能读取文件，但只有拥有者能写入
- **公共读写**：任何人都能读写（不推荐）

### 方案2：使用自定义域名

1. **绑定域名**：在OSS控制台绑定您的域名
2. **配置CNAME**：将域名指向OSS Bucket
3. **使用短域名**：`https://yourdomain.com/images/xxx.png`

### 方案3：延长签名时间（当前方案）

- 已将签名URL有效期设置为10年
- 基本等同于永久有效
- 保持私有Bucket的安全性

## 🎯 推荐配置

基于您的需求，我推荐以下配置：

### 开发/测试环境
```
Bucket权限：公共读
URL类型：永久URL
优点：简洁、永久、易于调试
```

### 生产环境
```
Bucket权限：私有
URL类型：长期签名URL（10年）
优点：安全、基本永久
```

## 🔄 当前实现

代码已更新，现在会同时返回两种URL：

```javascript
{
  url: "签名URL（10年有效）",
  permanentUrl: "永久URL（需要公共读权限）"
}
```

- **复制链接**时优先复制永久URL
- **控制台**会显示两种URL供选择
- **Toast提示**会显示使用的URL类型

## 💡 建议

1. **如果图片需要长期分享**：建议设置Bucket为"公共读"
2. **如果注重安全性**：保持当前配置（10年签名URL）
3. **如果有自定义域名**：推荐绑定域名使用短URL

---

**注意**：修改Bucket权限可能需要几分钟生效，请耐心等待。