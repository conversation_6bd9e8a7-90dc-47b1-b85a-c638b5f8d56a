新建一个变量，保存数据

widnow.wathDat = [

<!-- {
zoomValue: '',
styleValue: {
dragPadTransform: '',
viewfinderDivScale: '',
}
} -->

]
监听 class="slider-component undefined-disabled hide-rail" 下的 input 变化，
当 input 切换时候，
zoomValue 等于 input的value值
styleValue 等于 {
dragPadTransform: 'drag-handle'的style的transform值,
viewfinderDivScale: 'viewfinder-div default-viewfinder'的style的scale值,
}

参考：
class="drag-pad"

```
<div class="drag-pad" style="position:relative;width:200px;max-width:200px;height:148px;max-height:148px;box-sizing:border-box"><div class="drag-handle " style="transform: translateX(85.7143px) translateY(63.4286px); will-change: transform; width: 28.5714px; height: 21.1429px; cursor: grab; touch-action: none; display: grid; place-items: center;" tabindex="0"><div class="viewfinder-div default-viewfinder " style="position: absolute; width: 208px; height: 156px; scale: 1;"></div></div></div>
```

class="slider-component undefined-disabled hide-rail"

```
<span dir="ltr" data-orientation="horizontal" aria-disabled="false" class="slider-component undefined-disabled hide-rail" style="width:100%;--radix-slider-thumb-transform:translateX(-50%)"><span data-orientation="horizontal" class="SliderTrack track"><span data-orientation="horizontal" class="SliderRange rail" style="left: 0%; right: 92.3077%;"></span></span><span style="transform: var(--radix-slider-thumb-transform); position: absolute; left: calc(7.69231% + 2.96154px);"><span role="slider" aria-label="Volume" aria-valuemin="75" aria-valuemax="400" aria-orientation="horizontal" data-orientation="horizontal" tabindex="0" class="SliderThumb thumb" style="" data-radix-collection-item="" aria-valuenow="100"></span><input value="100" style="display: none;"></span><div class="labels"><span id="zoom-slider-label">Zoom</span><span>100%</span></div></span>
```
