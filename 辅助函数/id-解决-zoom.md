# Zoom 功能实现方案

## 问题分析

通过分析 `id-1-zoom.js` 和 `id-5-zoom.js` 的数据，发现以下关键问题：

1. **当前实现错误**：zoom 计算不应该依赖布局的 componentTransform.scale
2. **数据证明**：id:1（默认视图）和 id:5（底部放大，scale: 1.8）的 zoom 数据完全相同
3. **核心原则**：zoom 控制应该完全独立于布局配置

## 发现的规律

### 1. Scale 计算规律
```javascript
scale = 100 / value
```
- value: 100 → scale: 1.0
- value: 200 → scale: 0.5
- value: 144 → scale: 0.694444

### 2. TranslateX 规律
- 固定值：`85.7143px`
- 计算方式：`(drag-pad宽度200 - drag-handle宽度28.5714) / 2`

### 3. TranslateY 规律
- 默认垂直居中：`63.4286px = (drag-pad高度148 - drag-handle高度21.1429) / 2`
- 存在边界限制机制：
  - value ≤ 143：保持居中（63.4286px）
  - value = 144：突变到顶部（21.1429px）
  - value > 144：根据 viewfinder 实际尺寸动态调整

## 代码实现方案

### 1. 修改 calculateScaleFromZoom 函数

```javascript
// 移除 layoutScale 参数，使 zoom 计算完全独立
const calculateScaleFromZoom = (zoom: number): number => {
    return 100 / zoom
}
```

### 2. 实现智能边界检测算法

```javascript
const calculateDragHandlePosition = (zoom: number): DragHandlePosition => {
    // 固定参数
    const dragPadWidth = 200
    const dragPadHeight = 148
    const dragHandleWidth = 28.5714
    const dragHandleHeight = 21.1429
    const viewfinderWidth = 208
    const viewfinderHeight = 156
    
    // 计算 scale
    const scale = 100 / zoom
    
    // X 轴始终居中
    const x = (dragPadWidth - dragHandleWidth) / 2  // 85.7143
    
    // Y 轴根据 viewfinder 实际尺寸计算
    const actualViewfinderHeight = viewfinderHeight * scale
    
    // 计算可用的垂直移动范围
    const maxY = dragPadHeight - dragHandleHeight  // 126.8571
    const centerY = maxY / 2  // 63.4286
    
    // 边界检测逻辑
    let y: number
    
    if (zoom <= 143) {
        // 保持居中
        y = centerY
    } else if (zoom === 144) {
        // 特殊转折点
        y = dragHandleHeight  // 21.1429
    } else {
        // 根据 viewfinder 实际高度动态调整
        // 确保 viewfinder 不会超出 drag-pad 边界
        const viewfinderBottom = centerY + (actualViewfinderHeight / 2)
        if (viewfinderBottom > dragPadHeight) {
            // 需要向上调整
            y = dragPadHeight - (actualViewfinderHeight / 2) - (dragHandleHeight / 2)
            y = Math.max(0, Math.min(y, maxY))
        } else {
            y = centerY
        }
    }
    
    return { x, y }
}
```

### 3. 修改 PcRightSlider 组件

需要修改的代码位置：

1. **第 46-53 行**：移除 layoutScale 参数
```javascript
const calculateScaleFromZoom = (zoom: number): number => {
    return 100 / zoom
}
```

2. **第 768 和 808 行**：移除 activeLayoutConfig.componentTransform?.scale
```javascript
// 修改前
scale: calculateScaleFromZoom(
    currentValue,
    activeLayoutConfig.componentTransform?.scale || 1,
)

// 修改后
scale: calculateScaleFromZoom(currentValue)
```

3. **添加边界检测逻辑**：在 zoom 值变化时自动调整 drag-handle 位置
```javascript
// 在 handleZoomChange 函数中添加
const handleZoomChange = useCallback((value: number) => {
    setValue('zoom', value)
    setZoom(value)
    
    // 根据新的 zoom 值计算并更新 drag-handle 位置
    const newPosition = calculateDragHandlePosition(value)
    setDragHandlePosition(newPosition)
}, [setValue, setZoom, setDragHandlePosition])
```

## 测试验证

1. 测试不同布局（id:1 和 id:5）的 zoom 行为是否一致
2. 验证 zoom 值 144 的转折点行为
3. 确保 viewfinder 始终在可视区域内
4. 验证拖拽和点击的交互行为

## 注意事项

1. zoom 控制必须完全独立于布局的 componentTransform
2. 边界检测算法需要考虑 viewfinder 的实际尺寸
3. 保持向后兼容性，确保现有功能不受影响

## 实际实现总结

已完成以下修改：

1. **修改了 calculateScaleFromZoom 函数**（第47-51行）
   - 移除了 layoutScale 参数
   - 现在直接返回 `100 / zoom`

2. **添加了 calculateDragHandlePosition 函数**（第80-126行）
   - 实现了智能边界检测逻辑
   - 处理了 zoom = 144 的特殊转折点
   - 确保 viewfinder 始终在可视区域内

3. **更新了 handleZoomChange 函数**（第168-175行）
   - 在 zoom 值变化时自动计算新的 drag-handle 位置
   - 调用 setDragHandlePosition 更新位置

4. **添加了初始化 useEffect**（第162-165行）
   - 组件挂载时根据当前 zoom 值设置初始 drag-handle 位置

5. **第768和808行已经正确更新**
   - calculateScaleFromZoom 现在只接收 currentValue 参数
   - 不再传入 activeLayoutConfig.componentTransform?.scale