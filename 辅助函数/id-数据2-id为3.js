data = [
    {
        zoomValue: 75,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(144.617px)',
            viewfinderDivScale: '1.33333',
        },
        timestamp: '2025-08-14T02:31:48.383Z',
    },
    {
        zoomValue: 76,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(143.549px)',
            viewfinderDivScale: '1.31579',
        },
        timestamp: '2025-08-14T02:31:48.355Z',
    },
    {
        zoomValue: 77,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(142.508px)',
            viewfinderDivScale: '1.2987',
        },
        timestamp: '2025-08-14T02:31:48.334Z',
    },
    {
        zoomValue: 78,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(141.495px)',
            viewfinderDivScale: '1.28205',
        },
        timestamp: '2025-08-14T02:31:48.296Z',
    },
    {
        zoomValue: 79,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(140.506px)',
            viewfinderDivScale: '1.26582',
        },
        timestamp: '2025-08-14T02:31:48.260Z',
    },
    {
        zoomValue: 80,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(139.543px)',
            viewfinderDivScale: '1.25',
        },
        timestamp: '2025-08-14T02:31:48.249Z',
    },
    {
        zoomValue: 81,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(138.603px)',
            viewfinderDivScale: '1.23457',
        },
        timestamp: '2025-08-14T02:31:48.214Z',
    },
    {
        zoomValue: 82,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(137.686px)',
            viewfinderDivScale: '1.21951',
        },
        timestamp: '2025-08-14T02:31:48.192Z',
    },
    {
        zoomValue: 83,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(136.792px)',
            viewfinderDivScale: '1.20482',
        },
        timestamp: '2025-08-14T02:31:48.146Z',
    },
    {
        zoomValue: 84,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(135.918px)',
            viewfinderDivScale: '1.19048',
        },
        timestamp: '2025-08-14T02:31:48.109Z',
    },
    {
        zoomValue: 85,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(135.066px)',
            viewfinderDivScale: '1.17647',
        },
        timestamp: '2025-08-14T02:31:48.064Z',
    },
    {
        zoomValue: 86,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(134.233px)',
            viewfinderDivScale: '1.16279',
        },
        timestamp: '2025-08-14T02:31:48.019Z',
    },
    {
        zoomValue: 87,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(133.419px)',
            viewfinderDivScale: '1.14943',
        },
        timestamp: '2025-08-14T02:31:48.003Z',
    },
    {
        zoomValue: 88,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(132.623px)',
            viewfinderDivScale: '1.13636',
        },
        timestamp: '2025-08-14T02:31:47.973Z',
    },
    {
        zoomValue: 89,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(131.846px)',
            viewfinderDivScale: '1.1236',
        },
        timestamp: '2025-08-14T02:31:47.951Z',
    },
    {
        zoomValue: 90,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(131.086px)',
            viewfinderDivScale: '1.11111',
        },
        timestamp: '2025-08-14T02:31:47.906Z',
    },
    {
        zoomValue: 91,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(130.342px)',
            viewfinderDivScale: '1.0989',
        },
        timestamp: '2025-08-14T02:31:47.891Z',
    },
    {
        zoomValue: 92,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(129.615px)',
            viewfinderDivScale: '1.08696',
        },
        timestamp: '2025-08-14T02:31:47.869Z',
    },
    {
        zoomValue: 93,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(128.903px)',
            viewfinderDivScale: '1.07527',
        },
        timestamp: '2025-08-14T02:31:47.817Z',
    },
    {
        zoomValue: 94,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(128.207px)',
            viewfinderDivScale: '1.06383',
        },
        timestamp: '2025-08-14T02:31:47.787Z',
    },
    {
        zoomValue: 95,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(127.525px)',
            viewfinderDivScale: '1.05263',
        },
        timestamp: '2025-08-14T02:31:47.757Z',
    },
    {
        zoomValue: 96,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(126.857px)',
            viewfinderDivScale: '1.04167',
        },
        timestamp: '2025-08-14T02:31:47.716Z',
    },
    {
        zoomValue: 97,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(126.203px)',
            viewfinderDivScale: '1.03093',
        },
        timestamp: '2025-08-14T02:31:47.699Z',
    },
    {
        zoomValue: 98,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(125.563px)',
            viewfinderDivScale: '1.02041',
        },
        timestamp: '2025-08-14T02:31:47.636Z',
    },
    {
        zoomValue: 99,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(124.935px)',
            viewfinderDivScale: '1.0101',
        },
        timestamp: '2025-08-14T02:31:47.576Z',
    },
    {
        zoomValue: 100,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(124.32px)',
            viewfinderDivScale: '1',
        },
        timestamp: '2025-08-14T02:31:47.507Z',
    },
    {
        zoomValue: 101,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(123.717px)',
            viewfinderDivScale: '0.990099',
        },
        timestamp: '2025-08-14T02:31:47.434Z',
    },
    {
        zoomValue: 102,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(123.126px)',
            viewfinderDivScale: '0.980392',
        },
        timestamp: '2025-08-14T02:31:47.404Z',
    },
    {
        zoomValue: 103,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(122.546px)',
            viewfinderDivScale: '0.970874',
        },
        timestamp: '2025-08-14T02:31:47.358Z',
    },
    {
        zoomValue: 104,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(121.978px)',
            viewfinderDivScale: '0.961538',
        },
        timestamp: '2025-08-14T02:31:47.321Z',
    },
    {
        zoomValue: 105,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(121.42px)',
            viewfinderDivScale: '0.952381',
        },
        timestamp: '2025-08-14T02:31:47.291Z',
    },
    {
        zoomValue: 106,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(120.873px)',
            viewfinderDivScale: '0.943396',
        },
        timestamp: '2025-08-14T02:31:47.262Z',
    },
    {
        zoomValue: 107,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(120.336px)',
            viewfinderDivScale: '0.934579',
        },
        timestamp: '2025-08-14T02:31:47.238Z',
    },
    {
        zoomValue: 108,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(119.81px)',
            viewfinderDivScale: '0.925926',
        },
        timestamp: '2025-08-14T02:31:47.202Z',
    },
    {
        zoomValue: 109,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(119.292px)',
            viewfinderDivScale: '0.917431',
        },
        timestamp: '2025-08-14T02:31:47.170Z',
    },
    {
        zoomValue: 110,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(118.784px)',
            viewfinderDivScale: '0.909091',
        },
        timestamp: '2025-08-14T02:31:47.147Z',
    },
    {
        zoomValue: 111,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(118.286px)',
            viewfinderDivScale: '0.900901',
        },
        timestamp: '2025-08-14T02:31:47.133Z',
    },
    {
        zoomValue: 112,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(117.796px)',
            viewfinderDivScale: '0.892857',
        },
        timestamp: '2025-08-14T02:31:47.121Z',
    },
    {
        zoomValue: 113,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(117.315px)',
            viewfinderDivScale: '0.884956',
        },
        timestamp: '2025-08-14T02:31:47.104Z',
    },
    {
        zoomValue: 114,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(116.842px)',
            viewfinderDivScale: '0.877193',
        },
        timestamp: '2025-08-14T02:31:47.091Z',
    },
    {
        zoomValue: 115,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(116.378px)',
            viewfinderDivScale: '0.869565',
        },
        timestamp: '2025-08-14T02:31:47.081Z',
    },
    {
        zoomValue: 116,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(115.921px)',
            viewfinderDivScale: '0.862069',
        },
        timestamp: '2025-08-14T02:31:47.066Z',
    },
    {
        zoomValue: 117,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(115.473px)',
            viewfinderDivScale: '0.854701',
        },
        timestamp: '2025-08-14T02:31:47.055Z',
    },
    {
        zoomValue: 118,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(115.031px)',
            viewfinderDivScale: '0.847458',
        },
        timestamp: '2025-08-14T02:31:47.035Z',
    },
    {
        zoomValue: 119,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(114.598px)',
            viewfinderDivScale: '0.840336',
        },
        timestamp: '2025-08-14T02:31:47.014Z',
    },
    {
        zoomValue: 120,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(114.171px)',
            viewfinderDivScale: '0.833333',
        },
        timestamp: '2025-08-14T02:31:46.991Z',
    },
    {
        zoomValue: 121,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(113.752px)',
            viewfinderDivScale: '0.826446',
        },
        timestamp: '2025-08-14T02:31:46.936Z',
    },
    {
        zoomValue: 122,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(113.34px)',
            viewfinderDivScale: '0.819672',
        },
        timestamp: '2025-08-14T02:31:46.895Z',
    },
    {
        zoomValue: 123,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(112.934px)',
            viewfinderDivScale: '0.813008',
        },
        timestamp: '2025-08-14T02:31:46.819Z',
    },
    {
        zoomValue: 124,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(112.535px)',
            viewfinderDivScale: '0.806452',
        },
        timestamp: '2025-08-14T02:31:46.766Z',
    },
    {
        zoomValue: 125,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(112.142px)',
            viewfinderDivScale: '0.8',
        },
        timestamp: '2025-08-14T02:31:46.721Z',
    },
    {
        zoomValue: 126,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(111.755px)',
            viewfinderDivScale: '0.793651',
        },
        timestamp: '2025-08-14T02:31:46.684Z',
    },
    {
        zoomValue: 127,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(111.375px)',
            viewfinderDivScale: '0.787402',
        },
        timestamp: '2025-08-14T02:31:51.632Z',
    },
    {
        zoomValue: 128,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(111px)',
            viewfinderDivScale: '0.78125',
        },
        timestamp: '2025-08-14T02:31:46.646Z',
    },
    {
        zoomValue: 129,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(110.631px)',
            viewfinderDivScale: '0.775194',
        },
        timestamp: '2025-08-14T02:31:46.623Z',
    },
    {
        zoomValue: 130,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(110.268px)',
            viewfinderDivScale: '0.769231',
        },
        timestamp: '2025-08-14T02:31:46.600Z',
    },
    {
        zoomValue: 131,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(109.911px)',
            viewfinderDivScale: '0.763359',
        },
        timestamp: '2025-08-14T02:31:46.590Z',
    },
    {
        zoomValue: 132,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(109.558px)',
            viewfinderDivScale: '0.757576',
        },
        timestamp: '2025-08-14T02:31:46.579Z',
    },
    {
        zoomValue: 133,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(109.212px)',
            viewfinderDivScale: '0.75188',
        },
        timestamp: '2025-08-14T02:31:46.556Z',
    },
    {
        zoomValue: 134,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(108.87px)',
            viewfinderDivScale: '0.746269',
        },
        timestamp: '2025-08-14T02:31:46.533Z',
    },
    {
        zoomValue: 135,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(108.533px)',
            viewfinderDivScale: '0.740741',
        },
        timestamp: '2025-08-14T02:31:46.496Z',
    },
    {
        zoomValue: 136,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(108.202px)',
            viewfinderDivScale: '0.735294',
        },
        timestamp: '2025-08-14T02:31:46.464Z',
    },
    {
        zoomValue: 137,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(107.875px)',
            viewfinderDivScale: '0.729927',
        },
        timestamp: '2025-08-14T02:31:46.437Z',
    },
    {
        zoomValue: 138,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(107.553px)',
            viewfinderDivScale: '0.724638',
        },
        timestamp: '2025-08-14T02:31:46.414Z',
    },
    {
        zoomValue: 139,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(107.235px)',
            viewfinderDivScale: '0.719424',
        },
        timestamp: '2025-08-14T02:31:46.392Z',
    },
    {
        zoomValue: 140,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(106.922px)',
            viewfinderDivScale: '0.714286',
        },
        timestamp: '2025-08-14T02:31:46.381Z',
    },
    {
        zoomValue: 141,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(106.614px)',
            viewfinderDivScale: '0.70922',
        },
        timestamp: '2025-08-14T02:31:46.361Z',
    },
    {
        zoomValue: 142,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(106.31px)',
            viewfinderDivScale: '0.704225',
        },
        timestamp: '2025-08-14T02:31:52.181Z',
    },
    {
        zoomValue: 143,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(106.01px)',
            viewfinderDivScale: '0.699301',
        },
        timestamp: '2025-08-14T02:31:46.348Z',
    },
    {
        zoomValue: 144,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(105.714px)',
            viewfinderDivScale: '0.694444',
        },
        timestamp: '2025-08-14T02:31:46.323Z',
    },
    {
        zoomValue: 145,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(105.423px)',
            viewfinderDivScale: '0.689655',
        },
        timestamp: '2025-08-14T02:31:46.300Z',
    },
    {
        zoomValue: 146,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(105.135px)',
            viewfinderDivScale: '0.684932',
        },
        timestamp: '2025-08-14T02:31:46.286Z',
    },
    {
        zoomValue: 147,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(104.851px)',
            viewfinderDivScale: '0.680272',
        },
        timestamp: '2025-08-14T02:31:52.402Z',
    },
    {
        zoomValue: 148,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(104.571px)',
            viewfinderDivScale: '0.675676',
        },
        timestamp: '2025-08-14T02:31:46.258Z',
    },
    {
        zoomValue: 149,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(104.295px)',
            viewfinderDivScale: '0.671141',
        },
        timestamp: '2025-08-14T02:31:46.247Z',
    },
    {
        zoomValue: 150,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(104.023px)',
            viewfinderDivScale: '0.666667',
        },
        timestamp: '2025-08-14T02:31:46.229Z',
    },
    {
        zoomValue: 151,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(103.754px)',
            viewfinderDivScale: '0.662252',
        },
        timestamp: '2025-08-14T02:31:46.205Z',
    },
    {
        zoomValue: 152,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(103.489px)',
            viewfinderDivScale: '0.657895',
        },
        timestamp: '2025-08-14T02:31:46.195Z',
    },
    {
        zoomValue: 153,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(103.227px)',
            viewfinderDivScale: '0.653595',
        },
        timestamp: '2025-08-14T02:31:46.173Z',
    },
    {
        zoomValue: 154,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(102.968px)',
            viewfinderDivScale: '0.649351',
        },
        timestamp: '2025-08-14T02:31:46.137Z',
    },
    {
        zoomValue: 155,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(102.713px)',
            viewfinderDivScale: '0.645161',
        },
        timestamp: '2025-08-14T02:31:46.106Z',
    },
    {
        zoomValue: 156,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(102.462px)',
            viewfinderDivScale: '0.641026',
        },
        timestamp: '2025-08-14T02:31:46.016Z',
    },
    {
        zoomValue: 157,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(102.213px)',
            viewfinderDivScale: '0.636943',
        },
        timestamp: '2025-08-14T02:31:45.996Z',
    },
    {
        zoomValue: 158,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(101.967px)',
            viewfinderDivScale: '0.632911',
        },
        timestamp: '2025-08-14T02:31:45.956Z',
    },
    {
        zoomValue: 159,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(101.725px)',
            viewfinderDivScale: '0.628931',
        },
        timestamp: '2025-08-14T02:31:45.930Z',
    },
    {
        zoomValue: 160,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(101.486px)',
            viewfinderDivScale: '0.625',
        },
        timestamp: '2025-08-14T02:31:45.896Z',
    },
    {
        zoomValue: 161,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(101.249px)',
            viewfinderDivScale: '0.621118',
        },
        timestamp: '2025-08-14T02:31:45.849Z',
    },
    {
        zoomValue: 162,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(101.016px)',
            viewfinderDivScale: '0.617284',
        },
        timestamp: '2025-08-14T02:31:45.813Z',
    },
    {
        zoomValue: 163,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(100.785px)',
            viewfinderDivScale: '0.613497',
        },
        timestamp: '2025-08-14T02:31:45.791Z',
    },
    {
        zoomValue: 164,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(100.557px)',
            viewfinderDivScale: '0.609756',
        },
        timestamp: '2025-08-14T02:31:45.769Z',
    },
    {
        zoomValue: 165,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(100.332px)',
            viewfinderDivScale: '0.606061',
        },
        timestamp: '2025-08-14T02:31:45.746Z',
    },
    {
        zoomValue: 166,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(100.11px)',
            viewfinderDivScale: '0.60241',
        },
        timestamp: '2025-08-14T02:31:45.723Z',
    },
    {
        zoomValue: 167,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(99.8905px)',
            viewfinderDivScale: '0.598802',
        },
        timestamp: '2025-08-14T02:31:58.526Z',
    },
    {
        zoomValue: 168,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(99.6735px)',
            viewfinderDivScale: '0.595238',
        },
        timestamp: '2025-08-14T02:31:45.702Z',
    },
    {
        zoomValue: 169,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(99.459px)',
            viewfinderDivScale: '0.591716',
        },
        timestamp: '2025-08-14T02:31:45.689Z',
    },
    {
        zoomValue: 170,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(99.2471px)',
            viewfinderDivScale: '0.588235',
        },
        timestamp: '2025-08-14T02:31:45.672Z',
    },
    {
        zoomValue: 171,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(99.0376px)',
            viewfinderDivScale: '0.584795',
        },
        timestamp: '2025-08-14T02:31:45.653Z',
    },
    {
        zoomValue: 172,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(98.8306px)',
            viewfinderDivScale: '0.581395',
        },
        timestamp: '2025-08-14T02:31:45.633Z',
    },
    {
        zoomValue: 173,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(98.6259px)',
            viewfinderDivScale: '0.578035',
        },
        timestamp: '2025-08-14T02:31:45.596Z',
    },
    {
        zoomValue: 174,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(98.4236px)',
            viewfinderDivScale: '0.574713',
        },
        timestamp: '2025-08-14T02:31:45.549Z',
    },
    {
        zoomValue: 175,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(98.2237px)',
            viewfinderDivScale: '0.571429',
        },
        timestamp: '2025-08-14T02:31:45.530Z',
    },
    {
        zoomValue: 176,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(98.026px)',
            viewfinderDivScale: '0.568182',
        },
        timestamp: '2025-08-14T02:31:45.503Z',
    },
    {
        zoomValue: 177,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(97.8305px)',
            viewfinderDivScale: '0.564972',
        },
        timestamp: '2025-08-14T02:31:45.474Z',
    },
    {
        zoomValue: 178,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(97.6372px)',
            viewfinderDivScale: '0.561798',
        },
        timestamp: '2025-08-14T02:31:45.445Z',
    },
    {
        zoomValue: 179,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(97.4461px)',
            viewfinderDivScale: '0.558659',
        },
        timestamp: '2025-08-14T02:31:45.421Z',
    },
    {
        zoomValue: 180,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(97.2571px)',
            viewfinderDivScale: '0.555556',
        },
        timestamp: '2025-08-14T02:31:44.152Z',
    },
    {
        zoomValue: 181,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(97.0702px)',
            viewfinderDivScale: '0.552486',
        },
        timestamp: '2025-08-14T02:31:59.122Z',
    },
    {
        zoomValue: 182,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(96.8854px)',
            viewfinderDivScale: '0.549451',
        },
        timestamp: '2025-08-14T02:31:59.138Z',
    },
    {
        zoomValue: 183,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(96.7026px)',
            viewfinderDivScale: '0.546448',
        },
        timestamp: '2025-08-14T02:31:59.150Z',
    },
    {
        zoomValue: 184,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(96.5217px)',
            viewfinderDivScale: '0.543478',
        },
        timestamp: '2025-08-14T02:31:59.162Z',
    },
    {
        zoomValue: 185,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(96.3429px)',
            viewfinderDivScale: '0.540541',
        },
        timestamp: '2025-08-14T02:31:59.177Z',
    },
    {
        zoomValue: 186,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(96.1659px)',
            viewfinderDivScale: '0.537634',
        },
        timestamp: '2025-08-14T02:31:59.193Z',
    },
    {
        zoomValue: 187,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.9908px)',
            viewfinderDivScale: '0.534759',
        },
        timestamp: '2025-08-14T02:31:59.217Z',
    },
    {
        zoomValue: 188,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.8176px)',
            viewfinderDivScale: '0.531915',
        },
        timestamp: '2025-08-14T02:31:59.226Z',
    },
    {
        zoomValue: 189,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.6463px)',
            viewfinderDivScale: '0.529101',
        },
        timestamp: '2025-08-14T02:31:59.235Z',
    },
    {
        zoomValue: 190,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.4767px)',
            viewfinderDivScale: '0.526316',
        },
        timestamp: '2025-08-14T02:31:59.253Z',
    },
    {
        zoomValue: 191,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.3089px)',
            viewfinderDivScale: '0.52356',
        },
        timestamp: '2025-08-14T02:31:59.284Z',
    },
    {
        zoomValue: 192,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(95.1429px)',
            viewfinderDivScale: '0.520833',
        },
        timestamp: '2025-08-14T02:31:59.298Z',
    },
    {
        zoomValue: 193,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.9785px)',
            viewfinderDivScale: '0.518135',
        },
        timestamp: '2025-08-14T02:31:59.329Z',
    },
    {
        zoomValue: 194,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.8159px)',
            viewfinderDivScale: '0.515464',
        },
        timestamp: '2025-08-14T02:31:59.358Z',
    },
    {
        zoomValue: 195,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.6549px)',
            viewfinderDivScale: '0.512821',
        },
        timestamp: '2025-08-14T02:31:59.389Z',
    },
    {
        zoomValue: 196,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.4956px)',
            viewfinderDivScale: '0.510204',
        },
        timestamp: '2025-08-14T02:31:59.428Z',
    },
    {
        zoomValue: 197,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.3379px)',
            viewfinderDivScale: '0.507614',
        },
        timestamp: '2025-08-14T02:31:59.448Z',
    },
    {
        zoomValue: 198,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.1818px)',
            viewfinderDivScale: '0.505051',
        },
        timestamp: '2025-08-14T02:31:59.480Z',
    },
    {
        zoomValue: 199,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(94.0273px)',
            viewfinderDivScale: '0.502513',
        },
        timestamp: '2025-08-14T02:31:59.531Z',
    },
    {
        zoomValue: 200,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(93.8743px)',
            viewfinderDivScale: '0.5',
        },
        timestamp: '2025-08-14T02:31:59.546Z',
    },
    {
        zoomValue: 201,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(93.7228px)',
            viewfinderDivScale: '0.497512',
        },
        timestamp: '2025-08-14T02:31:59.557Z',
    },
    {
        zoomValue: 202,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(93.5728px)',
            viewfinderDivScale: '0.49505',
        },
        timestamp: '2025-08-14T02:31:59.569Z',
    },
]
