/**
 * 自动化测试套件 - 验证三种缩放模式算法的准确性
 */

import {
    calculateById,
    calculateMode1Transform,
    calculateMode2Transform,
    calculateMode3Transform
} from './cluade得出的算法'

// 类型定义
interface TestResult {
    readonly testName: string
    readonly passed: boolean
    readonly details: string
    readonly actualValue?: string
    readonly expectedValue?: string
    readonly error?: number
}

interface KeyTestCase {
    readonly mode: 1 | 3 | 5
    readonly zoom: number
    readonly expectedY: number
}

// 测试常量
const TEST_ZOOMS = [75, 100, 150, 200] as const
const FIXED_Y_VALUE = 63.4286
const TOLERANCE_STRICT = 0.01
const TOLERANCE_RELAXED = 0.1

const KEY_TEST_CASES: readonly KeyTestCase[] = [
    { mode: 3, zoom: 75, expectedY: 144.617 },
    { mode: 3, zoom: 200, expectedY: 93.8743 },
    { mode: 5, zoom: 75, expectedY: -17.76 },
    { mode: 5, zoom: 200, expectedY: 32.9829 }
] as const

/**
 * 从变换字符串中提取Y坐标值
 */
const extractYValue = (transform: string): number => {
    const match = transform.match(/translateY\(([^px]+)px\)/)
    if (!match) {
        throw new Error(`无法解析Y坐标: ${transform}`)
    }
    return parseFloat(match[1])
}

/**
 * 比较数值精度
 */
const compareValues = (
    actual: number, 
    expected: number, 
    tolerance = TOLERANCE_STRICT
): { isEqual: boolean; error: number } => {
    const error = Math.abs(actual - expected)
    return { isEqual: error <= tolerance, error }
}

/**
 * 创建测试结果对象
 */
const createTestResult = (
    testName: string,
    passed: boolean,
    details: string,
    actualValue?: string,
    expectedValue?: string,
    error?: number
): TestResult => ({
    testName,
    passed,
    details,
    actualValue,
    expectedValue,
    error
})

/**
 * 执行单个测试并捕获异常
 */
const executeTest = <T>(
    testName: string,
    testFn: () => T,
    validateFn: (result: T) => TestResult
): TestResult => {
    try {
        const result = testFn()
        return validateFn(result)
    } catch (error) {
        return createTestResult(
            testName,
            false,
            `执行失败: ${error}`,
            'Exception',
            'Normal'
        )
    }
}

/**
 * 测试模式1的固定Y坐标功能
 */
const testMode1FixedY = (): TestResult[] => {
    return TEST_ZOOMS.map(zoom => 
        executeTest(
            `模式1-固定Y-zoom${zoom}`,
            () => calculateById(1, zoom),
            (result) => {
                const actualY = extractYValue(result.dragPadTransform)
                const yCheck = compareValues(actualY, FIXED_Y_VALUE)
                
                return createTestResult(
                    `模式1-固定Y-zoom${zoom}`,
                    yCheck.isEqual,
                    `Y坐标应固定为${FIXED_Y_VALUE}px`,
                    actualY.toString(),
                    FIXED_Y_VALUE.toString(),
                    yCheck.error
                )
            }
        )
    )
}

/**
 * 测试关键数据点的精度
 */
const testKeyDataPoints = (): TestResult[] => {
    return KEY_TEST_CASES.map(testCase => 
        executeTest(
            `模式${testCase.mode}-数据验证-zoom${testCase.zoom}`,
            () => calculateById(testCase.mode, testCase.zoom),
            (result) => {
                const actualY = extractYValue(result.dragPadTransform)
                const yCheck = compareValues(actualY, testCase.expectedY, TOLERANCE_RELAXED)
                
                return createTestResult(
                    `模式${testCase.mode}-数据验证-zoom${testCase.zoom}`,
                    yCheck.isEqual,
                    'Y坐标精度检查',
                    actualY.toFixed(3),
                    testCase.expectedY.toString(),
                    yCheck.error
                )
            }
        )
    )
}

/**
 * 测试Scale计算的准确性
 */
const testScaleCalculation = (): TestResult[] => {
    return TEST_ZOOMS.map(zoom =>
        executeTest(
            `Scale计算-zoom${zoom}`,
            () => calculateMode1Transform(zoom),
            (result) => {
                const actualScale = parseFloat(result.viewfinderDivScale)
                const expectedScale = 100 / zoom
                const scaleCheck = compareValues(actualScale, expectedScale, 0.001)
                
                return createTestResult(
                    `Scale计算-zoom${zoom}`,
                    scaleCheck.isEqual,
                    `验证scale = 100/zoom`,
                    actualScale.toString(),
                    expectedScale.toString(),
                    scaleCheck.error
                )
            }
        )
    )
}

/**
 * 测试不同调用方式的一致性
 */
const testConsistency = (): TestResult[] => {
    return TEST_ZOOMS.map(zoom =>
        executeTest(
            `一致性检查-zoom${zoom}`,
            () => ({
                byId: calculateById(1, zoom),
                direct: calculateMode1Transform(zoom)
            }),
            ({ byId, direct }) => {
                const consistent = byId.dragPadTransform === direct.dragPadTransform &&
                                 byId.viewfinderDivScale === direct.viewfinderDivScale
                
                return createTestResult(
                    `一致性检查-zoom${zoom}`,
                    consistent,
                    '不同调用方式结果应一致',
                    byId.dragPadTransform,
                    direct.dragPadTransform
                )
            }
        )
    )
}

/**
 * 生成测试报告
 */
const generateReport = (results: TestResult[]): void => {
    const passed = results.filter(r => r.passed).length
    const total = results.length
    const passRate = (passed / total) * 100

    console.log(`测试结果: ${passed}/${total} 通过 (${passRate.toFixed(1)}%)`)

    const failed = results.filter(r => !r.passed)
    if (failed.length === 0) {
        console.log('🎉 所有测试通过!')
        return
    }

    console.log('❌ 失败项目:')
    failed.forEach(f => {
        console.log(`  ${f.testName}: ${f.details}`)
        if (f.actualValue && f.expectedValue) {
            console.log(`    实际: ${f.actualValue}, 期望: ${f.expectedValue}`)
        }
    })
}

/**
 * 快速测试核心功能
 */
export const runQuickTest = (): boolean => {
    console.log('⚡ 快速测试模式')
    
    const results = [
        ...testMode1FixedY(),
        ...testKeyDataPoints()
    ]
    
    generateReport(results)
    return results.every(r => r.passed)
}

/**
 * 完整测试所有功能
 */
export const runFullTest = (): boolean => {
    console.log('🔍 完整测试模式')
    
    const scaleResults = testScaleCalculation()
    const consistencyResults = testConsistency()
    const quickPassed = runQuickTest()
    
    if (!quickPassed) {
        scaleResults.push(createTestResult(
            '快速测试',
            false,
            '基础功能测试失败',
            'Failed',
            'Passed'
        ))
    }
    
    const allResults = [...scaleResults, ...consistencyResults]
    const passed = allResults.filter(r => r.passed).length
    const total = allResults.length
    const passRate = (passed / total) * 100
    
    console.log(`\n📊 完整测试报告`)
    console.log(`总测试: ${total}, 通过: ${passed}, 失败: ${total - passed}`)
    console.log(`通过率: ${passRate.toFixed(2)}%`)
    
    if (passRate === 100) {
        console.log('✅ 算法实现完全正确!')
    } else if (passRate >= 90) {
        console.log('✅ 算法基本正确，有少量问题')
    } else {
        console.log('❌ 算法存在问题，需要修复')
    }
    
    return passRate >= 90
}

// 如果直接运行此文件
if (typeof require !== 'undefined' && require.main === module) {
    console.log('🔧 执行自动化测试')
    
    const quickResult = runQuickTest()
    console.log(`\n快速测试: ${quickResult ? '✅ 通过' : '❌ 失败'}`)
    
    if (quickResult) {
        console.log('\n开始完整测试...')
        const fullResult = runFullTest()
        console.log(`完整测试: ${fullResult ? '✅ 通过' : '❌ 失败'}`)
    }
}