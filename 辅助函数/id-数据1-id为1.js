data = [
    {
        zoomValue: 75,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.33333',
        },
        timestamp: '2025-08-14T02:19:19.154Z',
    },
    {
        zoomValue: 76,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.31579',
        },
        timestamp: '2025-08-14T02:19:19.015Z',
    },
    {
        zoomValue: 77,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.2987',
        },
        timestamp: '2025-08-14T02:19:19.001Z',
    },
    {
        zoomValue: 78,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.28205',
        },
        timestamp: '2025-08-14T02:19:18.984Z',
    },
    {
        zoomValue: 79,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.26582',
        },
        timestamp: '2025-08-14T02:19:20.794Z',
    },
    {
        zoomValue: 80,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.25',
        },
        timestamp: '2025-08-14T02:19:20.820Z',
    },
    {
        zoomValue: 81,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.23457',
        },
        timestamp: '2025-08-14T02:19:18.924Z',
    },
    {
        zoomValue: 82,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.21951',
        },
        timestamp: '2025-08-14T02:19:20.879Z',
    },
    {
        zoomValue: 83,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.20482',
        },
        timestamp: '2025-08-14T02:19:20.929Z',
    },
    {
        zoomValue: 84,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.19048',
        },
        timestamp: '2025-08-14T02:19:18.900Z',
    },
    {
        zoomValue: 85,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.17647',
        },
        timestamp: '2025-08-14T02:19:20.964Z',
    },
    {
        zoomValue: 86,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.16279',
        },
        timestamp: '2025-08-14T02:19:18.880Z',
    },
    {
        zoomValue: 87,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.14943',
        },
        timestamp: '2025-08-14T02:19:21.075Z',
    },
    {
        zoomValue: 88,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.13636',
        },
        timestamp: '2025-08-14T02:19:18.858Z',
    },
    {
        zoomValue: 89,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.1236',
        },
        timestamp: '2025-08-14T02:19:21.156Z',
    },
    {
        zoomValue: 90,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.11111',
        },
        timestamp: '2025-08-14T02:19:18.833Z',
    },
    {
        zoomValue: 91,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.0989',
        },
        timestamp: '2025-08-14T02:19:21.220Z',
    },
    {
        zoomValue: 92,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.08696',
        },
        timestamp: '2025-08-14T02:19:21.317Z',
    },
    {
        zoomValue: 93,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.07527',
        },
        timestamp: '2025-08-14T02:19:21.329Z',
    },
    {
        zoomValue: 94,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.06383',
        },
        timestamp: '2025-08-14T02:19:21.351Z',
    },
    {
        zoomValue: 95,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.05263',
        },
        timestamp: '2025-08-14T02:19:18.795Z',
    },
    {
        zoomValue: 96,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.04167',
        },
        timestamp: '2025-08-14T02:19:21.427Z',
    },
    {
        zoomValue: 97,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.03093',
        },
        timestamp: '2025-08-14T02:19:21.443Z',
    },
    {
        zoomValue: 98,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.02041',
        },
        timestamp: '2025-08-14T02:19:21.486Z',
    },
    {
        zoomValue: 99,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1.0101',
        },
        timestamp: '2025-08-14T02:19:21.509Z',
    },
    {
        zoomValue: 100,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '1',
        },
        timestamp: '2025-08-14T02:18:11.719Z',
    },
    {
        zoomValue: 101,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.990099',
        },
        timestamp: '2025-08-14T02:19:18.769Z',
    },
    {
        zoomValue: 102,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.980392',
        },
        timestamp: '2025-08-14T02:19:21.636Z',
    },
    {
        zoomValue: 103,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.970874',
        },
        timestamp: '2025-08-14T02:19:21.666Z',
    },
    {
        zoomValue: 104,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.961538',
        },
        timestamp: '2025-08-14T02:19:21.690Z',
    },
    {
        zoomValue: 105,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.952381',
        },
        timestamp: '2025-08-14T02:19:21.712Z',
    },
    {
        zoomValue: 106,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.943396',
        },
        timestamp: '2025-08-14T02:19:21.719Z',
    },
    {
        zoomValue: 107,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.934579',
        },
        timestamp: '2025-08-14T02:19:21.742Z',
    },
    {
        zoomValue: 108,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.925926',
        },
        timestamp: '2025-08-14T02:19:21.763Z',
    },
    {
        zoomValue: 109,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.917431',
        },
        timestamp: '2025-08-14T02:19:21.799Z',
    },
    {
        zoomValue: 110,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.909091',
        },
        timestamp: '2025-08-14T02:19:21.834Z',
    },
    {
        zoomValue: 111,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.900901',
        },
        timestamp: '2025-08-14T02:19:21.846Z',
    },
    {
        zoomValue: 112,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.892857',
        },
        timestamp: '2025-08-14T02:19:21.865Z',
    },
    {
        zoomValue: 113,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.884956',
        },
        timestamp: '2025-08-14T02:19:21.898Z',
    },
    {
        zoomValue: 114,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.877193',
        },
        timestamp: '2025-08-14T02:19:21.927Z',
    },
    {
        zoomValue: 115,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.869565',
        },
        timestamp: '2025-08-14T02:19:21.949Z',
    },
    {
        zoomValue: 116,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.862069',
        },
        timestamp: '2025-08-14T02:19:21.967Z',
    },
    {
        zoomValue: 117,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.854701',
        },
        timestamp: '2025-08-14T02:19:21.984Z',
    },
    {
        zoomValue: 118,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.847458',
        },
        timestamp: '2025-08-14T02:19:21.997Z',
    },
    {
        zoomValue: 119,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.840336',
        },
        timestamp: '2025-08-14T02:19:22.043Z',
    },
    {
        zoomValue: 120,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.833333',
        },
        timestamp: '2025-08-14T02:19:22.052Z',
    },
    {
        zoomValue: 121,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.826446',
        },
        timestamp: '2025-08-14T02:19:22.075Z',
    },
    {
        zoomValue: 122,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.819672',
        },
        timestamp: '2025-08-14T02:19:22.114Z',
    },
    {
        zoomValue: 123,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.813008',
        },
        timestamp: '2025-08-14T02:19:22.129Z',
    },
    {
        zoomValue: 124,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.806452',
        },
        timestamp: '2025-08-14T02:19:22.176Z',
    },
    {
        zoomValue: 125,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.8',
        },
        timestamp: '2025-08-14T02:19:22.198Z',
    },
    {
        zoomValue: 126,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.793651',
        },
        timestamp: '2025-08-14T02:19:22.229Z',
    },
    {
        zoomValue: 127,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.787402',
        },
        timestamp: '2025-08-14T02:19:22.319Z',
    },
    {
        zoomValue: 128,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.78125',
        },
        timestamp: '2025-08-14T02:19:22.351Z',
    },
    {
        zoomValue: 129,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.775194',
        },
        timestamp: '2025-08-14T02:19:22.379Z',
    },
    {
        zoomValue: 130,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.769231',
        },
        timestamp: '2025-08-14T02:19:22.410Z',
    },
    {
        zoomValue: 131,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.763359',
        },
        timestamp: '2025-08-14T02:19:22.440Z',
    },
    {
        zoomValue: 132,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.757576',
        },
        timestamp: '2025-08-14T02:19:22.454Z',
    },
    {
        zoomValue: 133,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.75188',
        },
        timestamp: '2025-08-14T02:19:22.476Z',
    },
    {
        zoomValue: 134,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.746269',
        },
        timestamp: '2025-08-14T02:19:22.492Z',
    },
    {
        zoomValue: 135,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.740741',
        },
        timestamp: '2025-08-14T02:19:22.520Z',
    },
    {
        zoomValue: 136,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.735294',
        },
        timestamp: '2025-08-14T02:19:22.561Z',
    },
    {
        zoomValue: 137,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.729927',
        },
        timestamp: '2025-08-14T02:19:22.583Z',
    },
    {
        zoomValue: 138,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.724638',
        },
        timestamp: '2025-08-14T02:19:22.594Z',
    },
    {
        zoomValue: 139,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.719424',
        },
        timestamp: '2025-08-14T02:19:22.619Z',
    },
    {
        zoomValue: 140,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.714286',
        },
        timestamp: '2025-08-14T02:19:22.634Z',
    },
    {
        zoomValue: 141,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.70922',
        },
        timestamp: '2025-08-14T02:19:22.651Z',
    },
    {
        zoomValue: 142,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.704225',
        },
        timestamp: '2025-08-14T02:19:22.687Z',
    },
    {
        zoomValue: 143,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.699301',
        },
        timestamp: '2025-08-14T02:19:22.717Z',
    },
    {
        zoomValue: 144,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.694444',
        },
        timestamp: '2025-08-14T02:19:22.761Z',
    },
    {
        zoomValue: 145,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.689655',
        },
        timestamp: '2025-08-14T02:19:23.813Z',
    },
    {
        zoomValue: 146,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.684932',
        },
        timestamp: '2025-08-14T02:19:23.830Z',
    },
    {
        zoomValue: 147,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.680272',
        },
        timestamp: '2025-08-14T02:19:23.841Z',
    },
    {
        zoomValue: 148,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.675676',
        },
        timestamp: '2025-08-14T02:19:23.908Z',
    },
    {
        zoomValue: 149,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.671141',
        },
        timestamp: '2025-08-14T02:19:23.974Z',
    },
    {
        zoomValue: 150,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.666667',
        },
        timestamp: '2025-08-14T02:19:24.173Z',
    },
    {
        zoomValue: 151,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.662252',
        },
        timestamp: '2025-08-14T02:19:24.255Z',
    },
    {
        zoomValue: 152,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.657895',
        },
        timestamp: '2025-08-14T02:19:24.277Z',
    },
    {
        zoomValue: 153,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.653595',
        },
        timestamp: '2025-08-14T02:19:24.323Z',
    },
    {
        zoomValue: 154,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.649351',
        },
        timestamp: '2025-08-14T02:19:24.368Z',
    },
    {
        zoomValue: 155,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.645161',
        },
        timestamp: '2025-08-14T02:19:24.493Z',
    },
    {
        zoomValue: 156,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.641026',
        },
        timestamp: '2025-08-14T02:19:24.584Z',
    },
    {
        zoomValue: 157,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.636943',
        },
        timestamp: '2025-08-14T02:19:24.601Z',
    },
    {
        zoomValue: 158,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.632911',
        },
        timestamp: '2025-08-14T02:19:24.652Z',
    },
    {
        zoomValue: 159,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.628931',
        },
        timestamp: '2025-08-14T02:19:24.750Z',
    },
    {
        zoomValue: 160,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.625',
        },
        timestamp: '2025-08-14T02:19:24.986Z',
    },
    {
        zoomValue: 161,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.621118',
        },
        timestamp: '2025-08-14T02:19:24.995Z',
    },
    {
        zoomValue: 162,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.617284',
        },
        timestamp: '2025-08-14T02:19:25.013Z',
    },
    {
        zoomValue: 163,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.613497',
        },
        timestamp: '2025-08-14T02:19:25.038Z',
    },
    {
        zoomValue: 164,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.609756',
        },
        timestamp: '2025-08-14T02:19:25.069Z',
    },
    {
        zoomValue: 165,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.606061',
        },
        timestamp: '2025-08-14T02:19:25.090Z',
    },
    {
        zoomValue: 166,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.60241',
        },
        timestamp: '2025-08-14T02:19:25.096Z',
    },
    {
        zoomValue: 167,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.598802',
        },
        timestamp: '2025-08-14T02:19:25.156Z',
    },
    {
        zoomValue: 168,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.595238',
        },
        timestamp: '2025-08-14T02:19:25.186Z',
    },
    {
        zoomValue: 169,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.591716',
        },
        timestamp: '2025-08-14T02:19:25.237Z',
    },
    {
        zoomValue: 170,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.588235',
        },
        timestamp: '2025-08-14T02:19:25.282Z',
    },
    {
        zoomValue: 171,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.584795',
        },
        timestamp: '2025-08-14T02:19:25.336Z',
    },
    {
        zoomValue: 172,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.581395',
        },
        timestamp: '2025-08-14T02:19:25.379Z',
    },
    {
        zoomValue: 173,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.578035',
        },
        timestamp: '2025-08-14T02:19:25.418Z',
    },
    {
        zoomValue: 174,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.574713',
        },
        timestamp: '2025-08-14T02:19:25.462Z',
    },
    {
        zoomValue: 175,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.571429',
        },
        timestamp: '2025-08-14T02:19:25.485Z',
    },
    {
        zoomValue: 176,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.568182',
        },
        timestamp: '2025-08-14T02:19:25.634Z',
    },
    {
        zoomValue: 177,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.564972',
        },
        timestamp: '2025-08-14T02:19:25.665Z',
    },
    {
        zoomValue: 178,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.561798',
        },
        timestamp: '2025-08-14T02:19:25.702Z',
    },
    {
        zoomValue: 179,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.558659',
        },
        timestamp: '2025-08-14T02:19:25.717Z',
    },
    {
        zoomValue: 180,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.555556',
        },
        timestamp: '2025-08-14T02:19:25.875Z',
    },
    {
        zoomValue: 181,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.552486',
        },
        timestamp: '2025-08-14T02:19:25.938Z',
    },
    {
        zoomValue: 182,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.549451',
        },
        timestamp: '2025-08-14T02:19:26.005Z',
    },
    {
        zoomValue: 183,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.546448',
        },
        timestamp: '2025-08-14T02:19:26.063Z',
    },
    {
        zoomValue: 184,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.543478',
        },
        timestamp: '2025-08-14T02:19:26.091Z',
    },
    {
        zoomValue: 185,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.540541',
        },
        timestamp: '2025-08-14T02:19:26.189Z',
    },
    {
        zoomValue: 186,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.537634',
        },
        timestamp: '2025-08-14T02:19:26.363Z',
    },
    {
        zoomValue: 187,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.534759',
        },
        timestamp: '2025-08-14T02:19:26.394Z',
    },
    {
        zoomValue: 188,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.531915',
        },
        timestamp: '2025-08-14T02:19:26.432Z',
    },
    {
        zoomValue: 189,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.529101',
        },
        timestamp: '2025-08-14T02:19:26.511Z',
    },
    {
        zoomValue: 190,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.526316',
        },
        timestamp: '2025-08-14T02:19:26.556Z',
    },
    {
        zoomValue: 191,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.52356',
        },
        timestamp: '2025-08-14T02:19:26.609Z',
    },
    {
        zoomValue: 192,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.520833',
        },
        timestamp: '2025-08-14T02:19:26.634Z',
    },
    {
        zoomValue: 193,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.518135',
        },
        timestamp: '2025-08-14T02:19:26.653Z',
    },
    {
        zoomValue: 194,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.515464',
        },
        timestamp: '2025-08-14T02:19:26.706Z',
    },
    {
        zoomValue: 195,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.512821',
        },
        timestamp: '2025-08-14T02:19:26.851Z',
    },
    {
        zoomValue: 196,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.510204',
        },
        timestamp: '2025-08-14T02:19:26.886Z',
    },
    {
        zoomValue: 197,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.507614',
        },
        timestamp: '2025-08-14T02:19:26.922Z',
    },
    {
        zoomValue: 198,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.505051',
        },
        timestamp: '2025-08-14T02:19:26.957Z',
    },
    {
        zoomValue: 199,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.502513',
        },
        timestamp: '2025-08-14T02:19:26.992Z',
    },
    {
        zoomValue: 200,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.5',
        },
        timestamp: '2025-08-14T02:19:27.044Z',
    },
    {
        zoomValue: 201,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.497512',
        },
        timestamp: '2025-08-14T02:19:27.067Z',
    },
    {
        zoomValue: 202,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(63.4286px)',
            viewfinderDivScale: '0.49505',
        },
        timestamp: '2025-08-14T02:19:27.085Z',
    },
]
