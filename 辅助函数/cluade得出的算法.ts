/**
 * 缩放变换算法
 * 基于三组实际数据分析得出的精确算法
 * 
 * 核心原理：
 * 1. 缩放比例与zoomValue成反比：scale = 100 / zoomValue
 * 2. 位置变换基于缩放中心点：实际位置 = 缩放中心 + (初始位置 - 缩放中心) × scale
 */

// 常量定义
const ANCHOR_X = 85.7143
const ANCHOR_Y = 63.4286

export enum ZoomMode {
    FIXED_CENTER = 1,    // 模式1：固定中心点模式 (id=1)
    UPPER_ELEMENT = 2,   // 模式2：上方元素缩放模式 (id=3) 
    LOWER_ELEMENT = 3    // 模式3：下方元素缩放模式 (id=5)
}

export interface ZoomConfig {
    readonly zoomValue: number
    readonly initialY: number
    readonly anchorX?: number
    readonly anchorY?: number
}

export interface TransformResult {
    readonly dragPadTransform: string
    readonly viewfinderDivScale: string
}

// 三种模式的配置常量
export const ZOOM_MODE_CONFIGS = {
    [ZoomMode.FIXED_CENTER]: {
        initialY: ANCHOR_Y,
        description: '固定中心点模式 - Y坐标不随缩放变化'
    },
    [ZoomMode.UPPER_ELEMENT]: {
        initialY: 124.32,
        description: '上方元素缩放模式 - 元素位于缩放中心上方'
    },
    [ZoomMode.LOWER_ELEMENT]: {
        initialY: 2.537,
        description: '下方元素缩放模式 - 元素位于缩放中心下方'
    }
} as const

/**
 * 计算缩放比例
 * @param zoomValue 缩放值 (75-202)
 * @returns 缩放比例
 */
const calculateScale = (zoomValue: number): number => 100 / zoomValue

/**
 * 生成变换字符串
 * @param x X坐标
 * @param y Y坐标
 * @returns 变换字符串
 */
const createTransform = (x: number, y: number): string => 
    `translateX(${x}px) translateY(${y}px)`

/**
 * 通用缩放变换计算函数
 * 算法推导：Y = anchorY + (initialY - anchorY) × scale
 */
export function calculateZoomTransform(config: ZoomConfig): TransformResult {
    const { 
        zoomValue, 
        initialY, 
        anchorX = ANCHOR_X, 
        anchorY = ANCHOR_Y 
    } = config

    const scale = calculateScale(zoomValue)
    const x = anchorX
    const y = anchorY + (initialY - anchorY) * scale

    return {
        dragPadTransform: createTransform(x, y),
        viewfinderDivScale: scale.toString()
    }
}

/**
 * 模式1：固定中心点缩放算法 (id=1)
 * 特点：Y坐标始终保持在63.4286px，不随缩放变化
 */
export function calculateMode1Transform(zoomValue: number): TransformResult {
    const scale = calculateScale(zoomValue)
    
    return {
        dragPadTransform: createTransform(ANCHOR_X, ANCHOR_Y),
        viewfinderDivScale: scale.toString()
    }
}

/**
 * 模式2：上方元素缩放算法 (id=3)
 * 特点：元素初始位置在缩放中心上方，随缩放向中心收敛或远离
 */
export function calculateMode2Transform(zoomValue: number): TransformResult {
    return calculateZoomTransform({
        zoomValue,
        initialY: ZOOM_MODE_CONFIGS[ZoomMode.UPPER_ELEMENT].initialY
    })
}

/**
 * 模式3：下方元素缩放算法 (id=5)
 * 特点：元素初始位置在缩放中心下方，随缩放向中心收敛或远离
 */
export function calculateMode3Transform(zoomValue: number): TransformResult {
    return calculateZoomTransform({
        zoomValue,
        initialY: ZOOM_MODE_CONFIGS[ZoomMode.LOWER_ELEMENT].initialY
    })
}

/**
 * 根据模式ID自动选择对应的缩放算法
 */
export function calculateByMode(mode: ZoomMode | number, zoomValue: number): TransformResult {
    switch (mode) {
        case 1:
        case ZoomMode.FIXED_CENTER:
            return calculateMode1Transform(zoomValue)
        case 3:
        case ZoomMode.UPPER_ELEMENT:
            return calculateMode2Transform(zoomValue)
        case 5:
        case ZoomMode.LOWER_ELEMENT:
            return calculateMode3Transform(zoomValue)
        default:
            throw new Error(`无效的缩放模式: ${mode}。支持的模式: 1(固定中心), 3(上方元素), 5(下方元素)`)
    }
}

/**
 * 根据实际ID值选择缩放模式
 */
export function calculateById(id: 1 | 3 | 5, zoomValue: number): TransformResult {
    return calculateByMode(id, zoomValue)
}

/**
 * 批量计算多个zoom值的变换结果
 */
export function calculateBatch(
    mode: ZoomMode | number, 
    zoomValues: number[]
): Array<{ zoomValue: number } & TransformResult> {
    return zoomValues.map(zoomValue => ({
        zoomValue,
        ...calculateByMode(mode, zoomValue)
    }))
}

/**
 * 反向计算：从当前状态推算初始Y位置
 */
export function calculateInitialY(
    currentY: number,
    zoomValue: number,
    anchorY = ANCHOR_Y
): number {
    const scale = calculateScale(zoomValue)
    return anchorY + (currentY - anchorY) / scale
}

// 使用示例
export const examples = {
    mode1: (zoom: number) => calculateMode1Transform(zoom),
    mode2: (zoom: number) => calculateMode2Transform(zoom), 
    mode3: (zoom: number) => calculateMode3Transform(zoom),
    byId1: (zoom: number) => calculateById(1, zoom),
    byId3: (zoom: number) => calculateById(3, zoom),
    byId5: (zoom: number) => calculateById(5, zoom)
} as const