// 初始化全局变量 保存监听到的数据
// 根据需求文档 使用 window.wathDat (保持原始拼写)
window.wathDat = []

// 监听滑块变化的主函数
function initSliderWatcher() {
    // 查找滑块容器 注意CSS选择器中间有空格
    const sliderContainer =
        document.querySelector('.slider-component.undefined-disabled.hide-rail') ||
        document.querySelector('.slider-component.false-disabled.hide-rail')

    if (!sliderContainer) {
        // console.warn('未找到滑块容器 将在DOM变化时重试')
        return
    }

    // 查找隐藏的input元素
    const hiddenInput = sliderContainer.querySelector('input[style*="display: none"]')

    if (!hiddenInput) {
        // console.warn('未找到隐藏的input元素')
        return
    }

    // 提取样式值的函数
    function getStyleValues() {
        // 获取 drag-pad 的 transform 值
        const dragPad = document.querySelector('.drag-pad')
        let dragPadTransform = ''
        if (dragPad) {
            const dragHandle = dragPad.querySelector('.drag-handle')
            if (dragHandle) {
                dragPadTransform = dragHandle.style.transform || ''
            }
        }

        // 获取 viewfinder-div 的 scale 值
        let viewfinderDivScale = ''
        const viewfinderDiv = document.querySelector('.viewfinder-div.default-viewfinder')
        if (viewfinderDiv) {
            viewfinderDivScale = viewfinderDiv.style.scale || ''
        }

        return {
            dragPadTransform,
            viewfinderDivScale,
        }
    }

    // 保存数据到全局变量
    function saveWatchData(value, styleValue) {
        const dataEntry = {
            zoomValue: parseInt(value),
            styleValue: styleValue,
            timestamp: new Date().toISOString(), // 添加时间戳便于调试
        }

        // 检测zoomValue是否重复 如果重复则不push
        const isDuplicate = window.wathDat.some(item => item.zoomValue === dataEntry.zoomValue)

        if (!isDuplicate) {
            window.wathDat.push(dataEntry)

            // console.log('保存数据:', dataEntry)
        } else {
            // console.log('检测到重复zoomValue值，跳过保存:', dataEntry.zoomValue)
        }
    }

    // 监听input值变化
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                const currentValue = hiddenInput.value
                const styleValue = getStyleValues()

                // 保存当前状态
                saveWatchData(currentValue, styleValue)
            }
        })
    })

    // 开始监听input的value属性变化
    observer.observe(hiddenInput, {
        attributes: true,
        attributeFilter: ['value'],
    })

    // 同时监听input的change和input事件 作为备用方案
    hiddenInput.addEventListener('input', function () {
        const currentValue = this.value
        const styleValue = getStyleValues()
        saveWatchData(currentValue, styleValue)
    })

    hiddenInput.addEventListener('change', function () {
        const currentValue = this.value
        const styleValue = getStyleValues()
        saveWatchData(currentValue, styleValue)
    })

    // console.log('滑块监听器已初始化')

    // 保存初始状态
    const initialValue = hiddenInput.value
    const initialStyleValue = getStyleValues()
    saveWatchData(initialValue, initialStyleValue)
}

// DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSliderWatcher)
} else {
    // 如果DOM已经加载完成 直接初始化
    initSliderWatcher()
}

// 如果元素还没有渲染 使用MutationObserver等待
const documentObserver = new MutationObserver(function (mutations) {
    // 检查是否有我们需要的元素被添加
    const sliderExists = document.querySelector('.slider-component.undefined-disabled.hide-rail')
    if (sliderExists && window.wathDat.length === 0) {
        initSliderWatcher()
        documentObserver.disconnect() // 停止观察
    }
})

// 观察整个document的子节点变化
documentObserver.observe(document.body, {
    childList: true,
    subtree: true,
})

// 提供查看数据的辅助函数
window.getWatchData = function () {
    // 按照 zoomValue 排序后返回数据
    const sortedData = [...window.wathDat].sort((a, b) => a.zoomValue - b.zoomValue)
    // console.log('当前监听数据:', sortedData)
    copy(sortedData)
    return sortedData
}

// 提供清空数据的辅助函数
window.clearWatchData = function () {
    window.wathDat = []

    // console.log('监听数据已清空')
}

// console.log('辅助函数脚本已加载')
