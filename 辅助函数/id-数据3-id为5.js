data = [
    {
        zoomValue: 75,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-17.76px)',
            viewfinderDivScale: '1.33333',
        },
        timestamp: '2025-08-14T02:27:58.524Z',
    },
    {
        zoomValue: 76,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-16.6917px)',
            viewfinderDivScale: '1.31579',
        },
        timestamp: '2025-08-14T02:27:58.514Z',
    },
    {
        zoomValue: 77,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-15.6512px)',
            viewfinderDivScale: '1.2987',
        },
        timestamp: '2025-08-14T02:27:58.492Z',
    },
    {
        zoomValue: 78,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-14.6374px)',
            viewfinderDivScale: '1.28205',
        },
        timestamp: '2025-08-14T02:27:58.409Z',
    },
    {
        zoomValue: 79,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-13.6492px)',
            viewfinderDivScale: '1.26582',
        },
        timestamp: '2025-08-14T02:27:58.371Z',
    },
    {
        zoomValue: 80,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-12.6857px)',
            viewfinderDivScale: '1.25',
        },
        timestamp: '2025-08-14T02:27:58.288Z',
    },
    {
        zoomValue: 81,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-11.746px)',
            viewfinderDivScale: '1.23457',
        },
        timestamp: '2025-08-14T02:27:58.246Z',
    },
    {
        zoomValue: 82,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-10.8293px)',
            viewfinderDivScale: '1.21951',
        },
        timestamp: '2025-08-14T02:27:58.198Z',
    },
    {
        zoomValue: 83,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-9.9346px)',
            viewfinderDivScale: '1.20482',
        },
        timestamp: '2025-08-14T02:27:58.170Z',
    },
    {
        zoomValue: 84,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-9.06122px)',
            viewfinderDivScale: '1.19048',
        },
        timestamp: '2025-08-14T02:27:58.153Z',
    },
    {
        zoomValue: 85,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-8.2084px)',
            viewfinderDivScale: '1.17647',
        },
        timestamp: '2025-08-14T02:27:58.130Z',
    },
    {
        zoomValue: 86,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-7.37542px)',
            viewfinderDivScale: '1.16279',
        },
        timestamp: '2025-08-14T02:27:58.122Z',
    },
    {
        zoomValue: 87,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-6.56158px)',
            viewfinderDivScale: '1.14943',
        },
        timestamp: '2025-08-14T02:27:58.108Z',
    },
    {
        zoomValue: 88,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-5.76623px)',
            viewfinderDivScale: '1.13636',
        },
        timestamp: '2025-08-14T02:27:58.095Z',
    },
    {
        zoomValue: 89,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-4.98876px)',
            viewfinderDivScale: '1.1236',
        },
        timestamp: '2025-08-14T02:27:58.079Z',
    },
    {
        zoomValue: 90,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-4.22857px)',
            viewfinderDivScale: '1.11111',
        },
        timestamp: '2025-08-14T02:27:58.070Z',
    },
    {
        zoomValue: 91,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-3.48509px)',
            viewfinderDivScale: '1.0989',
        },
        timestamp: '2025-08-14T02:27:58.057Z',
    },
    {
        zoomValue: 92,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-2.75776px)',
            viewfinderDivScale: '1.08696',
        },
        timestamp: '2025-08-14T02:27:59.788Z',
    },
    {
        zoomValue: 93,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-2.04608px)',
            viewfinderDivScale: '1.07527',
        },
        timestamp: '2025-08-14T02:27:58.034Z',
    },
    {
        zoomValue: 94,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-1.34954px)',
            viewfinderDivScale: '1.06383',
        },
        timestamp: '2025-08-14T02:27:58.020Z',
    },
    {
        zoomValue: 95,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(-0.667669px)',
            viewfinderDivScale: '1.05263',
        },
        timestamp: '2025-08-14T02:27:58.007Z',
    },
    {
        zoomValue: 96,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(0px)',
            viewfinderDivScale: '1.04167',
        },
        timestamp: '2025-08-14T02:27:57.995Z',
    },
    {
        zoomValue: 97,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(0.653903px)',
            viewfinderDivScale: '1.03093',
        },
        timestamp: '2025-08-14T02:27:57.981Z',
    },
    {
        zoomValue: 98,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(1.29446px)',
            viewfinderDivScale: '1.02041',
        },
        timestamp: '2025-08-14T02:27:57.966Z',
    },
    {
        zoomValue: 99,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(1.92208px)',
            viewfinderDivScale: '1.0101',
        },
        timestamp: '2025-08-14T02:27:57.953Z',
    },
    {
        zoomValue: 100,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(2.53714px)',
            viewfinderDivScale: '1',
        },
        timestamp: '2025-08-14T02:27:57.939Z',
    },
    {
        zoomValue: 101,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(3.14003px)',
            viewfinderDivScale: '0.990099',
        },
        timestamp: '2025-08-14T02:27:57.929Z',
    },
    {
        zoomValue: 102,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(3.73109px)',
            viewfinderDivScale: '0.980392',
        },
        timestamp: '2025-08-14T02:27:57.913Z',
    },
    {
        zoomValue: 103,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(4.31068px)',
            viewfinderDivScale: '0.970874',
        },
        timestamp: '2025-08-14T02:27:57.898Z',
    },
    {
        zoomValue: 104,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(4.87912px)',
            viewfinderDivScale: '0.961538',
        },
        timestamp: '2025-08-14T02:27:57.883Z',
    },
    {
        zoomValue: 105,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(5.43673px)',
            viewfinderDivScale: '0.952381',
        },
        timestamp: '2025-08-14T02:27:57.869Z',
    },
    {
        zoomValue: 106,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(5.98383px)',
            viewfinderDivScale: '0.943396',
        },
        timestamp: '2025-08-14T02:27:57.854Z',
    },
    {
        zoomValue: 107,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(6.52069px)',
            viewfinderDivScale: '0.934579',
        },
        timestamp: '2025-08-14T02:27:57.840Z',
    },
    {
        zoomValue: 108,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(7.04762px)',
            viewfinderDivScale: '0.925926',
        },
        timestamp: '2025-08-14T02:27:57.828Z',
    },
    {
        zoomValue: 109,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(7.56488px)',
            viewfinderDivScale: '0.917431',
        },
        timestamp: '2025-08-14T02:27:57.814Z',
    },
    {
        zoomValue: 110,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(8.07273px)',
            viewfinderDivScale: '0.909091',
        },
        timestamp: '2025-08-14T02:27:57.806Z',
    },
    {
        zoomValue: 111,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(8.57143px)',
            viewfinderDivScale: '0.900901',
        },
        timestamp: '2025-08-14T02:27:57.795Z',
    },
    {
        zoomValue: 112,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(9.06122px)',
            viewfinderDivScale: '0.892857',
        },
        timestamp: '2025-08-14T02:27:57.779Z',
    },
    {
        zoomValue: 113,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(9.54235px)',
            viewfinderDivScale: '0.884956',
        },
        timestamp: '2025-08-14T02:27:57.756Z',
    },
    {
        zoomValue: 114,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(10.015px)',
            viewfinderDivScale: '0.877193',
        },
        timestamp: '2025-08-14T02:27:57.730Z',
    },
    {
        zoomValue: 115,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(10.4795px)',
            viewfinderDivScale: '0.869565',
        },
        timestamp: '2025-08-14T02:27:57.719Z',
    },
    {
        zoomValue: 116,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(10.936px)',
            viewfinderDivScale: '0.862069',
        },
        timestamp: '2025-08-14T02:28:00.262Z',
    },
    {
        zoomValue: 117,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(11.3846px)',
            viewfinderDivScale: '0.854701',
        },
        timestamp: '2025-08-14T02:27:57.689Z',
    },
    {
        zoomValue: 118,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(11.8257px)',
            viewfinderDivScale: '0.847458',
        },
        timestamp: '2025-08-14T02:27:57.676Z',
    },
    {
        zoomValue: 119,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(12.2593px)',
            viewfinderDivScale: '0.840336',
        },
        timestamp: '2025-08-14T02:27:57.663Z',
    },
    {
        zoomValue: 120,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(12.6857px)',
            viewfinderDivScale: '0.833333',
        },
        timestamp: '2025-08-14T02:27:57.647Z',
    },
    {
        zoomValue: 121,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(13.1051px)',
            viewfinderDivScale: '0.826446',
        },
        timestamp: '2025-08-14T02:27:57.639Z',
    },
    {
        zoomValue: 122,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(13.5176px)',
            viewfinderDivScale: '0.819672',
        },
        timestamp: '2025-08-14T02:27:57.629Z',
    },
    {
        zoomValue: 123,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(13.9233px)',
            viewfinderDivScale: '0.813008',
        },
        timestamp: '2025-08-14T02:27:57.605Z',
    },
    {
        zoomValue: 124,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(14.3226px)',
            viewfinderDivScale: '0.806452',
        },
        timestamp: '2025-08-14T02:27:57.583Z',
    },
    {
        zoomValue: 125,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(14.7154px)',
            viewfinderDivScale: '0.8',
        },
        timestamp: '2025-08-14T02:27:57.573Z',
    },
    {
        zoomValue: 126,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(15.102px)',
            viewfinderDivScale: '0.793651',
        },
        timestamp: '2025-08-14T02:27:57.563Z',
    },
    {
        zoomValue: 127,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(15.4826px)',
            viewfinderDivScale: '0.787402',
        },
        timestamp: '2025-08-14T02:27:57.546Z',
    },
    {
        zoomValue: 128,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(15.8571px)',
            viewfinderDivScale: '0.78125',
        },
        timestamp: '2025-08-14T02:27:57.523Z',
    },
    {
        zoomValue: 129,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(16.2259px)',
            viewfinderDivScale: '0.775194',
        },
        timestamp: '2025-08-14T02:27:57.501Z',
    },
    {
        zoomValue: 130,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(16.589px)',
            viewfinderDivScale: '0.769231',
        },
        timestamp: '2025-08-14T02:27:57.479Z',
    },
    {
        zoomValue: 131,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(16.9466px)',
            viewfinderDivScale: '0.763359',
        },
        timestamp: '2025-08-14T02:27:57.463Z',
    },
    {
        zoomValue: 132,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(17.2987px)',
            viewfinderDivScale: '0.757576',
        },
        timestamp: '2025-08-14T02:27:57.440Z',
    },
    {
        zoomValue: 133,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(17.6455px)',
            viewfinderDivScale: '0.75188',
        },
        timestamp: '2025-08-14T02:27:57.426Z',
    },
    {
        zoomValue: 134,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(17.9872px)',
            viewfinderDivScale: '0.746269',
        },
        timestamp: '2025-08-14T02:27:57.412Z',
    },
    {
        zoomValue: 135,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(18.3238px)',
            viewfinderDivScale: '0.740741',
        },
        timestamp: '2025-08-14T02:27:57.394Z',
    },
    {
        zoomValue: 136,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(18.6555px)',
            viewfinderDivScale: '0.735294',
        },
        timestamp: '2025-08-14T02:27:57.384Z',
    },
    {
        zoomValue: 137,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(18.9823px)',
            viewfinderDivScale: '0.729927',
        },
        timestamp: '2025-08-14T02:27:57.360Z',
    },
    {
        zoomValue: 138,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(19.3043px)',
            viewfinderDivScale: '0.724638',
        },
        timestamp: '2025-08-14T02:27:57.346Z',
    },
    {
        zoomValue: 139,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(19.6218px)',
            viewfinderDivScale: '0.719424',
        },
        timestamp: '2025-08-14T02:27:57.321Z',
    },
    {
        zoomValue: 140,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(19.9347px)',
            viewfinderDivScale: '0.714286',
        },
        timestamp: '2025-08-14T02:27:57.297Z',
    },
    {
        zoomValue: 141,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(20.2432px)',
            viewfinderDivScale: '0.70922',
        },
        timestamp: '2025-08-14T02:27:57.290Z',
    },
    {
        zoomValue: 142,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(20.5473px)',
            viewfinderDivScale: '0.704225',
        },
        timestamp: '2025-08-14T02:27:57.277Z',
    },
    {
        zoomValue: 143,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(20.8472px)',
            viewfinderDivScale: '0.699301',
        },
        timestamp: '2025-08-14T02:27:57.269Z',
    },
    {
        zoomValue: 144,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(21.1429px)',
            viewfinderDivScale: '0.694444',
        },
        timestamp: '2025-08-14T02:27:57.253Z',
    },
    {
        zoomValue: 145,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(21.4345px)',
            viewfinderDivScale: '0.689655',
        },
        timestamp: '2025-08-14T02:27:57.233Z',
    },
    {
        zoomValue: 146,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(21.7221px)',
            viewfinderDivScale: '0.684932',
        },
        timestamp: '2025-08-14T02:27:57.226Z',
    },
    {
        zoomValue: 147,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(22.0058px)',
            viewfinderDivScale: '0.680272',
        },
        timestamp: '2025-08-14T02:27:57.217Z',
    },
    {
        zoomValue: 148,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(22.2857px)',
            viewfinderDivScale: '0.675676',
        },
        timestamp: '2025-08-14T02:27:57.201Z',
    },
    {
        zoomValue: 149,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(22.5618px)',
            viewfinderDivScale: '0.671141',
        },
        timestamp: '2025-08-14T02:27:57.184Z',
    },
    {
        zoomValue: 150,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(22.8343px)',
            viewfinderDivScale: '0.666667',
        },
        timestamp: '2025-08-14T02:27:57.165Z',
    },
    {
        zoomValue: 151,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(23.1031px)',
            viewfinderDivScale: '0.662252',
        },
        timestamp: '2025-08-14T02:27:57.156Z',
    },
    {
        zoomValue: 152,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(23.3684px)',
            viewfinderDivScale: '0.657895',
        },
        timestamp: '2025-08-14T02:27:57.133Z',
    },
    {
        zoomValue: 153,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(23.6303px)',
            viewfinderDivScale: '0.653595',
        },
        timestamp: '2025-08-14T02:27:57.117Z',
    },
    {
        zoomValue: 154,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(23.8887px)',
            viewfinderDivScale: '0.649351',
        },
        timestamp: '2025-08-14T02:27:57.096Z',
    },
    {
        zoomValue: 155,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(24.1438px)',
            viewfinderDivScale: '0.645161',
        },
        timestamp: '2025-08-14T02:27:57.082Z',
    },
    {
        zoomValue: 156,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(24.3956px)',
            viewfinderDivScale: '0.641026',
        },
        timestamp: '2025-08-14T02:28:01.145Z',
    },
    {
        zoomValue: 157,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(24.6442px)',
            viewfinderDivScale: '0.636943',
        },
        timestamp: '2025-08-14T02:27:57.074Z',
    },
    {
        zoomValue: 158,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(24.8897px)',
            viewfinderDivScale: '0.632911',
        },
        timestamp: '2025-08-14T02:27:57.062Z',
    },
    {
        zoomValue: 159,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(25.1321px)',
            viewfinderDivScale: '0.628931',
        },
        timestamp: '2025-08-14T02:27:57.050Z',
    },
    {
        zoomValue: 160,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(25.3714px)',
            viewfinderDivScale: '0.625',
        },
        timestamp: '2025-08-14T02:27:57.039Z',
    },
    {
        zoomValue: 161,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(25.6078px)',
            viewfinderDivScale: '0.621118',
        },
        timestamp: '2025-08-14T02:28:01.250Z',
    },
    {
        zoomValue: 162,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(25.8413px)',
            viewfinderDivScale: '0.617284',
        },
        timestamp: '2025-08-14T02:28:01.273Z',
    },
    {
        zoomValue: 163,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(26.0719px)',
            viewfinderDivScale: '0.613497',
        },
        timestamp: '2025-08-14T02:27:52.266Z',
    },
    {
        zoomValue: 164,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(26.2997px)',
            viewfinderDivScale: '0.609756',
        },
        timestamp: '2025-08-14T02:27:52.251Z',
    },
    {
        zoomValue: 165,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(26.5247px)',
            viewfinderDivScale: '0.606061',
        },
        timestamp: '2025-08-14T02:27:52.229Z',
    },
    {
        zoomValue: 166,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(26.747px)',
            viewfinderDivScale: '0.60241',
        },
        timestamp: '2025-08-14T02:27:52.206Z',
    },
    {
        zoomValue: 167,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(26.9666px)',
            viewfinderDivScale: '0.598802',
        },
        timestamp: '2025-08-14T02:27:52.154Z',
    },
    {
        zoomValue: 168,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(27.1837px)',
            viewfinderDivScale: '0.595238',
        },
        timestamp: '2025-08-14T02:27:52.108Z',
    },
    {
        zoomValue: 169,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(27.3981px)',
            viewfinderDivScale: '0.591716',
        },
        timestamp: '2025-08-14T02:27:52.056Z',
    },
    {
        zoomValue: 170,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(27.6101px)',
            viewfinderDivScale: '0.588235',
        },
        timestamp: '2025-08-14T02:27:52.018Z',
    },
    {
        zoomValue: 171,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(27.8195px)',
            viewfinderDivScale: '0.584795',
        },
        timestamp: '2025-08-14T02:27:51.930Z',
    },
    {
        zoomValue: 172,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(28.0266px)',
            viewfinderDivScale: '0.581395',
        },
        timestamp: '2025-08-14T02:27:51.831Z',
    },
    {
        zoomValue: 173,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(28.2312px)',
            viewfinderDivScale: '0.578035',
        },
        timestamp: '2025-08-14T02:27:51.771Z',
    },
    {
        zoomValue: 174,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(28.4335px)',
            viewfinderDivScale: '0.574713',
        },
        timestamp: '2025-08-14T02:27:51.660Z',
    },
    {
        zoomValue: 175,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(28.6335px)',
            viewfinderDivScale: '0.571429',
        },
        timestamp: '2025-08-14T02:27:51.637Z',
    },
    {
        zoomValue: 176,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(28.8312px)',
            viewfinderDivScale: '0.568182',
        },
        timestamp: '2025-08-14T02:27:51.618Z',
    },
    {
        zoomValue: 177,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.0266px)',
            viewfinderDivScale: '0.564972',
        },
        timestamp: '2025-08-14T02:27:51.586Z',
    },
    {
        zoomValue: 178,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.2199px)',
            viewfinderDivScale: '0.561798',
        },
        timestamp: '2025-08-14T02:27:51.539Z',
    },
    {
        zoomValue: 179,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.411px)',
            viewfinderDivScale: '0.558659',
        },
        timestamp: '2025-08-14T02:27:51.477Z',
    },
    {
        zoomValue: 180,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.6px)',
            viewfinderDivScale: '0.555556',
        },
        timestamp: '2025-08-14T02:27:49.931Z',
    },
    {
        zoomValue: 181,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.7869px)',
            viewfinderDivScale: '0.552486',
        },
        timestamp: '2025-08-14T02:28:01.875Z',
    },
    {
        zoomValue: 182,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(29.9717px)',
            viewfinderDivScale: '0.549451',
        },
        timestamp: '2025-08-14T02:28:01.890Z',
    },
    {
        zoomValue: 183,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(30.1546px)',
            viewfinderDivScale: '0.546448',
        },
        timestamp: '2025-08-14T02:28:01.915Z',
    },
    {
        zoomValue: 184,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(30.3354px)',
            viewfinderDivScale: '0.543478',
        },
        timestamp: '2025-08-14T02:28:01.987Z',
    },
    {
        zoomValue: 185,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(30.5143px)',
            viewfinderDivScale: '0.540541',
        },
        timestamp: '2025-08-14T02:28:02.013Z',
    },
    {
        zoomValue: 186,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(30.6912px)',
            viewfinderDivScale: '0.537634',
        },
        timestamp: '2025-08-14T02:28:02.030Z',
    },
    {
        zoomValue: 187,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(30.8663px)',
            viewfinderDivScale: '0.534759',
        },
        timestamp: '2025-08-14T02:28:02.059Z',
    },
    {
        zoomValue: 188,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.0395px)',
            viewfinderDivScale: '0.531915',
        },
        timestamp: '2025-08-14T02:28:02.084Z',
    },
    {
        zoomValue: 189,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.2109px)',
            viewfinderDivScale: '0.529101',
        },
        timestamp: '2025-08-14T02:28:02.180Z',
    },
    {
        zoomValue: 190,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.3805px)',
            viewfinderDivScale: '0.526316',
        },
        timestamp: '2025-08-14T02:28:02.419Z',
    },
    {
        zoomValue: 191,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.5482px)',
            viewfinderDivScale: '0.52356',
        },
        timestamp: '2025-08-14T02:28:02.429Z',
    },
    {
        zoomValue: 192,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.7143px)',
            viewfinderDivScale: '0.520833',
        },
        timestamp: '2025-08-14T02:28:02.439Z',
    },
    {
        zoomValue: 193,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(31.8786px)',
            viewfinderDivScale: '0.518135',
        },
        timestamp: '2025-08-14T02:28:02.454Z',
    },
    {
        zoomValue: 194,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.0412px)',
            viewfinderDivScale: '0.515464',
        },
        timestamp: '2025-08-14T02:28:02.465Z',
    },
    {
        zoomValue: 195,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.2022px)',
            viewfinderDivScale: '0.512821',
        },
        timestamp: '2025-08-14T02:28:02.481Z',
    },
    {
        zoomValue: 196,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.3615px)',
            viewfinderDivScale: '0.510204',
        },
        timestamp: '2025-08-14T02:28:02.496Z',
    },
    {
        zoomValue: 197,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.5192px)',
            viewfinderDivScale: '0.507614',
        },
        timestamp: '2025-08-14T02:28:02.548Z',
    },
    {
        zoomValue: 198,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.6753px)',
            viewfinderDivScale: '0.505051',
        },
        timestamp: '2025-08-14T02:28:02.862Z',
    },
    {
        zoomValue: 199,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.8299px)',
            viewfinderDivScale: '0.502513',
        },
        timestamp: '2025-08-14T02:28:02.880Z',
    },
    {
        zoomValue: 200,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(32.9829px)',
            viewfinderDivScale: '0.5',
        },
        timestamp: '2025-08-14T02:28:02.896Z',
    },
    {
        zoomValue: 201,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(33.1343px)',
            viewfinderDivScale: '0.497512',
        },
        timestamp: '2025-08-14T02:28:02.916Z',
    },
    {
        zoomValue: 202,
        styleValue: {
            dragPadTransform: 'translateX(85.7143px) translateY(33.2843px)',
            viewfinderDivScale: '0.49505',
        },
        timestamp: '2025-08-14T02:28:02.929Z',
    },
]
