# =============================================================================
# Docker 环境变量模板
# =============================================================================
# 用途：Docker 容器运行时的环境变量配置
# 使用：复制此文件为 .env.local（本地）或 .env.production（生产）
# =============================================================================

# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
# 运行环境：development | production
NODE_ENV=development

# 应用端口
PORT=3000

# 主机名（0.0.0.0 允许外部访问）
HOSTNAME=0.0.0.0

# -----------------------------------------------------------------------------
# Next.js 配置
# -----------------------------------------------------------------------------
# 禁用遥测数据收集
NEXT_TELEMETRY_DISABLED=1

# 公开的应用 URL（用于生成绝对路径）
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 站点名称
NEXT_PUBLIC_SITE_NAME=Next Wallpaper

# -----------------------------------------------------------------------------
# Docker 特定配置
# -----------------------------------------------------------------------------
# 容器名称
CONTAINER_NAME=next-wallpaper

# 镜像标签
IMAGE_TAG=latest

# 时区设置
TZ=Asia/Shanghai

# -----------------------------------------------------------------------------
# 开发环境配置（仅在 NODE_ENV=development 时生效）
# -----------------------------------------------------------------------------
# 启用文件监视（Docker 环境需要）
WATCHPACK_POLLING=true
CHOKIDAR_USEPOLLING=true

# 调试端口（需要时取消注释）
# DEBUG_PORT=9229

# -----------------------------------------------------------------------------
# 生产环境配置（仅在 NODE_ENV=production 时生效）
# -----------------------------------------------------------------------------
# 域名配置（生产环境）
# DOMAIN=www.example.com
# ALT_DOMAIN=example.com

# SSL/HTTPS 配置
# HTTPS_ENABLED=true
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# -----------------------------------------------------------------------------
# 资源限制（可选）
# -----------------------------------------------------------------------------
# CPU 限制（核数）
CPU_LIMIT=2

# 内存限制
MEMORY_LIMIT=2G

# CPU 预留
CPU_RESERVE=1

# 内存预留
MEMORY_RESERVE=1G

# -----------------------------------------------------------------------------
# 日志配置
# -----------------------------------------------------------------------------
# 日志级别：error | warn | info | debug
LOG_LEVEL=info

# 单个日志文件最大大小
LOG_MAX_SIZE=10m

# 保留的日志文件数量
LOG_MAX_FILES=3

# -----------------------------------------------------------------------------
# 数据存储路径（用于数据持久化）
# -----------------------------------------------------------------------------
# 数据目录
DATA_PATH=./data

# 上传文件存储路径
UPLOAD_PATH=./data/uploads

# 日志存储路径
LOGS_PATH=./data/logs

# -----------------------------------------------------------------------------
# 网络配置（国内用户）
# -----------------------------------------------------------------------------
# 是否使用国内镜像加速（构建时）
# USE_CHINA_MIRROR=true

# NPM 镜像源
# NPM_REGISTRY=https://registry.npmmirror.com

# -----------------------------------------------------------------------------
# 安全配置（生产环境必须设置）
# -----------------------------------------------------------------------------
# 会话密钥（生产环境必须修改）
# SESSION_SECRET=your-secret-key-here

# API 密钥（如果需要）
# API_KEY=your-api-key-here

# CORS 允许的域名
# CORS_ORIGIN=https://www.example.com

# -----------------------------------------------------------------------------
# 监控和健康检查
# -----------------------------------------------------------------------------
# 健康检查 URL
HEALTH_CHECK_URL=/api/health

# 健康检查间隔（秒）
HEALTH_CHECK_INTERVAL=30

# -----------------------------------------------------------------------------
# 自定义配置
# -----------------------------------------------------------------------------
# 在此添加你的自定义环境变量
# CUSTOM_VAR=value

# =============================================================================
# 注意事项：
# =============================================================================
# 1. 不要将包含敏感信息的 .env 文件提交到版本控制
# 2. 生产环境的密钥必须使用强随机字符串
# 3. 根据实际需求调整资源限制
# 4. 确保路径配置正确，特别是在生产环境
# =============================================================================