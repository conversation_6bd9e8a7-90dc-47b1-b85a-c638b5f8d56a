# =============================================================================
# Next.js 多阶段构建 Dockerfile
# =============================================================================
# 说明：
# - 使用多阶段构建优化镜像大小
# - 基础镜像：Node.js 20.18.3 Alpine (轻量级 Linux)
# - 包管理器：pnpm v9 (性能更好，节省磁盘空间)
# - 构建目标：开发环境 (development) 和生产环境 (runner)
# =============================================================================

# -----------------------------------------------------------------------------
# 阶段 1: 依赖安装基础层
# -----------------------------------------------------------------------------
# 用途：仅安装生产依赖，为生产镜像准备最小依赖集
# 大小：约 200MB
# -----------------------------------------------------------------------------
FROM node:20.18.3-alpine AS deps

# 配置 npm 和 pnpm 使用国内镜像加速（可选，根据网络环境）
# 如果在国内构建，取消下面的注释
# RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm 包管理器
# 注意：使用 npm 安装避免 corepack 签名验证问题
RUN npm install -g pnpm@9

# 配置 pnpm 使用国内镜像（可选）
# RUN pnpm config set registry https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 复制包管理文件
# 只复制这两个文件，利用 Docker 缓存机制
COPY package.json pnpm-lock.yaml ./

# 安装生产依赖
# --frozen-lockfile: 确保使用 lock 文件中的确切版本
# --prod: 只安装生产依赖，不安装 devDependencies
RUN pnpm install --frozen-lockfile --prod

# -----------------------------------------------------------------------------
# 阶段 2: 构建层
# -----------------------------------------------------------------------------
# 用途：编译 TypeScript，构建 Next.js 应用
# 包含：所有依赖（包括开发依赖）
# -----------------------------------------------------------------------------
FROM node:20.18.3-alpine AS builder

# 配置 npm 使用国内镜像（可选）
# RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@9

# 配置 pnpm 使用国内镜像（可选）
# RUN pnpm config set registry https://registry.npmmirror.com

WORKDIR /app

# 复制包管理文件并安装所有依赖
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# 复制源代码
# 注意：.dockerignore 会排除不需要的文件
COPY . .

# 构建 Next.js 应用
# 生成优化的生产构建，输出到 .next 目录
RUN pnpm build

# -----------------------------------------------------------------------------
# 阶段 3: 生产运行层
# -----------------------------------------------------------------------------
# 用途：最终的生产镜像，只包含运行所需的最小文件
# 大小：约 150MB
# 特性：使用非 root 用户运行，提高安全性
# -----------------------------------------------------------------------------
FROM node:20.18.3-alpine AS runner

WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production \
    # 禁用 Next.js 遥测数据收集
    NEXT_TELEMETRY_DISABLED=1 \
    # 设置端口（可被覆盖）
    PORT=3000

# 创建非 root 用户和组
# 用户 ID 1001 是一个常见的非特权用户 ID
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 从依赖阶段复制生产依赖
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules

# 从构建阶段复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/next.config.ts ./next.config.ts

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查（可选，docker-compose 中也有配置）
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => process.exit(res.statusCode === 200 ? 0 : 1))"

# 启动命令
# 使用 node 直接运行，避免 npm/pnpm 的额外开销
CMD ["node_modules/.bin/next", "start"]

# -----------------------------------------------------------------------------
# 阶段 4: 开发环境层
# -----------------------------------------------------------------------------
# 用途：开发环境，支持热重载
# 特性：包含所有开发工具，挂载源代码目录
# 大小：约 500MB
# -----------------------------------------------------------------------------
FROM node:20.18.3-alpine AS development

# 配置 apk 使用国内镜像（可选）
# RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装开发工具
# git: 某些包可能需要
# python3, make, g++: 用于编译原生模块
RUN apk add --no-cache git python3 make g++ && \
    npm install -g pnpm@9

# 配置 pnpm 使用国内镜像（可选）
# RUN pnpm config set registry https://registry.npmmirror.com

WORKDIR /app

# 设置开发环境变量
ENV NODE_ENV=development \
    NEXT_TELEMETRY_DISABLED=1 \
    # 启用文件监视（Docker 环境需要）
    WATCHPACK_POLLING=true \
    # Turbopack 配置
    TURBOPACK_BUILD_TRACE_FILE_PATH=/app/.next/turbopack-trace.json

# 复制包管理文件
COPY package.json pnpm-lock.yaml ./

# 安装所有依赖（包括开发依赖）
RUN pnpm install --frozen-lockfile

# 复制源代码
# 在开发环境中，通常会通过 volume 挂载覆盖这些文件
COPY . .

# 暴露端口
EXPOSE 3000

# 暴露调试端口（如果需要）
EXPOSE 9229

# 启动开发服务器
# --turbopack: 使用 Turbopack 加速开发构建
CMD ["pnpm", "dev"]

# =============================================================================
# 构建示例：
# =============================================================================
# 开发环境: docker build --target development -t myapp:dev .
# 生产环境: docker build --target runner -t myapp:prod .
# 指定平台: docker build --platform linux/amd64 --target runner -t myapp:prod .
# =============================================================================