import { useIsMobile } from './hooks/useAppState'
import useResizeImage from './hooks/useResizeImage'
import { useCustomImageStore } from './hooks/useCustomImageStore'
import { useBackgroundStore, BackgroundTypeEnum } from './hooks/useBackgroundStore'

// BackgroundTypeEnum 已从 useBackgroundStore 导入

/**
 * 组件属性接口
 * 现在所有状态都通过 useBackgroundStore 管理，不需要props
 */
interface PublicFrameCustomViewProps {
    // 所有状态现在通过 useBackgroundStore 管理，不需要props
}

/**
 * 背景选择组件 - 提供透明、颜色、图片、Unsplash等背景选项的Tabs切换功能
 * 支持移动端和PC端不同的渲染样式，每个选项都有独立的事件处理逻辑
 *
 * @param {PublicFrameCustomViewProps} props - 组件属性
 * @returns {JSX.Element} 渲染的背景选择组件
 */
export const Public_FrameCustomView = ({}: PublicFrameCustomViewProps) => {
    // 检测是否为移动端设备
    const isMobile = useIsMobile()

    // 使用背景存储获取状态
    const { backgroundType, color, setBackgroundType, toggleColorPicker, toggleImagePicker } =
        useBackgroundStore()

    // 使用自定义图片存储获取动态图片
    const { image: customImage } = useCustomImageStore()

    // 动态图片URL：使用上传的图片或null作为占位符
    const dynamicImageUrl = customImage ? customImage.previewUrl : null
    const { resizedImage, isLoading, error } = useResizeImage(dynamicImageUrl, { scaleFactor: 1.5 })

    // 定义背景选项列表
    const itemList = [
        {
            label: 'Transparent',
            type: BackgroundTypeEnum.TRANSPARENT,
            content: (
                <div className='icon'>
                    <svg
                        xmlns='http://www.w3.org/2000/svg'
                        xmlnsXlink='http://www.w3.org/1999/xlink'
                        width={24}
                        height={24}
                        fill='none'
                    >
                        <rect
                            width={24}
                            height={24}
                            fill='url(#transparent_svg__a)'
                            fillOpacity='0.4'
                            rx={6}
                        />
                        <rect
                            width={23}
                            height={23}
                            x='0.5'
                            y='0.5'
                            stroke='#fff'
                            strokeOpacity='0.16'
                            rx='5.5'
                        />
                        <defs>
                            <pattern
                                id='transparent_svg__a'
                                width={1}
                                height={1}
                                patternContentUnits='objectBoundingBox'
                            >
                                <use xlinkHref='#transparent_svg__b' transform='scale(.01563)' />
                            </pattern>
                            <image
                                xlinkHref='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAA40lEQVR4Xu3bQQ6EQAhEUbj/oXsO8Sdh4XOvJAi/qkF3Zt6E6710++xuiD6T40uACtACqYlzD2IACFKBkoHcgmSQDJJBMngKIT6ADygF6DSYfcCLTzg/z0eGrASogDbT0gKxB2MB5pkiBoBgrEEMwIBjLx9fAAiCIAhygmkkRgYjhWMHditsL2AvYC+QIHjdwzk+BmAABmBAWc1kCF0bKRAEQRAEQRAMGaACbaCUz/P5BRiKxhQaiV07uRjfYgQDMKDpGAhGCMUCzD4CBEEw1iAGYIBPZMJh+g8/P8cKpAJfV4EfMee/sLtaEFIAAAAASUVORK5CYII='
                                id='transparent_svg__b'
                                width={64}
                                height={64}
                            />
                        </defs>
                    </svg>
                </div>
            ),
        },
        {
            label: 'Color',
            type: BackgroundTypeEnum.COLOR,
            content: (
                <div className='color-picker-preview' style={{ background: color || '#ffffff' }} />
            ),
        },
        {
            label: 'Image',
            type: BackgroundTypeEnum.IMAGE,
            content: (
                <>
                    {resizedImage ? (
                        <div className='image-picker-preview'>
                            {isLoading && <div>Loading...</div>}
                            {error && <div>Error resizing image</div>}
                            {resizedImage && (
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    alt='back'
                                    src={resizedImage}
                                />
                            )}
                        </div>
                    ) : (
                        <div className='icon'>
                            <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                <path
                                    fill='currentColor'
                                    d='M15.9 3C18.95 3 21 5.14 21 8.325v7.35C21 18.859 18.95 21 15.899 21h-7.8C5.049 21 3 18.859 3 15.675v-7.35C3 5.14 5.049 3 8.099 3zm.992 9.495c-.964-.602-1.709.243-1.91.513-.194.261-.36.549-.536.837-.429.711-.92 1.53-1.771 2.006-1.236.685-2.175.054-2.85-.405a5 5 0 0 0-.745-.44c-.604-.261-1.148.036-1.955 1.062-.424.536-.844 1.067-1.269 1.596-.255.317-.194.806.149 1.017.548.337 1.216.519 1.97.519h7.585c.428 0 .857-.059 1.266-.193a3.33 3.33 0 0 0 2.035-1.9c.322-.765.479-1.694.177-2.467-.1-.256-.25-.495-.461-.705-.553-.549-1.07-1.061-1.685-1.44M8.848 6.6a2.251 2.251 0 0 0 0 4.5c1.24 0 2.25-1.01 2.25-2.25 0-1.241-1.01-2.25-2.25-2.25'
                                />
                            </svg>
                        </div>
                    )}
                </>
            ),
        },
        {
            label: 'Unsplash',
            type: BackgroundTypeEnum.UNSPLASH,
            content: (
                <div className='icon'>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            d='M15.7 10.845H22V22H2V10.845h6.299v5.576H15.7zM15.7 2H8.299v5.576H15.7z'
                        />
                    </svg>
                </div>
            ),
        },
    ]

    // 背景状态现在通过 useBackgroundStore 管理，不再需要本地state

    /**
     * 处理透明背景选择事件
     * 设置背景为透明状态，清除所有背景设置
     */
    const handleTransparentBackground = (): void => {
        // 批量更新状态，减少重渲染
        setBackgroundType(BackgroundTypeEnum.TRANSPARENT)
        toggleColorPicker(false)
        toggleImagePicker(false)
        // 提升响应速度
    }

    /**
     * 处理颜色背景选择事件
     * 打开颜色选择器，允许用户选择自定义颜色，支持toggle功能
     */
    const handleColorBackground = (): void => {
        // 关闭其他弹窗
        toggleImagePicker(false)

        // 检查当前是否是颜色类型，实现toggle功能
        if (backgroundType === BackgroundTypeEnum.COLOR) {
            // 重复点击颜色，toggle颜色选择器
            toggleColorPicker()
        } else {
            // 切换到颜色类型并打开选择器
            setBackgroundType(BackgroundTypeEnum.COLOR)
            toggleColorPicker(true)
        }
    }

    /**
     * 处理图片背景选择事件
     * 打开文件选择器，允许用户上传本地图片，支持toggle功能
     */
    const handleImageBackground = (): void => {
        // 关闭其他弹窗
        toggleColorPicker(false)

        // 检查当前是否是图片类型，实现toggle功能
        if (backgroundType === BackgroundTypeEnum.IMAGE) {
            // 重复点击图片，toggle图片选择器
            toggleImagePicker()
        } else {
            // 切换到图片类型并打开选择器
            setBackgroundType(BackgroundTypeEnum.IMAGE)
            toggleImagePicker(true)
        }
    }

    /**
     * 处理Unsplash背景选择事件
     * 打开Unsplash图片库，允许用户选择在线图片
     */
    const handleUnsplashBackground = (): void => {
        // 关闭所有其他弹窗
        toggleColorPicker(false)
        toggleImagePicker(false)

        setBackgroundType(BackgroundTypeEnum.UNSPLASH)
        // 提升响应速度
    }

    /**
     * 根据背景类型执行对应的事件处理函数
     * @param {BackgroundTypeEnum} backgroundType - 背景类型枚举值
     */
    const executeBackgroundEvent = (backgroundType: BackgroundTypeEnum): void => {
        if (backgroundType === BackgroundTypeEnum.TRANSPARENT) {
            handleTransparentBackground()
        } else if (backgroundType === BackgroundTypeEnum.COLOR) {
            handleColorBackground()
        } else if (backgroundType === BackgroundTypeEnum.IMAGE) {
            handleImageBackground()
        } else if (backgroundType === BackgroundTypeEnum.UNSPLASH) {
            handleUnsplashBackground()
        }
    }

    /**
     * 处理背景选项Tab点击事件
     * 直接调用对应的业务逻辑，状态由store管理
     * @param {string} label - 被点击的选项标签
     * @param {BackgroundTypeEnum} type - 背景类型枚举值
     */
    const handleBackgroundTabClick = (label: string, type: BackgroundTypeEnum): void => {
        executeBackgroundEvent(type)
    }

    /**
     * 渲染公共的背景选项内容
     * 遍历所有选项并根据激活状态设置样式
     *
     * @returns {JSX.Element} 背景选项列表
     */
    const publicRender = () => {
        return (
            <>
                {itemList.map(item => {
                    let isActive = false

                    switch (item.type) {
                    case BackgroundTypeEnum.TRANSPARENT:
                        isActive = backgroundType === BackgroundTypeEnum.TRANSPARENT
                        break
                    case BackgroundTypeEnum.COLOR:
                        isActive =
                                backgroundType === BackgroundTypeEnum.COLOR && color !== '#ffffff'
                        break
                    case BackgroundTypeEnum.IMAGE:
                        isActive =
                                backgroundType === BackgroundTypeEnum.IMAGE && customImage !== null
                        break
                    case BackgroundTypeEnum.UNSPLASH:
                        isActive = backgroundType === BackgroundTypeEnum.UNSPLASH
                        break
                    }

                    return (
                        <div
                            key={item.label}
                            className={`panel-button custom-back-btn ${
                                isActive ? 'true-active' : 'false-active'
                            } has-label `}
                            onClick={() => handleBackgroundTabClick(item.label, item.type)}
                        >
                            <div className='preview' style={{ aspectRatio: '1 / 1' }}>
                                {item.content}
                            </div>
                            <div className='label-wrapper'>
                                <span className='footnote truncate'>{item.label}</span>
                            </div>
                        </div>
                    )
                })}
            </>
        )
    }

    /**
     * 渲染移动端版本的背景选择组件
     *
     * @returns {JSX.Element} 移动端背景选择组件
     */
    const mobileInViewRender = () => {
        return (
            <div
                id='panel-undefined-control-mobile'
                className='panel-control-mobile undefined'
                style={{ opacity: 1, transform: 'none' }}
            >
                <div className='panel-control-grid col-4'>{publicRender()}</div>
            </div>
        )
    }

    /**
     * 渲染PC端版本的背景选择组件
     *
     * @returns {JSX.Element} PC端背景选择组件
     */
    const pcInViewRender = () => {
        return (
            <>
                <div className='panel-control undefined '>
                    <span className='label gray-text'>background</span>
                    <div className='controls'>
                        <div className='panel-control-grid col-4'>{publicRender()}</div>
                    </div>
                </div>
            </>
        )
    }

    // 根据设备类型返回对应的渲染结果
    if (isMobile) {
        return mobileInViewRender()
    } else {
        return pcInViewRender()
    }
}
