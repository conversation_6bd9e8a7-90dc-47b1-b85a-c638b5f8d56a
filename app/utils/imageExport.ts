import * as htmlToImage from 'html-to-image'
import { toast } from 'sonner'
import { copyImageToClipboard, canCopyImagesToClipboard } from 'copy-image-clipboard'
import { getCanvasFrame } from '../features/viewDimensions/utils/重构/获取'
import { getExportSettings, getViewDimensions } from '../hooks/useAppState'
import { ImageExportFormat } from '../features/viewDimensions/utils/重构/状态管理'

/**
 * 导出状态枚举
 * 用于标识导出过程中的不同状态
 */
enum ExportStatus {
    /** 准备中 */
    Preparing = 'preparing',
    /** 导出中 */
    Exporting = 'exporting',
    /** 成功 */
    Success = 'success',
    /** 失败 */
    Failed = 'failed',
}

/**
 * 导出错误类型枚举
 * 用于分类不同类型的导出错误
 */
enum ExportErrorType {
    /** 无法获取目标元素 */
    ElementNotFound = 'element_not_found',
    /** 无法获取应用状态 */
    StateNotFound = 'state_not_found',
    /** 图片生成失败 */
    ImageGenerationFailed = 'image_generation_failed',
    /** 不支持的格式 */
    UnsupportedFormat = 'unsupported_format',
    /** 剪贴板操作失败 */
    ClipboardFailed = 'clipboard_failed',
    /** 浏览器不支持 */
    BrowserNotSupported = 'browser_not_supported',
}

/**
 * Toast 消息配置枚举
 * 统一管理所有提示消息的文案和样式
 */
enum ToastMessage {
    /** 开始导出 */
    ExportStarting = '正在准备导出...',
    /** 导出进行中 */
    ExportInProgress = '正在生成高质量图片...',
    /** 导出成功 */
    ExportSuccess = '图片导出成功！',
    /** 开始复制 */
    CopyStarting = '正在准备复制到剪贴板...',
    /** 复制进行中 */
    CopyInProgress = '正在处理图片数据...',
    /** 复制成功 */
    CopySuccess = '图片已复制到剪贴板！',
    /** 元素未找到 */
    ElementNotFound = '找不到要导出的内容，请刷新页面重试',
    /** 状态错误 */
    StateError = '应用状态异常，请重新设置导出参数',
    /** 图片生成失败 */
    ImageGenerationError = '图片生成失败，请重试',
    /** 格式不支持 */
    UnsupportedFormat = '不支持的导出格式',
    /** 剪贴板不支持 */
    ClipboardNotSupported = '当前浏览器不支持剪贴板功能',
    /** 剪贴板失败 */
    ClipboardError = '复制到剪贴板失败，请尝试下载',
    /** 下载失败 */
    DownloadError = '文件下载失败，请重试',
}

/**
 * 导出结果接口
 * 定义导出操作的返回结果结构
 */
interface ExportResult {
    /** 导出状态 */
    status: ExportStatus
    /** 导出的图片数据URL（成功时） */
    dataUrl?: string
    /** 错误信息（失败时） */
    error?: string
    /** 错误类型（失败时） */
    errorType?: ExportErrorType
    /** 实际导出的图片尺寸 */
    actualSize?: {
        width: number
        height: number
    }
}

/**
 * Blob导出结果接口
 * 定义Blob导出操作的返回结果结构
 */
interface BlobExportResult {
    /** 导出状态 */
    status: ExportStatus
    /** 导出的图片Blob对象（成功时） */
    blob?: Blob
    /** 错误信息（失败时） */
    error?: string
    /** 错误类型（失败时） */
    errorType?: ExportErrorType
    /** 实际导出的图片尺寸 */
    actualSize?: {
        width: number
        height: number
    }
}

/**
 * html-to-image 库的导出配置
 * 针对包含 SVG 和图片的复杂DOM结构进行优化
 * 支持 PNG 和 JPEG 两种格式
 */
interface ImageExportConfig {
    /** 图片质量 (0-1，仅JPEG格式使用) */
    quality?: number
    /** 像素密度比例，影响最终图片分辨率 */
    pixelRatio: number
    /** 背景颜色，确保透明区域有正确的背景 */
    backgroundColor: string
    /** 是否跳过字体嵌入，false确保字体正确显示 */
    skipFonts: boolean
    /** 是否启用CORS，true确保跨域图片正确加载 */
    useCORS: boolean
    /** 是否允许跨域污染画布，true确保复杂图片正确处理 */
    allowTaint: boolean
    /** 过滤器函数，用于处理特殊元素 */
    filter?: (node: Element) => boolean
}

/**
 * 显示错误提示的统一方法
 * 根据错误类型显示相应的用户友好提示信息
 *
 * @param errorType - 错误类型
 * @param customMessage - 自定义错误消息（可选）
 */
const showErrorToast = (errorType: ExportErrorType, customMessage?: string): void => {
    let message: string

    switch (errorType) {
    case ExportErrorType.ElementNotFound:
        message = ToastMessage.ElementNotFound
        break
    case ExportErrorType.StateNotFound:
        message = ToastMessage.StateError
        break
    case ExportErrorType.ImageGenerationFailed:
        message = customMessage || ToastMessage.ImageGenerationError
        break
    case ExportErrorType.UnsupportedFormat:
        message = ToastMessage.UnsupportedFormat
        break
    case ExportErrorType.BrowserNotSupported:
        message = ToastMessage.ClipboardNotSupported
        break
    case ExportErrorType.ClipboardFailed:
        message = ToastMessage.ClipboardError
        break
    default:
        message = customMessage || '操作失败，请重试'
    }

    toast.error(message, {
        duration: 4000,
        position: 'top-center',
    })
}

/**
 * 显示成功提示的统一方法
 *
 * @param message - 成功消息
 * @param description - 详细描述（可选）
 */
const showSuccessToast = (message: string, description?: string): void => {
    toast.success(message, {
        description,
        duration: 3000,
        position: 'top-center',
    })
}

/**
 * 显示加载提示的统一方法
 *
 * @param message - 加载消息
 * @returns Toast ID，用于后续更新或关闭
 */
const showLoadingToast = (message: string): string | number => {
    return toast.loading(message, {
        position: 'top-center',
    })
}

/**
 * 创建针对 DisplayContainer 优化的导出配置
 * 支持 PNG 和 JPEG 两种格式的专门优化
 *
 * @param format - 导出格式（PNG 或 JPEG）
 * @param scaleFactor - 缩放因子
 * @returns 优化后的导出配置
 */
const createOptimizedExportConfig = (
    format: ImageExportFormat,
    scaleFactor: number,
): ImageExportConfig => {
    // 基础配置，针对包含SVG和图片的复杂DOM进行优化
    const baseConfig = {
        pixelRatio: scaleFactor,
        skipFonts: false, // 确保字体正确显示
        useCORS: true, // 支持跨域图片
        allowTaint: true, // 允许跨域图片污染画布
        // 过滤函数：排除可能干扰导出的元素
        filter: (node: Element) => {
            // 排除工具提示、弹窗等覆盖元素
            if (node.classList?.contains('tooltip')) return false
            if (node.classList?.contains('modal')) return false
            if (node.classList?.contains('popover')) return false

            return true
        },
    }

    // 根据格式添加特定配置
    if (format === ImageExportFormat.JPEG) {
        return {
            ...baseConfig,
            quality: 0.95, // JPEG高质量设置
            backgroundColor: '#ffffff', // JPEG不支持透明，设置白色背景
        }
    } else if (format === ImageExportFormat.PNG) {
        return {
            ...baseConfig,
            backgroundColor: 'transparent', // PNG支持透明背景
        }
    } else {
        // 默认使用PNG配置
        return {
            ...baseConfig,
            backgroundColor: 'transparent',
        }
    }
}

/**
 * 获取要导出的目标元素
 *
 * @returns 导出结果，包含元素信息或错误信息
 */
const getExportTargetElement = (): ExportResult => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
        return {
            status: ExportStatus.Failed,
            error: '导出功能仅在浏览器环境中可用',
            errorType: ExportErrorType.BrowserNotSupported,
        }
    }

    // 获取画布框架元素
    const canvasFrame = getCanvasFrame()
    if (!canvasFrame || !canvasFrame.element) {
        return {
            status: ExportStatus.Failed,
            error: '无法找到要导出的画布元素，请确保页面已完全加载',
            errorType: ExportErrorType.ElementNotFound,
        }
    }

    return {
        status: ExportStatus.Success,
    }
}

/**
 * 执行图片导出为Blob的核心逻辑
 * 处理所有导出相关的状态获取、配置创建和Blob生成
 * 支持 PNG 和 JPEG 两种格式
 *
 * @returns Blob导出结果
 */
const executeImageExportAsBlob = async (): Promise<BlobExportResult> => {
    try {
        // 第一步：验证目标元素
        const elementResult = getExportTargetElement()
        if (elementResult.status === ExportStatus.Failed) {
            return {
                status: elementResult.status,
                error: elementResult.error,
                errorType: elementResult.errorType,
            }
        }

        // 第二步：获取应用状态
        let exportSettings, viewDimensions
        try {
            exportSettings = getExportSettings()
            viewDimensions = getViewDimensions()
        } catch (error) {
            return {
                status: ExportStatus.Failed,
                error: '无法获取应用设置状态',
                errorType: ExportErrorType.StateNotFound,
            }
        }

        // 第三步：验证状态有效性
        if (!exportSettings || !viewDimensions) {
            return {
                status: ExportStatus.Failed,
                error: '应用设置或视图尺寸状态无效',
                errorType: ExportErrorType.StateNotFound,
            }
        }

        // 第四步：获取目标DOM元素
        const canvasFrame = getCanvasFrame()
        if (!canvasFrame?.element) {
            return {
                status: ExportStatus.Failed,
                error: '无法获取画布元素',
                errorType: ExportErrorType.ElementNotFound,
            }
        }

        const targetElement = canvasFrame.element
        const { format, quality } = exportSettings

        // 第五步：创建导出配置
        const exportConfig = createOptimizedExportConfig(format, quality.scaleFactor)

        // 第六步：执行Blob生成（直接生成Blob，避免dataURL转换）
        let blob: Blob | null
        try {
            blob = await htmlToImage.toBlob(targetElement, exportConfig)
            if (!blob) {
                return {
                    status: ExportStatus.Failed,
                    error: '图片生成失败，未能创建Blob对象',
                    errorType: ExportErrorType.ImageGenerationFailed,
                }
            }
        } catch (imageError) {
            console.error('图片Blob生成失败:', imageError)
            return {
                status: ExportStatus.Failed,
                error: `图片生成失败: ${imageError instanceof Error ? imageError.message : '未知错误'}`,
                errorType: ExportErrorType.ImageGenerationFailed,
            }
        }

        // 第七步：获取Blob的实际尺寸（通过创建临时Image对象）
        const actualSize = await getBlobActualSize(blob)

        // 第八步：记录成功日志
        console.log(`✅ 图片Blob导出成功:
        - 格式: ${format}
        - 质量: ${quality.resolutionTierName} (${quality.scaleMultiplierLabel})
        - 预期尺寸: ${Math.round(viewDimensions.useWidth * quality.scaleFactor)}x${Math.round(viewDimensions.useHeight * quality.scaleFactor)}
        - 实际尺寸: ${actualSize.width}x${actualSize.height}
        - Blob大小: ${(blob.size / 1024).toFixed(2)} KB`)

        return {
            status: ExportStatus.Success,
            blob,
            actualSize,
        }
    } catch (unexpectedError) {
        console.error('Blob导出过程中发生意外错误:', unexpectedError)
        return {
            status: ExportStatus.Failed,
            error: `导出过程中发生意外错误: ${unexpectedError instanceof Error ? unexpectedError.message : '未知错误'}`,
            errorType: ExportErrorType.ImageGenerationFailed,
        }
    }
}
const executeImageExport = async (toastId?: string | number): Promise<ExportResult> => {
    try {
        // 第一步：验证目标元素
        const elementResult = getExportTargetElement()
        if (elementResult.status === ExportStatus.Failed) {
            return elementResult
        }

        // 第二步：获取应用状态
        let exportSettings, viewDimensions
        try {
            exportSettings = getExportSettings()
            viewDimensions = getViewDimensions()
        } catch (error) {
            return {
                status: ExportStatus.Failed,
                error: '无法获取应用设置状态',
                errorType: ExportErrorType.StateNotFound,
            }
        }

        // 第三步：验证状态有效性
        if (!exportSettings || !viewDimensions) {
            return {
                status: ExportStatus.Failed,
                error: '应用设置或视图尺寸状态无效',
                errorType: ExportErrorType.StateNotFound,
            }
        }

        // 第四步：获取目标DOM元素
        const canvasFrame = getCanvasFrame()
        if (!canvasFrame?.element) {
            return {
                status: ExportStatus.Failed,
                error: '无法获取画布元素',
                errorType: ExportErrorType.ElementNotFound,
            }
        }

        const targetElement = canvasFrame.element
        const { format, quality } = exportSettings

        // 更新Toast为导出进行中状态
        if (toastId) {
            toast.loading(ToastMessage.ExportInProgress, { id: toastId })
        }

        // 第五步：创建导出配置
        const exportConfig = createOptimizedExportConfig(format, quality.scaleFactor)

        // 第六步：执行图片生成（支持PNG和JPEG格式）
        let dataUrl: string
        try {
            if (format === ImageExportFormat.PNG) {
                dataUrl = await htmlToImage.toPng(targetElement, exportConfig)
            } else if (format === ImageExportFormat.JPEG) {
                dataUrl = await htmlToImage.toJpeg(targetElement, exportConfig)
            } else {
                return {
                    status: ExportStatus.Failed,
                    error: `不支持的导出格式: ${format}`,
                    errorType: ExportErrorType.UnsupportedFormat,
                }
            }
        } catch (imageError) {
            console.error('图片生成失败:', imageError)
            return {
                status: ExportStatus.Failed,
                error: `图片生成失败: ${imageError instanceof Error ? imageError.message : '未知错误'}`,
                errorType: ExportErrorType.ImageGenerationFailed,
            }
        }

        // 第七步：验证生成的图片并获取实际尺寸
        const actualSize = await getImageActualSize(dataUrl)

        // 第八步：记录成功日志
        console.log(`✅ 图片导出成功:
        - 格式: ${format}
        - 质量: ${quality.resolutionTierName} (${quality.scaleMultiplierLabel})
        - 预期尺寸: ${Math.round(viewDimensions.useWidth * quality.scaleFactor)}x${Math.round(viewDimensions.useHeight * quality.scaleFactor)}
        - 实际尺寸: ${actualSize.width}x${actualSize.height}`)

        return {
            status: ExportStatus.Success,
            dataUrl,
            actualSize,
        }
    } catch (unexpectedError) {
        console.error('导出过程中发生意外错误:', unexpectedError)
        return {
            status: ExportStatus.Failed,
            error: `导出过程中发生意外错误: ${unexpectedError instanceof Error ? unexpectedError.message : '未知错误'}`,
            errorType: ExportErrorType.ImageGenerationFailed,
        }
    }
}

/**
 * 获取Blob的实际尺寸
 * 通过创建临时Image对象来获取Blob图片的实际宽高
 *
 * @param blob - 图片Blob对象
 * @returns 图片的实际宽高
 */
const getBlobActualSize = (blob: Blob): Promise<{ width: number; height: number }> => {
    return new Promise(resolve => {
        const img = new Image()
        const url = URL.createObjectURL(blob)
        
        img.onload = () => {
            // 释放对象URL以避免内存泄漏
            URL.revokeObjectURL(url)
            resolve({
                width: img.naturalWidth,
                height: img.naturalHeight,
            })
        }
        img.onerror = () => {
            // 释放对象URL以避免内存泄漏
            URL.revokeObjectURL(url)
            // 如果图片加载失败，返回默认尺寸
            resolve({ width: 0, height: 0 })
        }
        img.src = url
    })
}

/**
 * 获取图片的实际尺寸
 *
 * @param dataUrl - 图片的data URL
 * @returns 图片的实际宽高
 */
const getImageActualSize = (dataUrl: string): Promise<{ width: number; height: number }> => {
    return new Promise(resolve => {
        const img = new Image()
        img.onload = () => {
            resolve({
                width: img.naturalWidth,
                height: img.naturalHeight,
            })
        }
        img.onerror = () => {
            // 如果图片加载失败，返回默认尺寸
            resolve({ width: 0, height: 0 })
        }
        img.src = dataUrl
    })
}

/**
 * 生成下载文件名
 * 基于当前设置生成具有描述性的文件名
 * 支持 PNG 和 JPEG 格式的正确扩展名
 *
 * @param actualSize - 实际图片尺寸
 * @returns 文件名字符串
 */
const generateDownloadFilename = (actualSize: { width: number; height: number }): string => {
    const exportSettings = getExportSettings()
    const viewDimensions = getViewDimensions()

    if (!exportSettings || !viewDimensions) {
        return `wallpaper_export.${exportSettings?.format?.toLowerCase() || 'png'}`
    }

    const { format, quality } = exportSettings
    const { key } = viewDimensions

    // 根据格式确定正确的文件扩展名
    const fileExtension = format === ImageExportFormat.JPEG ? 'jpg' : 'png'

    // 生成描述性文件名
    return `wallpaper_${key}_${quality.scaleMultiplierLabel}_${actualSize.width}x${actualSize.height}.${fileExtension}`
}

/**
 * 创建并触发文件下载
 *
 * @param dataUrl - 图片的data URL
 * @param filename - 下载文件名
 */
const triggerDownload = (dataUrl: string, filename: string): void => {
    try {
        const link = document.createElement('a')
        link.download = filename
        link.href = dataUrl

        // 添加到DOM并触发点击
        document.body.appendChild(link)
        link.click()

        // 清理DOM
        document.body.removeChild(link)

        console.log(`📁 文件下载已触发: ${filename}`)
    } catch (error) {
        console.error('触发下载失败:', error)
        throw new Error('无法触发文件下载')
    }
}

/**
 * 导出图片为Blob对象
 * 主要的图片Blob导出功能，用于OSS上传等场景
 * 无需任何参数，所有配置从应用状态获取
 * 支持 PNG 和 JPEG 两种格式
 * 
 * @example
 * ```typescript
 * // 导出图片为Blob对象，用于上传或其他处理
 * const result = await exportImageAsBlob();
 * if (result.success && result.blob) {
 *     // 可以将Blob用于上传到云存储等操作
 *     await uploadImageToOSS(result.blob);
 * }
 * ```
 *
 * @returns Promise<{success: boolean, blob?: Blob, error?: string, actualSize?: {width: number, height: number}}>
 */
export const exportImageAsBlob = async (): Promise<{
    success: boolean
    blob?: Blob
    error?: string
    actualSize?: { width: number; height: number }
}> => {
    try {
        console.log('🎯 开始导出图片为Blob...')

        // 执行Blob导出
        const result = await executeImageExportAsBlob()

        // 处理导出失败的情况
        if (result.status === ExportStatus.Failed) {
            console.error('❌ 图片Blob导出失败:', result.error)
            return {
                success: false,
                error: result.error,
            }
        }

        // 确保有有效的Blob数据
        if (!result.blob || !result.actualSize) {
            console.error('❌ Blob导出结果无效')
            return {
                success: false,
                error: '导出结果无效',
            }
        }

        console.log('✅ 图片Blob导出成功')
        return {
            success: true,
            blob: result.blob,
            actualSize: result.actualSize,
        }
    } catch (unexpectedError) {
        console.error('❌ Blob导出过程中发生意外错误:', unexpectedError)
        return {
            success: false,
            error: `发生意外错误: ${unexpectedError instanceof Error ? unexpectedError.message : '未知错误'}`,
        }
    }
}

/**
 * 导出图片并下载
 * 主要的图片导出功能，无需任何参数，所有配置从应用状态获取
 * 支持 PNG 和 JPEG 两种格式的导出
 * 集成了完整的 Toast 用户反馈体验
 *
 * @example
 * ```typescript
 * // 直接调用导出函数，支持PNG和JPEG格式
 * await exportImage();
 * ```
 *
 * @returns 是否导出成功
 */
export const exportImage = async (): Promise<boolean> => {
    // 显示开始导出的加载提示
    const toastId = showLoadingToast(ToastMessage.ExportStarting)

    try {
        console.log('🚀 开始导出图片...')

        // 执行图片导出
        const result = await executeImageExport(toastId)

        // 处理导出失败的情况
        if (result.status === ExportStatus.Failed) {
            console.error('❌ 图片导出失败:', result.error)

            // 关闭加载提示并显示错误
            toast.dismiss(toastId)
            if (result.errorType) {
                showErrorToast(result.errorType, result.error)
            }

            return false
        }

        // 确保有有效的图片数据
        if (!result.dataUrl || !result.actualSize) {
            console.error('❌ 导出结果无效')

            // 关闭加载提示并显示错误
            toast.dismiss(toastId)
            showErrorToast(ExportErrorType.ImageGenerationFailed, '导出结果无效')

            return false
        }

        try {
            // 生成文件名并触发下载
            const filename = generateDownloadFilename(result.actualSize)
            triggerDownload(result.dataUrl, filename)

            // 关闭加载提示并显示成功信息
            toast.dismiss(toastId)

            const exportSettings = getExportSettings()
            const successDescription = exportSettings
                ? `${exportSettings.quality.resolutionTierName} · ${result.actualSize.width}×${result.actualSize.height} · ${exportSettings.format}`
                : `${result.actualSize.width}×${result.actualSize.height}`

            showSuccessToast(ToastMessage.ExportSuccess, successDescription)

            console.log('✅ 图片导出并下载成功')
            return true
        } catch (downloadError) {
            console.error('❌ 文件下载失败:', downloadError)

            // 关闭加载提示并显示下载错误
            toast.dismiss(toastId)
            showErrorToast(ExportErrorType.ImageGenerationFailed, ToastMessage.DownloadError)

            return false
        }
    } catch (unexpectedError) {
        console.error('❌ 导出过程中发生意外错误:', unexpectedError)

        // 关闭加载提示并显示错误
        toast.dismiss(toastId)
        showErrorToast(ExportErrorType.ImageGenerationFailed, '发生意外错误，请重试')

        return false
    }
}

/**
 * 将图片复制到剪贴板
 * 使用 copy-image-clipboard 库将导出的图片直接复制到用户剪贴板
 * 支持 PNG 和 JPEG 两种格式的复制
 * 集成了完整的 Toast 用户反馈体验
 *
 * @example
 * ```typescript
 * // 直接调用复制到剪贴板函数，支持PNG和JPEG格式
 * const success = await exportToClipboard();
 * if (success) {
 *     console.log('图片已复制到剪贴板');
 * }
 * ```
 *
 * @returns 是否复制成功
 */
export const exportToClipboard = async (): Promise<boolean> => {
    // 检查浏览器对复制图片到剪贴板的支持
    if (!canCopyImagesToClipboard()) {
        console.error('❌ 当前浏览器不支持复制图片到剪贴板')
        showErrorToast(ExportErrorType.BrowserNotSupported)
        return false
    }

    // 显示开始复制的加载提示
    const toastId = showLoadingToast(ToastMessage.CopyStarting)

    try {
        console.log('📋 开始复制图片到剪贴板...')

        // 更新Toast为处理中状态
        toast.loading(ToastMessage.CopyInProgress, { id: toastId })

        // 执行图片导出
        const result = await executeImageExport()

        // 处理导出失败的情况
        if (result.status === ExportStatus.Failed) {
            console.error('❌ 图片导出失败，无法复制到剪贴板:', result.error)

            // 关闭加载提示并显示错误
            toast.dismiss(toastId)
            if (result.errorType) {
                showErrorToast(result.errorType, result.error)
            }

            return false
        }

        // 确保有有效的图片数据
        if (!result.dataUrl || !result.actualSize) {
            console.error('❌ 导出结果无效，无法复制到剪贴板')

            // 关闭加载提示并显示错误
            toast.dismiss(toastId)
            showErrorToast(ExportErrorType.ImageGenerationFailed, '导出结果无效')

            return false
        }

        try {
            // 使用 copy-image-clipboard 库复制图片
            // 该库支持 PNG 和 JPEG 格式
            await copyImageToClipboard(result.dataUrl)

            // 关闭加载提示并显示成功信息
            toast.dismiss(toastId)

            const exportSettings = getExportSettings()
            const successDescription = exportSettings
                ? `${exportSettings.quality.resolutionTierName} · ${result.actualSize.width}×${result.actualSize.height} · ${exportSettings.format}`
                : `${result.actualSize.width}×${result.actualSize.height}`

            showSuccessToast(ToastMessage.CopySuccess, successDescription)

            console.log(`✅ 图片已复制到剪贴板:
            - 格式: ${exportSettings?.format || 'Unknown'}
            - 尺寸: ${result.actualSize.width}x${result.actualSize.height}`)

            return true
        } catch (clipboardError) {
            console.error('❌ 复制到剪贴板失败:', clipboardError)

            // 关闭加载提示并显示剪贴板错误
            toast.dismiss(toastId)
            showErrorToast(ExportErrorType.ClipboardFailed)

            return false
        }
    } catch (unexpectedError) {
        console.error('❌ 复制过程中发生意外错误:', unexpectedError)

        // 关闭加载提示并显示错误
        toast.dismiss(toastId)
        showErrorToast(ExportErrorType.ClipboardFailed, '发生意外错误，请重试')

        return false
    }
}
