/**
 * @file 精确数学计算工具函数
 * @description 使用 decimal.js 库封装常用的数学运算，避免 JavaScript 浮点数精度问题
 */

import Decimal from 'decimal.js'

/**
 * 精确除法运算
 * @param dividend 被除数
 * @param divisor 除数
 * @returns 计算结果
 * @example
 * divide(10, 3) // 3.333333333333333
 * divide(1, 100) // 0.01
 */
export function divide(dividend: number, divisor: number): number {
    return new Decimal(dividend).dividedBy(divisor).toNumber()
}

/**
 * 精确乘法运算
 * @param multiplicand 被乘数
 * @param multiplier 乘数
 * @returns 计算结果
 * @example
 * multiply(0.1, 0.2) // 0.02
 * multiply(19.9, 100) // 1990
 */
export function multiply(multiplicand: number, multiplier: number): number {
    return new Decimal(multiplicand).times(multiplier).toNumber()
}

/**
 * 精确加法运算
 * @param augend 被加数
 * @param addend 加数
 * @returns 计算结果
 * @example
 * add(0.1, 0.2) // 0.3
 */
export function add(augend: number, addend: number): number {
    return new Decimal(augend).plus(addend).toNumber()
}

/**
 * 精确减法运算
 * @param minuend 被减数
 * @param subtrahend 减数
 * @returns 计算结果
 * @example
 * subtract(0.3, 0.1) // 0.2
 */
export function subtract(minuend: number, subtrahend: number): number {
    return new Decimal(minuend).minus(subtrahend).toNumber()
}

/**
 * 精确四舍五入
 * @param value 要四舍五入的数值
 * @param decimalPlaces 保留的小数位数，默认为 0
 * @returns 四舍五入后的结果
 * @example
 * round(1.234567, 2) // 1.23
 * round(1.5) // 2
 */
export function round(value: number, decimalPlaces: number = 0): number {
    return new Decimal(value).toDecimalPlaces(decimalPlaces).toNumber()
}

/**
 * 精确向上取整
 * @param value 要向上取整的数值
 * @returns 向上取整后的结果
 * @example
 * ceil(1.1) // 2
 * ceil(1.0) // 1
 */
export function ceil(value: number): number {
    return new Decimal(value).ceil().toNumber()
}

/**
 * 精确向下取整
 * @param value 要向下取整的数值
 * @returns 向下取整后的结果
 * @example
 * floor(1.9) // 1
 * floor(2.0) // 2
 */
export function floor(value: number): number {
    return new Decimal(value).floor().toNumber()
}

/**
 * 获取最小值
 * @param values 数值数组
 * @returns 最小值
 * @example
 * min(1, 2, 3) // 1
 * min(0.1, 0.2) // 0.1
 */
export function min(...values: number[]): number {
    if (values.length === 0) throw new Error('至少需要一个参数')
    return Decimal.min(...values).toNumber()
}

/**
 * 获取最大值
 * @param values 数值数组
 * @returns 最大值
 * @example
 * max(1, 2, 3) // 3
 * max(0.1, 0.2) // 0.2
 */
export function max(...values: number[]): number {
    if (values.length === 0) throw new Error('至少需要一个参数')
    return Decimal.max(...values).toNumber()
}

/**
 * 精确计算平方根
 * @param value 要计算平方根的数值
 * @returns 平方根
 * @example
 * sqrt(4) // 2
 * sqrt(2) // 1.4142135623730951
 */
export function sqrt(value: number): number {
    return new Decimal(value).sqrt().toNumber()
}

/**
 * 精确计算幂
 * @param base 底数
 * @param exponent 指数
 * @returns 计算结果
 * @example
 * pow(2, 3) // 8
 * pow(10, -2) // 0.01
 */
export function pow(base: number, exponent: number): number {
    return new Decimal(base).pow(exponent).toNumber()
}

/**
 * 精确计算绝对值
 * @param value 数值
 * @returns 绝对值
 * @example
 * abs(-5) // 5
 * abs(5) // 5
 */
export function abs(value: number): number {
    return new Decimal(value).abs().toNumber()
}

/**
 * 保留指定小数位数（不四舍五入，直接截断）
 * @param value 数值
 * @param decimalPlaces 保留的小数位数
 * @returns 截断后的结果
 * @example
 * toFixed(1.236, 2) // 1.23
 * toFixed(1.999, 2) // 1.99
 */
export function toFixed(value: number, decimalPlaces: number): number {
    return new Decimal(value).toDecimalPlaces(decimalPlaces, Decimal.ROUND_DOWN).toNumber()
}

/**
 * 格式化为字符串，保留指定小数位数
 * @param value 数值
 * @param decimalPlaces 保留的小数位数
 * @returns 格式化后的字符串
 * @example
 * toFixedString(1.5, 2) // "1.50"
 * toFixedString(1.999, 2) // "2.00"
 */
export function toFixedString(value: number, decimalPlaces: number): string {
    return new Decimal(value).toFixed(decimalPlaces)
}

/**
 * 批量精确计算
 * 用于链式计算，避免中间结果的精度损失
 * @example
 * const result = calculate(100)
 *   .divide(3)
 *   .multiply(2)
 *   .add(1)
 *   .subtract(0.5)
 *   .round(2)
 *   .value() // 67.17
 */
export function calculate(initialValue: number) {
    let decimal = new Decimal(initialValue)

    return {
        add(value: number) {
            decimal = decimal.plus(value)
            return this
        },
        subtract(value: number) {
            decimal = decimal.minus(value)
            return this
        },
        multiply(value: number) {
            decimal = decimal.times(value)
            return this
        },
        divide(value: number) {
            decimal = decimal.dividedBy(value)
            return this
        },
        pow(value: number) {
            decimal = decimal.pow(value)
            return this
        },
        sqrt() {
            decimal = decimal.sqrt()
            return this
        },
        abs() {
            decimal = decimal.abs()
            return this
        },
        round(decimalPlaces: number = 0) {
            decimal = decimal.toDecimalPlaces(decimalPlaces)
            return this
        },
        floor() {
            decimal = decimal.floor()
            return this
        },
        ceil() {
            decimal = decimal.ceil()
            return this
        },
        value(): number {
            return decimal.toNumber()
        },
        toString(decimalPlaces?: number): string {
            return decimalPlaces !== undefined
                ? decimal.toFixed(decimalPlaces)
                : decimal.toString()
        },
    }
}