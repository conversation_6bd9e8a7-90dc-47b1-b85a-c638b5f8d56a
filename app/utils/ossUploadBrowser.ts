/**
 * 纯前端阿里云OSS上传工具
 * 使用CDN版本的OSS SDK，避免Node.js依赖问题
 */

// 全局类型声明
declare global {
    interface Window {
        OSS: any
    }
}

/**
 * 阿里云OSS配置接口
 */
interface OSSConfig {
    accessKeyId: string     // AccessKey ID
    accessKeySecret: string // AccessKey Secret
    region: string          // 地域，例如：oss-cn-guangzhou
    bucket: string          // 存储空间名称
    customDomain?: string   // 自定义域名（可选）
}

/**
 * 上传结果接口
 */
interface UploadResult {
    success: boolean        // 上传是否成功
    url?: string           // 上传成功后的永久访问URL
    error?: string         // 错误信息
}

/**
 * 默认OSS配置
 */
const DEFAULT_OSS_CONFIG: OSSConfig = {
    accessKeyId: 'LTAI5tNr92DFCjZDm1Wt2juy',
    accessKeySecret: '******************************',
    region: 'oss-cn-guangzhou',
    bucket: 'mockpix-oss-bucket',
    customDomain: 'https://remotion-upload.mockpix.com', // 您的自定义域名
}

/**
 * 生成唯一文件名
 * 格式：timestamp_randomString.extension
 */
function generateUniqueFileName(originalName?: string): string {
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 8)
    const extension = originalName ? originalName.split('.').pop() : 'png'
    return `${timestamp}_${randomString}.${extension}`
}

/**
 * 动态加载OSS SDK
 * 使用CDN版本避免Node.js依赖问题
 */
async function loadOSSSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
        // 如果已经加载过，直接返回
        if (window.OSS) {
            resolve()
            return
        }

        // 创建script标签动态加载
        const script = document.createElement('script')
        script.src = 'https://gosspublic.alicdn.com/aliyun-oss-sdk-6.23.0.min.js'
        script.async = true
        
        script.onload = () => {
            if (window.OSS) {
                console.log('✅ OSS SDK 加载成功')
                resolve()
            } else {
                reject(new Error('OSS SDK 加载失败'))
            }
        }
        
        script.onerror = () => {
            reject(new Error('无法加载 OSS SDK'))
        }
        
        document.head.appendChild(script)
    })
}

/**
 * 创建OSS客户端实例
 * 使用浏览器版本的配置
 */
function createOSSClient(config: OSSConfig = DEFAULT_OSS_CONFIG): any {
    if (!window.OSS) {
        throw new Error('OSS SDK 未加载，请先调用 loadOSSSDK()')
    }

    return new window.OSS({
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        region: config.region,
        bucket: config.bucket,
        // 浏览器专用配置
        secure: true,           // 使用HTTPS
        useFetch: true,         // 使用fetch API而不是XMLHttpRequest
        enableProxy: false,     // 禁用代理（避免proxy-agent问题）
        // 添加CORS相关配置
        timeout: 60000,         // 60秒超时
        // 自定义域名配置（如果有的话）
        // cname: true,
        // endpoint: 'your-custom-domain.com',
    })
}

/**
 * 上传图片到阿里云OSS（纯前端版本）
 * @param imageBlob - 图片Blob对象
 * @param fileName - 自定义文件名（可选）
 * @param config - OSS配置（可选，使用默认配置）
 * @returns Promise<UploadResult> - 上传结果
 * 
 * 使用示例：
 * await loadOSSSDK(); // 确保SDK已加载
 * const result = await uploadImageToOSS(imageBlob);
 * if (result.success) {
 *   console.log('上传成功，图片地址：', result.url);
 * } else {
 *   console.error('上传失败：', result.error);
 * }
 */
export async function uploadImageToOSS(
    imageBlob: Blob,
    fileName?: string,
    config?: OSSConfig,
): Promise<UploadResult> {
    try {
        // 验证输入参数
        if (!imageBlob || !(imageBlob instanceof Blob)) {
            return {
                success: false,
                error: '无效的图片数据，请提供有效的Blob对象',
            }
        }

        // 检查文件大小（限制为10MB）
        const maxSize = 10 * 1024 * 1024 // 10MB
        if (imageBlob.size > maxSize) {
            return {
                success: false,
                error: `文件大小超过限制，最大允许 ${maxSize / 1024 / 1024}MB`,
            }
        }

        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        if (!allowedTypes.includes(imageBlob.type)) {
            return {
                success: false,
                error: '不支持的文件类型，仅支持 JPEG、PNG、GIF、WebP 格式',
            }
        }

        // 确保OSS SDK已加载
        if (!window.OSS) {
            await loadOSSSDK()
        }

        // 创建OSS客户端
        const ossClient = createOSSClient(config)

        // 生成文件名
        const finalFileName = fileName || generateUniqueFileName()
        
        // 设置上传路径，在images文件夹下
        const uploadPath = `images/${finalFileName}`

        console.log(`🔄 开始上传图片到OSS: ${uploadPath}`)

        // 执行上传操作，设置正确的HTTP头以确保图片可以在浏览器中直接查看
        const uploadResult = await ossClient.put(uploadPath, imageBlob, {
            // 设置文件MIME类型
            mime: imageBlob.type,
            // 设置HTTP头，确保图片在浏览器中显示而不是下载
            headers: {
                'Content-Type': imageBlob.type,
                'Content-Disposition': 'inline', // 关键设置：inline而不是attachment
            },
            // 设置元数据
            meta: {
                uploadTime: new Date().toISOString(),
                fileSize: imageBlob.size.toString(),
                userAgent: navigator.userAgent,
            },
        })

        // 检查上传是否成功
        if (!uploadResult || !uploadResult.name) {
            return {
                success: false,
                error: '上传失败，未收到有效的响应',
            }
        }

        // 生成自定义域名URL（优先使用自定义域名）
        const finalConfig = config || DEFAULT_OSS_CONFIG
        const customDomain = finalConfig.customDomain
        
        let permanentUrl: string
        if (customDomain) {
            // 使用自定义域名
            permanentUrl = `${customDomain}/${uploadPath}`
        } else {
            // 使用OSS默认域名
            permanentUrl = `https://${finalConfig.bucket}.${finalConfig.region}.aliyuncs.com/${uploadPath}`
        }

        console.log(`✅ 图片上传成功: ${permanentUrl}`)
        console.log(`🔗 自定义域名URL（可直接在浏览器中查看）: ${permanentUrl}`)

        return {
            success: true,
            url: permanentUrl,        // 使用自定义域名的永久URL
        }
    } catch (error) {
        // 处理不同类型的错误
        let errorMessage = '上传失败'

        if (error instanceof Error) {
            errorMessage = error.message
        } else if (typeof error === 'string') {
            errorMessage = error
        }

        // 根据常见错误类型提供更友好的错误信息
        if (errorMessage.includes('AccessDenied')) {
            errorMessage = 'OSS访问权限不足，请检查AccessKey配置'
        } else if (errorMessage.includes('NoSuchBucket')) {
            errorMessage = '存储桶不存在，请检查bucket配置'
        } else if (errorMessage.includes('InvalidAccessKeyId')) {
            errorMessage = 'AccessKey ID无效，请检查配置'
        } else if (errorMessage.includes('SignatureDoesNotMatch')) {
            errorMessage = 'AccessKey Secret错误，请检查配置'
        } else if (errorMessage.includes('CORS') || errorMessage.includes('Access-Control-Allow-Origin')) {
            errorMessage = `CORS配置错误！请在OSS控制台配置CORS规则：
            
📋 OSS控制台配置步骤：
1. 登录阿里云OSS控制台
2. 进入存储空间：mockpix-oss-bucket
3. 左侧菜单：权限管理 > 跨域设置
4. 添加规则：
   - 来源：http://localhost:3000
   - 方法：GET,PUT,POST,DELETE,HEAD
   - 允许Headers：*
   - 暴露Headers：ETag,x-oss-request-id

或者临时将Bucket权限改为"公共读写"进行测试`
        } else if (errorMessage.includes('Failed to fetch')) {
            errorMessage = `网络请求失败，可能原因：
            
🔍 排查步骤：
1. 检查网络连接
2. 确认OSS Bucket的CORS设置
3. 确认Bucket读写权限（当前为私有）
4. 检查AccessKey权限

💡 建议：先在OSS控制台配置CORS规则`
        }

        console.error('❌ OSS上传失败:', errorMessage)

        return {
            success: false,
            error: errorMessage,
        }
    }
}

/**
 * 预加载OSS SDK
 * 建议在应用初始化时调用，避免上传时的延迟
 */
export async function preloadOSSSDK(): Promise<boolean> {
    try {
        await loadOSSSDK()
        return true
    } catch (error) {
        console.error('预加载OSS SDK失败:', error)
        return false
    }
}

/**
 * 检查OSS SDK是否已加载
 */
export function isOSSSDKLoaded(): boolean {
    return !!window.OSS
}

/**
 * 导出配置类型，供外部使用
 */
export type { OSSConfig, UploadResult }