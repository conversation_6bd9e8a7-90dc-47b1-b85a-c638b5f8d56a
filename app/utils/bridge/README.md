# WebView Bridge 使用指南

## 概述

WebView Bridge 提供了 H5 页面与微信小程序之间的通信能力，让 Next.js 应用可以调用小程序的原生功能。

## 快速开始

### 1. 检查环境

```typescript
import bridge from '@/utils/bridge'

if (bridge.isReady()) {
    console.log('在小程序环境中')
}
```

### 2. 保存图片到相册

```typescript
// 在图片上传成功后
function handleImageUploaded(imageUrl: string) {
    if (!bridge.isReady()) {
        // 非小程序环境的处理
        return
    }
    
    // 保存到小程序相册
    bridge.saveImage(imageUrl)
    // 小程序会显示 Toast 提示保存结果
}
```

## 完整示例

### React 组件中使用

```tsx
import { useState } from 'react'
import bridge from '@/utils/bridge'

export function ImageUploader() {
    const [uploading, setUploading] = useState(false)
    
    const handleUpload = async (file: File) => {
        setUploading(true)
        
        try {
            // 上传图片到服务器
            const formData = new FormData()
            formData.append('image', file)
            
            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            
            const { imageUrl } = await response.json()
            
            // 如果在小程序环境，保存到相册
            if (bridge.isReady()) {
                bridge.saveImage(imageUrl)
                // 用户会在小程序端看到保存结果提示
            } else {
                // 浏览器环境，提供下载链接
                window.open(imageUrl)
            }
        } catch (error) {
            console.error('上传失败:', error)
        } finally {
            setUploading(false)
        }
    }
    
    return (
        <div>
            <input 
                type="file" 
                accept="image/*"
                onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleUpload(file)
                }}
                disabled={uploading}
            />
            {uploading && <p>上传中...</p>}
        </div>
    )
}
```

### 批量保存图片

```typescript
function BatchImageSaver({ images }: { images: string[] }) {
    const handleBatchSave = () => {
        if (!bridge.isReady()) {
            alert('请在微信小程序中使用此功能')
            return
        }
        
        // 逐个保存图片
        images.forEach((imageUrl, index) => {
            // 延迟发送，避免同时处理太多
            setTimeout(() => {
                bridge.saveImage(imageUrl)
            }, index * 1000)
        })
    }
    
    return (
        <button onClick={handleBatchSave}>
            批量保存 {images.length} 张图片
        </button>
    )
}
```

## 通信流程

1. **H5 发送消息**：通过 `bridge.saveImage(url)` 发送请求
2. **消息缓存**：消息通过 `wx.miniProgram.postMessage()` 发送并缓存
3. **触发处理**：通过 `navigateTo` 立即触发 `bindmessage` 事件
4. **小程序处理**：小程序接收消息并执行相应操作
5. **用户反馈**：通过 Toast 等 UI 组件显示操作结果

## API 参考

### `bridge.isReady()`

检查是否在微信小程序 WebView 环境中。

**返回值**：`boolean`

### `bridge.saveImage(imageUrl)`

保存图片到相册。

**参数**：
- `imageUrl: string` - 要保存的图片 URL

**返回值**：`boolean` - 是否成功发送请求

## 注意事项

1. **环境检测**：调用 Bridge API 前务必检查环境
2. **用户反馈**：操作结果通过小程序 Toast 显示，H5 端无需处理
3. **异步操作**：保存图片等操作是异步的，但不返回 Promise
4. **错误处理**：错误信息在小程序端显示，H5 端无法获取

## 调试技巧

1. 打开浏览器控制台查看 `[Bridge]` 前缀的日志
2. 在微信开发者工具中查看小程序端日志
3. 检查 `bridge.isReady()` 返回值确认环境

## 常见问题

### Q: 为什么消息没有立即处理？

A: `bindmessage` 事件需要特定操作触发。我们通过 `navigateTo` 自动触发，确保消息立即处理。

### Q: 如何知道操作是否成功？

A: 操作结果通过小程序的 Toast 提示显示。用户可以直接看到"图片已保存到相册"等提示。

### Q: 可以获取操作结果吗？

A: 当前方案是单向通信，H5 无法获取操作结果。如需双向通信，需要更复杂的实现。