/**
 * WebView Bridge - H5 端与微信小程序通信模块
 *
 * 功能：
 * 1. 环境检测：判断是否在小程序 WebView 中
 * 2. 消息发送：向小程序发送指令
 * 3. 自动触发：通过 navigateTo 触发消息处理
 */

import wxSDK from 'weixin-js-sdk'

// ============= 类型定义 =============

interface WxMiniProgram {
    postMessage: (data: { data: BridgeMessage }) => void
    navigateTo: (options: { url: string }) => void
    navigateBack: (options?: { delta?: number }) => void
    getEnv?: (callback: (res: { miniprogram: boolean }) => void) => void
}

interface BridgeMessage {
    action: string
    data: Record<string, unknown>
    timestamp: number
}

// 扩展 Window 类型，明确定义 wx 的类型
interface WindowWithWx extends Window {
    wx?: {
        miniProgram: WxMiniProgram
    }
}

// ============= 核心类 =============

class WebViewBridge {
    private readonly debug: boolean
    private readonly triggerDelay: number
    private readonly triggerUrl: string
    private wxMiniProgram: WxMiniProgram | null = null

    constructor(debug = false) {
        this.debug = debug
        this.triggerDelay = 100 // 触发延迟（毫秒）
        this.triggerUrl = '/pages/webview/index?action=trigger'
        this.init()
    }

    /**
     * 初始化 Bridge
     * 检测环境并缓存 miniProgram 对象
     */
    private init(): void {
        if (typeof window === 'undefined') {
            return
        }

        // 尝试多种方式获取 wx 对象
        // 1. 首先检查 window.wx（小程序 WebView 注入）
        const win = window as WindowWithWx

        // 2. 如果 window.wx 不存在，尝试使用导入的 wxSDK
        if (!win.wx && wxSDK) {
            // 将 wxSDK 赋值给 window.wx
            // @ts-expect-error wxSDK 类型与 window.wx 不完全匹配
            window.wx = wxSDK
        }

        // 3. 再次检查 wx.miniProgram
        if (win.wx?.miniProgram) {
            this.wxMiniProgram = win.wx.miniProgram
            this.log('Bridge 初始化成功')
            this.log('wxSDK 已导入，可用于 JSSDK 功能')
        } else {
            // 非小程序环境，记录日志但不报错
            this.log('当前不在小程序 WebView 环境中')

            // 在开发环境显示提示
            if (process.env.NODE_ENV === 'development') {
                console.warn('[Bridge] 当前页面不在微信小程序 WebView 中运行，Bridge 功能将不可用')
            }
        }
    }

    /**
     * 调试日志输出
     */
    private log(...args: unknown[]): void {
        if (this.debug) {
            // eslint-disable-next-line no-console
            console.log('[Bridge]', ...args)
        }
    }

    /**
     * 检查是否在小程序环境
     */
    isReady(): boolean {
        return this.wxMiniProgram !== null
    }

    /**
     * 发送消息到小程序
     * @param action - 操作类型
     * @param data - 操作数据
     * @returns 是否成功发送
     */
    private sendMessage(action: string, data: Record<string, unknown>): boolean {
        if (!this.isReady()) {
            console.warn('[Bridge] 不在小程序环境，无法发送消息')
            return false
        }

        // 构建消息
        const message: BridgeMessage = {
            action,
            data,
            timestamp: Date.now(),
        }

        try {
            // 发送消息（会被缓存）
            this.wxMiniProgram!.postMessage({ data: message })
            this.log('消息已发送:', message)

            return true
        } catch (error) {
            console.error('[Bridge] 发送消息失败:', error)
            return false
        }
    }

    /**
     * 跳转到小程序指定路由（内部方法）
     * @param route - 目标路由路径
     * @param params - 路由参数
     * @returns 是否成功跳转
     * @example
     * ```typescript
     * // 假设 route = '/pages/gallery/index', params = { id: '123', type: 'photo' }
     * navigateToRoute('/pages/gallery/index', { id: '123', type: 'photo' })
     * // 生成的 URL: /pages/gallery/index?id=123&type=photo
     * ```
     */
    private navigateToRoute(route: string, params?: Record<string, unknown>): boolean {
        if (!this.isReady()) {
            console.warn('[Bridge] 不在小程序环境，无法跳转路由')
            return false
        }

        if (!route) {
            console.error('[Bridge] 路由路径不能为空')
            return false
        }

        try {
            // 使用 URLSearchParams 简化参数处理
            // 自动处理编码和过滤无效值
            const url = params
                ? `${route}?${new URLSearchParams(params as Record<string, string>)}`
                : route

            this.log('跳转到小程序路由:', url)

            // 执行跳转
            this.wxMiniProgram!.navigateTo({ url })

            return true
        } catch (error) {
            console.error('[Bridge] 路由跳转失败:', error)
            return false
        }
    }

    // ============= 公开方法 =============

    /**
     * 保存图片到相册
     * @param imageUrl - 图片 URL
     * @example
     * ```typescript
     * // 假设 imageUrl = 'https://example.com/photo.jpg'
     * bridge.saveImage(imageUrl)
     * // 小程序会：
     * // 1. 发送保存图片消息到小程序
     * // 2. 跳转到小程序主页处理保存
     * // 3. 小程序下载并保存图片到相册
     * ```
     */
    saveImage(imageUrl: string): boolean {
        if (!imageUrl) {
            console.error('[Bridge] 图片 URL 不能为空')
            return false
        }

        if (!this.isReady()) {
            console.warn('[Bridge] 不在小程序环境，无法保存图片')
            return false
        }

        this.log('请求保存图片:', imageUrl)

        // 路由跳转方法，传递图片 URL 作为参数
        // 小程序可以从 URL 参数中获取图片地址进行处理
        const routeSuccess = this.navigateToRoute('/pages/index/index', {
            // action: 'saveImage',
            imageUrl: imageUrl,
            // timestamp: Date.now(),
        })

        if (!routeSuccess) {
            console.error('[Bridge] 跳转到小程序主页失败')
            return false
        }

        this.log('已发送保存请求并跳转到小程序主页')
        return true
    }

    /**
     * 开启调试模式
     */
    enableDebug(): void {
        // 使用对象解构赋值避免 any 类型
        Object.assign(this, { debug: true })
        this.log('调试模式已开启')
    }
}

// ============= 导出 =============

// 创建单例实例
const bridgeInstance = new WebViewBridge()
// 导出便捷方法对象
export const bridge = {
    /**
     * 检查是否在小程序环境
     */
    isReady: () => bridgeInstance.isReady(),

    /**
     * 保存图片到相册
     */
    saveImage: (imageUrl: string) => bridgeInstance.saveImage(imageUrl),

    /**
     * 开启调试模式
     */
    enableDebug: () => bridgeInstance.enableDebug(),
}
// 默认导出
export default bridge

// 导出 wxSDK 供其他模块使用
export { wxSDK }

// 开发环境自动开启调试
if (process.env.NODE_ENV === 'development') {
    bridge.enableDebug()
}
