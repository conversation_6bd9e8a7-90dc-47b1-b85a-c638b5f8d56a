/**
 * 微信 JS-SDK 类型定义补充
 * 为 weixin-js-sdk 提供基础类型支持
 */

declare module 'weixin-js-sdk' {
    interface WxConfig {
        debug?: boolean
        appId: string
        timestamp: number
        nonceStr: string
        signature: string
        jsApiList: string[]
    }

    interface Wx {
        config(config: WxConfig): void
        ready(callback: () => void): void
        error(callback: (res: any) => void): void
        
        // 分享相关
        updateAppMessageShareData(config: any): void
        updateTimelineShareData(config: any): void
        
        // 图片相关
        chooseImage(config: any): void
        previewImage(config: any): void
        uploadImage(config: any): void
        downloadImage(config: any): void
        
        // 获取本地图片接口
        getLocalImgData(config: any): void
        
        // 其他常用接口
        getLocation(config: any): void
        openLocation(config: any): void
        closeWindow(): void
        hideMenuItems(config: any): void
        showMenuItems(config: any): void
        
        // 小程序相关（注意：这个通常在 window.wx 上）
        miniProgram?: {
            postMessage: (data: any) => void
            navigateTo: (options: { url: string }) => void
            navigateBack: (options?: { delta?: number }) => void
            getEnv?: (callback: (res: { miniprogram: boolean }) => void) => void
        }
    }

    const wx: Wx
    export default wx
}