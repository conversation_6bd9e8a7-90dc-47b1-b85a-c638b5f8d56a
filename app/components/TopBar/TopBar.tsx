'use client'

import { ExportPopver } from '@/app/components/ExportPopver/ExportPopver'
import { ModalKey } from '@/app/features/viewDimensions/utils/重构/状态管理'
import { useExportSettings, useIsMobile, useModalActions } from '@/app/hooks/useAppState'
import bridge from '@/app/utils/bridge'
import { ChevronRight } from 'lucide-react'
import { BiSolidMessageError } from 'react-icons/bi'
import { RiArrowGoBackFill, RiArrowGoForwardFill, RiCommandFill } from 'react-icons/ri'
import { exportImage, exportToClipboard, exportImageAsBlob } from '../../utils/imageExport'
import { uploadImageToOSS, preloadOSSSDK } from '@/app/utils/ossUploadBrowser'
import { toast } from 'sonner'
import { RenderDesktopView, RenderMobileView } from '../../utils/viewRenderCheck'
import { useEffect } from 'react'

// import './TopBar.scss'

const TopBar = () => {
    const isMobile = useIsMobile()
    // 获取当前导出设置
    const { currentExportSettings } = useExportSettings()

    // 获取 modal 操作方法
    const { setModal, toggleModal } = useModalActions()

    // 预加载OSS SDK（组件挂载时）
    useEffect(() => {
        // 异步预加载OSS SDK，不阻塞UI渲染
        preloadOSSSDK().then(success => {
            if (success) {
                console.log('✅ OSS SDK 预加载成功')
            } else {
                console.warn('⚠️ OSS SDK 预加载失败，将在上传时动态加载')
            }
        })
    }, [])

    // 点击 Logo 事件 - 保存图片到相册
    const handleLogoClick = (imageUrl?: string) => {
        // 检查是否在小程序环境
        if (bridge.isReady()) {
            // 如果传入了图片地址，使用传入的地址；否则使用测试图片
            const finalImageUrl = imageUrl
            bridge.saveImage(finalImageUrl)
        }
    }

    // 处理OSS上传的辅助函数
    const handleOSSUpload = async (): Promise<void> => {
        // 显示OSS上传开始的加载提示
        const ossToastId = toast.loading('正在上传图片到云端...', {
            position: 'top-center',
        })

        try {
            console.log('🔄 开始OSS上传...')

            // 步骤1: 导出图片为Blob
            const blobResult = await exportImageAsBlob()
            if (!blobResult.success || !blobResult.blob) {
                toast.dismiss(ossToastId)
                toast.error('图片导出失败，无法上传到云端', {
                    description: blobResult.error || '未知错误',
                    position: 'top-center',
                    duration: 4000,
                })
                console.error('❌ OSS上传失败 - 图片导出失败:', blobResult.error)
                return
            }

            // 步骤2: 上传到OSS
            toast.loading('正在上传到阿里云OSS...', { id: ossToastId })
            const uploadResult = await uploadImageToOSS(blobResult.blob)

            if (uploadResult.success && uploadResult.url) {
                // 上传成功
                toast.dismiss(ossToastId)
                toast.success('图片已成功上传到云端！', {
                    description: `图片尺寸: ${blobResult.actualSize?.width}×${blobResult.actualSize?.height}`,
                    position: 'top-center',
                    duration: 6000,
                    action: {
                        label: '复制链接',
                        onClick: () => {
                            navigator.clipboard.writeText(uploadResult.url!)
                            toast.success('自定义域名链接已复制到剪贴板')
                        },
                    },
                })

                console.log('✅ OSS上传成功，自定义域名可直接访问:', uploadResult.url)

                // OSS上传成功后，调用 handleLogoClick 保存图片到小程序相册
                handleLogoClick(uploadResult.url)
            } else {
                // 上传失败
                toast.dismiss(ossToastId)
                toast.error('图片上传到云端失败', {
                    description: uploadResult.error || '未知错误',
                    position: 'top-center',
                    duration: 4000,
                })
                console.error('❌ OSS上传失败:', uploadResult.error)
            }
        } catch (error) {
            console.error('❌ OSS上传过程中发生意外错误:', error)
            toast.dismiss(ossToastId)
            toast.error('上传过程中发生错误', {
                description: error instanceof Error ? error.message : '未知错误',
                position: 'top-center',
                duration: 4000,
            })
        }
    }

    // 点击导出按钮事件（集成OSS上传功能）
    const handleExportClick = async () => {
        console.log('开始导出图片并上传到OSS...')

        try {
            // 并行执行本地下载和OSS上传，确保两个功能互不影响
            const [localDownloadPromise, ossUploadPromise] = await Promise.allSettled([
                // 本地下载（保持原有功能不变）
                exportImage(),
                // OSS上传（新增功能）
                handleOSSUpload(),
            ])

            // 处理本地下载结果
            if (localDownloadPromise.status === 'fulfilled') {
                const downloadSuccess = localDownloadPromise.value
                if (downloadSuccess) {
                    console.log('✅ 本地下载成功')
                    // 导出成功后显示成功弹窗（保持原有逻辑）
                    setModal(ModalKey.SuccessPopover, true)
                } else {
                    console.error('❌ 本地下载失败')
                }
            } else {
                console.error('❌ 本地下载过程中发生错误:', localDownloadPromise.reason)
            }

            // 处理OSS上传结果
            if (ossUploadPromise.status === 'rejected') {
                console.error('❌ OSS上传过程中发生错误:', ossUploadPromise.reason)
            }
        } catch (error) {
            console.error('❌ 整体导出过程中发生意外错误:', error)
            // 这里的错误处理主要是保底，实际的错误应该在各自的函数中处理
            toast.error('导出过程中发生意外错误', {
                description: error instanceof Error ? error.message : '未知错误',
                position: 'top-center',
                duration: 4000,
            })
        }
    }

    // 原有的导出按钮事件（已被上面的新函数替换，保留作为备份注释）
    /*
    const handleExportClickOriginal = async () => {
        console.log('开始导出图片...')
        try {
            await exportImage()
            console.log('图片导出成功')
            // 导出成功后显示成功弹窗
            setModal(ModalKey.SuccessPopover, true)
        } catch (error) {
            console.error('图片导出失败:', error)
        }
    }
    */

    // 点击复制按钮事件
    const handleCopyClick = async () => {
        console.log('开始复制图片到剪贴板...')
        try {
            const success = await exportToClipboard()
            if (success) {
                console.log('图片已复制到剪贴板')
                // 复制成功后显示成功弹窗
                setModal(ModalKey.SuccessPopover, true)
            } else {
                console.error('复制到剪贴板失败')
            }
        } catch (error) {
            console.error('复制图片失败:', error)
        }
    }

    // 点击选项按钮事件
    const handleOptionsClick = () => {
        console.log('options clicked')
        // 切换导出弹窗显示状态
        toggleModal(ModalKey.ExportPopover)
    }

    // 点击模版按钮事件
    const handleTemplateClick = () => {
        console.log('template clicked')
    }

    // 点击重制按钮事件
    const handleResetClick = () => {
        console.log('reset clicked')
    }

    // 点击快捷键按钮事件
    const handleQuickClick = () => {
        console.log('quick clicked')
    }

    // 点击反馈按钮事件
    const handleFeedbackClick = () => {
        console.log('feedback clicked')
    }

    // 点击重制 —— 上一步事件
    const handleUndoClick = () => {
        console.log('undo clicked')
    }

    // 点击重制 —— 下一步事件
    const handleRedoClick = () => {
        console.log('redo clicked')
    }

    return (
        <>
            {!isMobile && (
                <>
                    {/* 桌面版内容面板 */}
                    <div className='app-top-bar'>
                        {/* 左侧导航菜单区域 */}
                        <nav className='navbar'>
                            {/* Logo 和导航指示器容器 */}
                            <button
                                onClick={handleLogoClick}
                                type='button'
                                className='button icon-button medium-button undefined-button undefined-blur undefined-round undefined-active mobile-menu'
                                style={{ flexDirection: 'row' }}
                            >
                                {/* 应用 Logo 图片 */}
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    className='logo'
                                    src='/image/shots-logo.png'
                                    alt='Shots logo'
                                />
                                {/* 导航指示箭头图标 */}
                                <ChevronRight absoluteStrokeWidth />
                            </button>

                            {/* Tempalte 模版按钮 */}
                            <div className='right' onClick={handleTemplateClick}>
                                <button
                                    type='button'
                                    className='button default-button medium-button undefined-button undefined-blur undefined-round undefined-active templates-btn'
                                    style={{ flexDirection: 'row-reverse' }}
                                >
                                    <ChevronRight absoluteStrokeWidth />
                                    <span>Templates</span>
                                    <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src='/image/templates-btn.png'
                                    />
                                    {/* <img
                                        crossOrigin='anonymous'
                                        loading='lazy'
                                        decoding='async'
                                        src='https://test-mock-pix.oss-cn-hangzhou.aliyuncs.com/2025/07/27/dc2f8ead-a58e-42d4-aa2c-6d40cae08745.jpg'
                                    /> */}
                                </button>
                            </div>
                        </nav>

                        {/* 命令栏区域 - 用于搜索或执行命令 */}
                        <div className='command-bar-desktop'>
                            <div className='left'>
                                {/* 反馈按钮 */}
                                <button
                                    type='button'
                                    className='button icon-button medium-button undefined-button undefined-blur undefined-round undefined-active tip'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <BiSolidMessageError />
                                    <div className='tooltip' style={{ top: '120%', left: '-100%' }}>
                                        Send Feedback
                                    </div>
                                </button>
                            </div>
                            <div className='center'>
                                {/* 重制 —— 上一步 */}
                                <button
                                    onClick={handleFeedbackClick}
                                    type='button'
                                    className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active tip'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <RiArrowGoBackFill />
                                    <div className='tooltip' style={{ top: '120%', left: '-100%' }}>
                                        Undo
                                        <div className='keys'>
                                            <span>⌘</span>
                                            <span>Z</span>
                                        </div>
                                    </div>
                                </button>

                                {/* 重制 —— 下一步 */}
                                <button
                                    onClick={handleUndoClick}
                                    type='button'
                                    className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active tip'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <RiArrowGoForwardFill />
                                    <div className='tooltip' style={{ top: '120%', left: '-140%' }}>
                                        Redo
                                        <div className='keys'>
                                            <span>⇧</span>
                                            <span>⌘</span>
                                            <span>Z</span>
                                        </div>
                                    </div>
                                </button>

                                {/* 重制 —— 重新开始 */}
                                <div
                                    onClick={handleResetClick}
                                    className='start-new-placeholder'
                                    style={{ width: 96, height: 36 }}
                                >
                                    <div
                                        className='start-new-component'
                                        style={{
                                            width: 96,
                                            minWidth: 96,
                                            height: 36,
                                            minHeight: 36,
                                            borderRadius: 32,
                                        }}
                                    >
                                        <div
                                            id='modal-view-id'
                                            className='modal-view view-mini'
                                            style={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                width: 96,
                                                height: 36,
                                                opacity: 1,
                                                filter: 'blur(0px)',
                                            }}
                                        >
                                            <span className='h6'>Start Over</span>
                                        </div>
                                    </div>
                                </div>

                                {/* 快捷键提示 */}
                                <button
                                    onClick={handleQuickClick}
                                    type='button'
                                    className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active tip'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <RiCommandFill />
                                    <div className='tooltip' style={{ top: '120%', left: '-50%' }}>
                                        Shortcuts
                                    </div>
                                </button>

                                {/* 预览按钮 */}
                                <button
                                    type='button'
                                    className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active tip'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                        <path
                                            fill='currentColor'
                                            d='M21.149 10.407c.5 0 .851-.372.851-.853V3.486C22 2.55 21.449 2 20.507 2h-6.07c-.496 0-.878.353-.878.849 0 .495.39.844.878.844h1.33l3.626-.201-3.106 2.974-3.349 3.349a.87.87 0 0 0-.257.626c0 .527.386.871.91.871a.8.8 0 0 0 .593-.247l3.332-3.356 2.99-3.097-.197 3.613v1.329c0 .495.354.853.84.853M9.563 22c.495 0 .877-.354.877-.85-.004-.495-.39-.844-.877-.844H8.241l-3.635.201 3.106-2.974 3.349-3.345a.84.84 0 0 0 .253-.629c0-.528-.382-.872-.906-.872a.82.82 0 0 0-.593.247L6.492 16.29l-2.989 3.097.188-3.612v-1.33c0-.495-.345-.853-.841-.853-.5 0-.85.372-.85.853v6.068C2 21.449 2.55 22 3.492 22z'
                                        />
                                    </svg>
                                    <div className='tooltip' style={{ top: '120%', left: '-50%' }}>
                                        Preview
                                    </div>
                                </button>
                            </div>

                            {/* 是否开启性能模式 */}
                            <div className='right'>
                                <div>
                                    <button
                                        type='button'
                                        className='button icon-button medium-button undefined-button undefined-blur true-round undefined-active'
                                        style={{ flexDirection: 'row' }}
                                    >
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M1 12c0 6.059 4.938 11 11.004 11C18.061 23 23 18.059 23 12S18.061 1 12.004 1C5.938 1 1 5.94 1 12m16.293-4.707-3.795 5.563c-.34.52-.859.875-1.494.875-.961 0-1.74-.77-1.74-1.731 0-.588.294-1.102.75-1.423l5.584-3.98c.515-.37 1.046.161.695.696'
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* 导出功能区域 */}
                        <div className='export-button-placeholder'>
                            {/* 复制按钮 */}
                            <div onClick={handleCopyClick} className='copy-button-wrapper'>
                                <button
                                    type='button'
                                    className='button icon-button undefined-button undefined-button undefined-blur undefined-round undefined-active copy-button'
                                    style={{ flexDirection: 'row' }}
                                >
                                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                        <g fill='currentColor' fillRule='evenodd'>
                                            <path d='M11.82 6.02h.2c.16 0 .32.06.43.18l4.07 4.24c.1.11.16.26.16.41v7.48c0 1.98-1.62 3.61-3.6 3.64H6.49c-1.99-.04-3.58-1.68-3.54-3.66V9.59c.04-2 1.69-3.61 3.67-3.61l5.04-.01c.03-.01.06-.01.1-.01Zm-.61 1.21H6.66c-1.34 0-2.44 1.07-2.48 2.41v8.71a2.376 2.376 0 0 0 2.34 2.43l.11-.01h6.44a2.44 2.44 0 0 0 2.39-2.44l-.01-6.73h-1.62a2.684 2.684 0 0 1-2.67-2.68l-.01-1.73Zm1.2.67v1.05c0 .8.65 1.46 1.46 1.46l.95-.01-2.42-2.52Z' />
                                            <path d='M15.949 2c-.04-.01-.07-.01-.11-.01h-5.16c-1.99 0-3.63 1.6-3.68 3.6V6.8h1.2V5.6a2.48 2.48 0 0 1 2.47-2.42l4.54-.01v1.72c0 1.47 1.19 2.67 2.66 2.67l1.61-.01v6.72c0 1.32-1.08 2.41-2.4 2.43H15.8v1.2l1.27-.01a3.67 3.67 0 0 0 3.596-3.65V6.76c0-.16-.07-.31-.17-.42l-4.08-4.25a.63.63 0 0 0-.44-.19l-.11-.01Zm.5 2.93-.01-1.06 2.41 2.51h-.96c-.81-.01-1.47-.67-1.47-1.47Z' />
                                        </g>
                                    </svg>
                                </button>
                            </div>

                            {/* 导出设置按钮 */}
                            <button
                                type='button'
                                className='button icon-button undefined-button undefined-button undefined-blur undefined-round undefined-active export-settings-btn'
                                onClick={handleOptionsClick}
                                style={{ flexDirection: 'row' }}
                            >
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        fillRule='evenodd'
                                        d='M17.38 13.7c1.71 0 3.11 1.38 3.11 3.09 0 1.7-1.4 3.09-3.12 3.09s-3.12-1.39-3.12-3.1 1.39-3.1 3.11-3.1Zm0 1.5c-.89 0-1.62.71-1.62 1.59s.72 1.59 1.61 1.59c.88 0 1.61-.72 1.61-1.6s-.73-1.6-1.62-1.6Zm-7.31.88a.749.749 0 1 1 0 1.5H3.76c-.42 0-.75-.34-.75-.75 0-.42.33-.75.75-.75h6.3ZM6.1 3.98c1.71 0 3.11 1.39 3.11 3.09s-1.4 3.09-3.12 3.09-3.12-1.388-3.12-3.1c0-1.71 1.39-3.1 3.11-3.1Zm0 1.5c-.89 0-1.62.71-1.62 1.59s.72 1.59 1.61 1.59 1.61-.72 1.61-1.6c0-.89-.73-1.6-1.62-1.6Zm13.07.9a.749.749 0 1 1 0 1.5h-6.3c-.42 0-.75-.34-.75-.75 0-.42.33-.75.75-.75z'
                                    />
                                </svg>
                            </button>

                            {/* 直接导出区域 */}
                            <div
                                onClick={handleExportClick}
                                className='export-button export-state'
                                style={{ pointerEvents: 'auto', opacity: 1, borderRadius: 14 }}
                            >
                                <div className='export-button-safe-area'>
                                    <div
                                        id='modal-view-id'
                                        className='modal-view export-state'
                                        style={{ opacity: 1, filter: 'blur(0px)' }}
                                    >
                                        <div className='icon'>
                                            <svg
                                                xmlns='http://www.w3.org/2000/svg'
                                                viewBox='0 0 24 24'
                                            >
                                                <path
                                                    fill='currentColor'
                                                    d='M12.058 22c-.631 0-1.087-.443-1.087-1.074V7.533l.134-3.303.684.241L7.72 8.998l-1.921 1.867c-.188.188-.47.295-.752.295C4.442 11.16 4 10.69 4 10.1c0-.282.106-.538.334-.793l6.919-6.932c.228-.241.51-.375.805-.375s.578.134.806.375l6.918 6.932c.228.255.335.511.335.793 0 .59-.443 1.06-1.047 1.06-.282 0-.564-.107-.752-.295l-1.921-1.867-4.084-4.527.699-.241.134 3.303v13.393c0 .631-.456 1.074-1.088 1.074'
                                                />
                                            </svg>
                                        </div>
                                        <h6>export</h6>
                                        <span>
                                            {currentExportSettings.quality.scaleMultiplierLabel} ·{' '}
                                            {currentExportSettings.format}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            )}

            {isMobile && (
                <>
                    {/* 移动版内容面板 */}
                    {/* 顶部导航栏区域 */}
                    <nav className='navbar'>
                        {/* 左侧导航菜单区域 */}
                        <div className='left'>
                            {/* Logo 和导航指示器容器 */}
                            <button
                                onClick={handleLogoClick}
                                type='button'
                                className='button icon-button medium-button undefined-button undefined-blur undefined-round undefined-active mobile-menu'
                                style={{ flexDirection: 'row' }}
                            >
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    className='logo'
                                    alt='Shots logo'
                                    src='/image/shots-logo.png'
                                />
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        d='M17.179 12.168a.95.95 0 0 0-.293-.692L9.644 4.281A.9.9 0 0 0 8.964 4c-.55 0-.972.41-.972.96 0 .258.117.504.281.692l6.551 6.516-6.551 6.515a1.05 1.05 0 0 0-.281.692c0 .55.422.96.972.96a.9.9 0 0 0 .68-.281l7.242-7.195a.95.95 0 0 0 .293-.691'
                                    />
                                </svg>
                            </button>
                        </div>

                        {/* 中间区域 */}
                        <div className='center'>
                            <div
                                className='start-new-placeholder'
                                style={{ width: 80, height: 36 }}
                            >
                                {/* 重制 —— 重新开始 */}
                                <div
                                    onClick={handleResetClick}
                                    className='start-new-component'
                                    style={{
                                        width: 80,
                                        minWidth: 80,
                                        height: 36,
                                        minHeight: 36,
                                        borderRadius: 38,
                                    }}
                                >
                                    <div
                                        id='modal-view-id'
                                        className='modal-view view-mini'
                                        style={{
                                            position: 'absolute',
                                            top: 0,
                                            left: 0,
                                            width: 80,
                                            height: 36,
                                            opacity: 1,
                                            filter: 'blur(0px)',
                                        }}
                                    >
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M4 11.664a.96.96 0 0 0 .949.949h5.765v5.765c0 .504.434.95.95.95.515 0 .937-.446.937-.95v-5.765h5.777a.96.96 0 0 0 .938-.949.96.96 0 0 0-.938-.95h-5.777V4.949c0-.504-.422-.949-.937-.949a.97.97 0 0 0-.95.949v5.765H4.949a.96.96 0 0 0-.949.95'
                                            />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 右侧区域 */}
                        <div className='right'>
                            {/* 导出设置按钮 */}
                            <div className='dropdown mobile-export-settings-drop'>
                                <div onClick={handleOptionsClick} className='button-wrapper'>
                                    <button
                                        type='button'
                                        className='button icon-button small-button secondary-button undefined-blur true-round undefined-active '
                                        style={{ flexDirection: 'row' }}
                                    >
                                        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                            <path
                                                fill='currentColor'
                                                d='M18.56 14.01c1.9 0 3.43 1.51 3.43 3.37s-1.54 3.37-3.44 3.37-3.44-1.52-3.44-3.38c0-1.87 1.54-3.38 3.43-3.38Zm-8.48 1.93c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48H3.49c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57ZM5.43 2.98c1.89 0 3.43 1.51 3.43 3.37S7.32 9.72 5.42 9.72 1.98 8.2 1.98 6.34c0-1.87 1.53-3.38 3.43-3.38Zm15.05 1.89c.83 0 1.5.66 1.5 1.48 0 .81-.68 1.48-1.51 1.48h-6.58c-.84 0-1.51-.67-1.51-1.49s.67-1.49 1.5-1.49h6.57Z'
                                            />
                                        </svg>
                                    </button>
                                </div>

                                {/* 导出设置弹窗 */}
                                {isMobile && <ExportPopver />}
                            </div>
                            {/* 直接导出区域 */}
                            <button
                                onClick={handleExportClick}
                                type='button'
                                className='button default-button small-button primary-button undefined-blur true-round undefined-active undefined'
                                style={{ flexDirection: 'row', minWidth: 72 }}
                            >
                                <span>Export</span>
                            </button>
                        </div>
                    </nav>

                    {/* 移动版命令栏区域 */}
                    <div className='command-bar-mobile'>
                        <div className='left' />
                        <div className='center'>
                            {/* 重制 —— 上一步 */}
                            <button
                                onClick={handleUndoClick}
                                type='button'
                                className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active undefined'
                                style={{ flexDirection: 'row' }}
                            >
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        fillOpacity='0.85'
                                        d='M22 14.994c0-4.042-2.741-6.836-7.567-6.836H7.322l-3.28.148.19.487L6.846 6.56l3.058-2.984a.9.9 0 0 0 .254-.645c0-.551-.37-.931-.942-.931-.222 0-.497.116-.677.296L2.317 8.402A.96.96 0 0 0 2 9.111a.95.95 0 0 0 .317.698l6.222 6.106c.18.19.455.296.677.296.572 0 .942-.381.942-.931a.9.9 0 0 0-.254-.645l-3.058-2.974-2.614-2.244-.19.487 3.28.148h7.27c3.609 0 5.513 1.99 5.513 4.836 0 2.858-1.904 4.974-5.513 4.974h-2.434c-.582 0-.963.423-.963.942s.381.942.963.942h2.445c4.73 0 7.397-2.699 7.397-6.752'
                                    />
                                </svg>
                            </button>

                            {/* 重制 —— 下一步 */}
                            <button
                                onClick={handleRedoClick}
                                type='button'
                                className='button icon-button small-button undefined-button undefined-blur undefined-round undefined-active undefined'
                                style={{ flexDirection: 'row' }}
                            >
                                <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                    <path
                                        fill='currentColor'
                                        fillOpacity='0.85'
                                        d='M2 14.994c0-4.042 2.74-6.836 7.566-6.836h7.111l3.28.148-.19.487-2.614-2.233-3.058-2.984a.9.9 0 0 1-.254-.645c0-.551.37-.931.942-.931.222 0 .497.116.677.296l6.222 6.106a.96.96 0 0 1 .318.709.95.95 0 0 1-.318.698l-6.222 6.106a1 1 0 0 1-.677.296c-.572 0-.942-.381-.942-.931 0-.275.095-.476.254-.645l3.058-2.974 2.614-2.244.19.487-3.28.148h-7.27c-3.609 0-5.513 1.99-5.513 4.836 0 2.858 1.904 4.974 5.513 4.974h2.434c.582 0 .963.423.963.942s-.381.942-.963.942H9.396C4.666 21.746 2 19.047 2 14.994'
                                    />
                                </svg>
                            </button>
                        </div>
                        <div className='right' />
                    </div>
                </>
            )}
        </>
    )
}

export default TopBar
