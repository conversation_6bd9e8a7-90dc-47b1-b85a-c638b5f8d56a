/**
 * @file app/components/DisplayContainer/DisplayConfig.ts
 * @description 该文件作为布局配置的单一数据源。
 * 它定义了所有共享的类型接口（DeviceConfig, LayoutConfig）、枚举（LayoutType），
 * 以及所有可用的设备布局配置（单设备、双设备、三设备）。
 * 其他组件（如 CanvasContainer 和 MobileMockup_Layout）将从此文件导入配置，
 * 以确保数据的一致性和可维护性。
 */

/**
 * @interface TransformConfig
 * @description 定义变换配置的对象结构。
 * 支持CSS transform的所有基础变换属性。
 * @property {number} [perspective] - 透视距离，单位为em
 * @property {{x: number, y: number}} [translate] - 平移变换，x和y的百分比值
 * @property {number} [scale] - 缩放比例
 * @property {{x: number, y: number, z: number}} [rotate] - 旋转角度，单位为度
 * @property {{x: number, y: number}} [skew] - 倾斜角度，单位为度
 */
export interface TransformConfig {
    perspective?: number
    translate?: { x: number; y: number }
    scale?: number
    rotate?: { x: number; y: number; z: number }
    skew?: { x: number; y: number }
}

/**
 * @interface DeviceConfig
 * @description 定义单个设备在布局中的详细配置。
 * 这是最基础的配置单元。
 * @property {TransformConfig} transform - 设备的变换配置对象。
 * @property {string} [filter] - 作用于设备的可选 CSS filter 样式。
 * @property {number | 'unset'} [zIndex] - 设备的可选 z-index 值。
 * @property {string} [imageSource] - 设备显示屏中要展示的图片URL（可选）。
 */
export interface DeviceConfig {
    transform: TransformConfig
    filter?: string
    zIndex?: number | 'unset'
    imageSource?: string
}

/**
 * @interface DeviceLayoutTypeInterface
 * @description 定义布局设备数量类型的接口
 * @property {LayoutType} type - 布局类型，区分单设备、双设备、三设备布局
 */
export interface DeviceLayoutTypeInterface {
    type: LayoutType
}

/**
 * @interface LayoutConfig
 * @description 定义一个完整的设备布局配置。
 * 它包含一个或多个 DeviceConfig。
 * @property {number} id - 布局的唯一标识符。
 * @property {string} name - 布局的名称。
 * @property {TransformConfig} [componentTransform] - 应用于整个布局容器的变换配置对象。
 * @property {string} aspectRatio - 布局的宽高比。
 * @property {DeviceConfig[]} devices - 布局中包含的设备配置数组。
 */
export interface LayoutConfig {
    id: number
    name: string
    componentTransform?: TransformConfig
    aspectRatio: string
    devices: DeviceConfig[]
}

/**
 * @interface ExtendedLayoutConfig
 * @description 扩展 LayoutConfig，添加设备类型标识
 * 继承所有 LayoutConfig 属性，并添加 type 字段用于标识布局的设备数量
 * @property {LayoutType} type - 布局类型标识符，Single/Dual/Triple
 */
export interface ExtendedLayoutConfig extends LayoutConfig, DeviceLayoutTypeInterface {}

/**
 * @enum LayoutType
 * @description 定义布局的类型，用于区分单设备、双设备、三设备布局组。
 */
export enum LayoutType {
    Single = 'single',
    Dual = 'dual',
    Triple = 'triple',
}

/**
 * @const defaultTransform
 * @description 默认的变换配置对象，用于初始化设备和组件的变换状态
 * 包含零位移、标准缩放和零旋转，这是最常用的基础变换配置
 */
export const defaultTransform: TransformConfig = {
    translate: { x: 0, y: 0 },
    scale: 1,
    rotate: { x: 0, y: 0, z: 0 },
}

/**
 * @const defaultDeviceTransform
 * @description 默认的设备变换配置对象，包含透视效果和零倾斜
 * 适用于大多数设备的基础3D展示效果
 */
export const defaultDeviceTransform: TransformConfig = {
    perspective: 200,
    translate: { x: 0, y: 0 },
    scale: 1,
    rotate: { x: 0, y: 0, z: 0 },
    skew: { x: 0, y: 0 },
}

const defaultComtransform: TransformConfig = {
    translate: { x: 0, y: 0 },
    scale: 1,
    rotate: { x: 0, y: 0, z: 0 },
}

/**
 * @const singleDeviceLayoutConfigs
 * @description 单设备布局配置数组。
 * 每个配置项都包含 type 字段，值为 LayoutType.Single，标识为单设备布局
 */
export const singleDeviceLayoutConfigs: ExtendedLayoutConfig[] = [
    {
        id: 1,
        name: '默认视图',
        type: LayoutType.Single,
        componentTransform: defaultComtransform,
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
                zIndex: 1,
            },
        ],
    },
    {
        id: 2,
        name: '倾斜视图',
        type: LayoutType.Single,
        componentTransform: defaultComtransform,
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: -8 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 3,
        name: '顶部放大',
        type: LayoutType.Single,
        componentTransform: {
            translate: { x: 0, y: -40 },
            scale: 1.8,
            rotate: { x: 0, y: 0, z: 0 },
        },
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 4,
        name: '顶部进一步放大',
        type: LayoutType.Single,
        componentTransform: {
            translate: { x: 0, y: -66 },
            scale: 2.5,
            rotate: { x: 0, y: 0, z: 0 },
        },
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 5,
        name: '底部放大',
        type: LayoutType.Single,
        componentTransform: {
            translate: { x: 0, y: 40 },
            scale: 1.8,
            rotate: { x: 0, y: 0, z: 0 },
        },
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 6,
        name: '底部进一步放大',
        type: LayoutType.Single,
        componentTransform: {
            translate: { x: 0, y: 66 },
            scale: 2.5,
            rotate: { x: 0, y: 0, z: 0 },
        },
        aspectRatio: '0.461373 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 1,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
]

/**
 * @const dualDeviceLayoutConfigs
 * @description 用于渲染双设备预览的配置数组。
 * 每个配置项都包含 type 字段，值为 LayoutType.Dual，标识为双设备布局
 */
export const dualDeviceLayoutConfigs: ExtendedLayoutConfig[] = [
    {
        id: 1,
        name: 'Double Default',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -30, y: 0 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 30, y: 0 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 2,
        name: 'Double Offset',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -30, y: -5 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 30, y: 5 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 3,
        name: 'Double Offset 2',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -18, y: -4 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 18, y: 4 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 4,
        name: 'Double Rotated',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -25, y: 0 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: -10 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 25, y: 0 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 10 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 5,
        name: 'Double Rotated Offset',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -21, y: -6 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: -10 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 21, y: 6 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 10 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 6,
        name: 'Double Mixed',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -17.5, y: -2.5 },
                    scale: 0.3,
                    rotate: { x: 0, y: 0, z: -10 },
                    skew: { x: 0, y: 0 },
                },
                filter: 'blur(0.7em)',
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 19.8, y: 0.2 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: 7 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 7,
        name: 'Double Rotated 2',
        type: LayoutType.Dual,
        componentTransform: defaultComtransform,
        aspectRatio: '0.922747 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -20, y: -5 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: -16 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 20, y: 5 },
                    scale: 0.4,
                    rotate: { x: 0, y: 0, z: -16 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
]

/**
 * @const tripleDeviceLayoutConfigs
 * @description 用于渲染三设备预览的配置数组。
 * 每个配置项都包含 type 字段，值为 LayoutType.Triple，标识为三设备布局
 */
export const tripleDeviceLayoutConfigs: ExtendedLayoutConfig[] = [
    {
        id: 1,
        name: 'Three Default',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -38, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 38, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 2,
        name: 'Three Offset',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -38, y: 3 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 38, y: -3 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 3,
        name: 'Three Center Front',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -26, y: 1 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: -8 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.297,
                    rotate: { x: 0, y: 0, z: 0 },
                    skew: { x: 0, y: 0 },
                },
                zIndex: 1,
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 26, y: 1 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 8 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 4,
        name: 'Three Mixed',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -26, y: 1.5 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: -12 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.297,
                    rotate: { x: 0, y: 0, z: -4 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 29, y: 0 },
                    scale: 0.324,
                    rotate: { x: 0, y: 0, z: 4 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 5,
        name: 'Three Mixed 2',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -24, y: 5 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 4 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: -4 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 22, y: -6 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: -12 },
                    skew: { x: 0, y: 0 },
                },
            },
        ],
    },
    {
        id: 6,
        name: 'Three Mixed Blur',
        type: LayoutType.Triple,
        componentTransform: defaultComtransform,
        aspectRatio: '1.38412 / 1',
        devices: [
            {
                transform: {
                    perspective: 200,
                    translate: { x: -18, y: 1 },
                    scale: 0.2,
                    rotate: { x: 0, y: 0, z: -10 },
                    skew: { x: 0, y: 0 },
                },
                filter: 'blur(1em)',
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 0, y: 0 },
                    scale: 0.27,
                    rotate: { x: 0, y: 0, z: 6 },
                    skew: { x: 0, y: 0 },
                },
            },
            {
                transform: {
                    perspective: 200,
                    translate: { x: 25, y: 7 },
                    scale: 0.3,
                    rotate: { x: 0, y: 0, z: -6 },
                    skew: { x: 0, y: 0 },
                },
                filter: 'blur(0.6em)',
            },
        ],
    },
]

/**
 * @description 将 TransformConfig 对象转换为 CSS transform 字符串
 *
 * 转换规则：
 * - perspective: 200 → perspective(200em)
 * - translate: {x: 10, y: 20} → translate(10%, 20%)
 * - scale: 1.5 → scale(1.5)
 * - rotate: {x: 10, y: 20, z: 30} → rotateX(10deg) rotateY(20deg) rotateZ(30deg)
 * - skew: {x: 5, y: 10} → skewX(5deg) skewY(10deg)
 *
 * @param {TransformConfig} config - 变换配置对象
 * @returns {string} CSS transform 字符串
 *
 * @example
 * // 输入: { translate: { x: 0, y: -40 }, scale: 1.8, rotate: { x: 0, y: 0, z: 0 } }
 * // 输出: "translate(0%, -40%) scale(1.8) rotateX(0deg) rotateY(0deg) rotateZ(0deg)"
 */
export function transformConfigToCSSString(config: TransformConfig): string {
    const transformParts: string[] = []

    // 透视变换 - 例如：perspective: 200 → perspective(200em)
    if (config.perspective !== undefined) {
        transformParts.push(`perspective(${config.perspective}em)`)
    }

    // 平移变换 - 例如：translate: {x: 10, y: 20} → translate(10%, 20%)
    if (config.translate) {
        transformParts.push(`translate(${config.translate.x}%, ${config.translate.y}%)`)
    }

    // 缩放变换 - 例如：scale: 1.5 → scale(1.5)
    if (config.scale !== undefined) {
        transformParts.push(`scale(${config.scale})`)
    }

    // 旋转变换 - 例如：rotate: {x: 10, y: 20, z: 30} → rotateX(10deg) rotateY(20deg) rotateZ(30deg)
    if (config.rotate) {
        transformParts.push(`rotateX(${config.rotate.x}deg)`)
        transformParts.push(`rotateY(${config.rotate.y}deg)`)
        transformParts.push(`rotateZ(${config.rotate.z}deg)`)
    }

    // 倾斜变换 - 例如：skew: {x: 5, y: 10} → skewX(5deg) skewY(10deg)
    if (config.skew) {
        transformParts.push(`skewX(${config.skew.x}deg)`)
        transformParts.push(`skewY(${config.skew.y}deg)`)
    }

    return transformParts.join(' ')
}
