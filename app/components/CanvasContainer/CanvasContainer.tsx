import { useIsMobile, useCalculatedDimensions } from '../../hooks/useAppState'
import { useActiveLayout } from '../../features/viewDimensions/utils/重构/状态管理'
import DisplayContainer from '../DisplayContainer/DisplayContainer'
import {
    LayoutConfig,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs,
    tripleDeviceLayoutConfigs,
    LayoutType,
} from '../DisplayContainer/DisplayConfig'

/**
 * @interface CanvasContainerProps
 * @description CanvasContainer 组件的属性
 * @property {(isActive: boolean) => void} [setIsActiveMediaPickerModal] - 设置媒体选择器弹窗状态的函数（可选）
 */
interface CanvasContainerProps {
    setIsActiveMediaPickerModal?: (isActive: boolean) => void
}

/**
 * @component CanvasContainer
 * @description 主画布容器组件。
 * 它的核心职责是从Zustand Store获取当前激活的布局配置，并将其传递给核心渲染引擎 `DisplayContainer`。
 */
const CanvasContainer = ({ setIsActiveMediaPickerModal }: CanvasContainerProps) => {
    // 从Zustand Store获取当前激活的布局状态
    const activeLayout = useActiveLayout()
    // 获取移动设备状态，虽然当前未使用，但保留以便未来扩展。
    const isMobile = useIsMobile()

    // 通过 hook 获取计算后的尺寸信息。
    // 注意：这里的尺寸主要用于设置 `frame` div 的宽高，确保画布容器本身尺寸正确。
    // 具体的尺寸有效性检查和渲染，已全权交由子组件 `DisplayContainer` 处理。
    const { calculatedWidth, calculatedHeight } = useCalculatedDimensions()

    // 将计算出的尺寸赋值给变量，用于 style 属性。
    const actualWidth = calculatedWidth
    const actualHeight = calculatedHeight

    // 添加尺寸有效性检查
    const isValidDimensions = !!(actualWidth && actualHeight && actualWidth > 0 && actualHeight > 0)

    // 调试信息 - 确认 CanvasContainer 成功获取了尺寸。
    console.log('✅ Canvas尺寸获取状态:', {
        isMobile,
        calculatedWidth,
        calculatedHeight,
        actualWidth,
        actualHeight,
        isValidDimensions,
        hasCalculatedDimensions: isValidDimensions,
    })

    // 如果尺寸无效，显示加载状态
    if (!isValidDimensions) {
        console.log('⏳ Canvas尺寸未准备好，显示加载状态')
        return (
            <div className='canvas'>
                <div className='canvas-safe-area'>
                    <div
                        className='frame preview-frame'
                        style={{
                            width: 400,
                            height: 300,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f8f9fa',
                            border: '2px dashed #dee2e6',
                            borderRadius: '8px',
                        }}
                    >
                        <div style={{ textAlign: 'center', color: '#6c757d' }}>
                            <div>⏳ 正在计算画布尺寸...</div>
                            <div style={{ fontSize: '12px', marginTop: '8px' }}>
                                宽度: {calculatedWidth || '未定义'} | 高度:{' '}
                                {calculatedHeight || '未定义'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    /**
     * @function getActiveLayoutConfig
     * @description 根据传入的布局对象，从配置数组中查找并返回完整的布局配置。
     * @param {{type: LayoutType, id: number}} layout - 需要查找的布局对象。
     * @returns {LayoutConfig} 返回找到的布局配置。如果找不到，则返回一个默认配置。
     */
    const getActiveLayoutConfig = (layout: { type: LayoutType; id: number }): LayoutConfig => {
        let baseConfig: LayoutConfig | undefined

        // 1. 根据布局类型，在对应的配置数组中查找匹配的 ID。
        if (layout.type === LayoutType.Single) {
            baseConfig = singleDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Dual) {
            baseConfig = dualDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Triple) {
            baseConfig = tripleDeviceLayoutConfigs.find(c => c.id === layout.id)
        }

        // 2. 防御性编程：如果找不到配置，打印警告并返回一个安全的默认值。
        if (!baseConfig) {
            console.warn(
                `在 DisplayConfig.ts 中未找到布局 ${layout.type}-${layout.id} 的配置，将使用默认布局。`,
            )
            // 返回单设备布局的第一个作为默认值
            return singleDeviceLayoutConfigs[0]
        }

        // 3. 深拷贝基础配置以避免意外修改原始配置数组，这对于共享数据至关重要。
        return JSON.parse(JSON.stringify(baseConfig))
    }

    // 根据从 props 接收的 activeLayout 获取最终的布局配置
    const activeLayoutConfig = getActiveLayoutConfig(activeLayout)

    return (
        <div className='canvas'>
            <div className='canvas-safe-area'>
                <div
                    className='frame preview-frame'
                    style={{
                        width: actualWidth,
                        height: actualHeight,
                    }}
                >
                    <div id='shots-canvas-player' className='frame-content'>
                        {Array.from({ length: 5 }, (_, index) => (
                            <audio
                                key={index}
                                preload='metadata'
                                src='data:audio/mp3;base64,/+MYxAAJcAV8AAgAABn//////+/gQ5BAMA+D4Pg+BAQBAEAwD4Pg+D4EBAEAQDAPg++hYBH///hUFQVBUFREDQNHmf///////+MYxBUGkAGIMAAAAP/29Xt6lUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV/+MYxDUAAANIAAAAAFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV'
                            />
                        ))}

                        <div
                            style={{
                                position: 'relative',
                                overflow: 'hidden',
                                width: actualWidth,
                                height: actualHeight,
                                opacity: 1,
                            }}
                        >
                            <div
                                style={{
                                    width: actualWidth,
                                    height: actualHeight,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    overflow: 'hidden',
                                }}
                            >
                                {/* 
                                  渲染核心显示组件。
                                  我们只需要将最终选择好的"布局结构"传递给它即可。
                                */}
                                <DisplayContainer
                                    layoutConfig={activeLayoutConfig}
                                    setIsActiveMediaPickerModal={setIsActiveMediaPickerModal}
                                    isUseCanvasView={true}
                                />
                            </div>
                        </div>

                        <div
                            id='shots-watermark-container'
                            className='custom-watermark-display'
                            style={{ background: 'none' }}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default CanvasContainer
