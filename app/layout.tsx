import type { Metadata } from 'next'
import Script from 'next/script'
// import './css/globals.scss'
import './css/defaultScss/defaultMain.scss'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
    title: '加加加=油💪🏻💪🏻💪🏻',
    description: '',
}

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode
}>) {
    // 判断是否应该加载 VConsole
    const isDev = process.env.NODE_ENV === 'development'
    const shouldLoadVConsole = isDev
    
    return (
        <html lang='zh-CN'>
            <head>
                {shouldLoadVConsole && (
                    <Script
                        id='vconsole-init'
                        strategy='beforeInteractive'
                        dangerouslySetInnerHTML={{
                            __html: `
                                // VConsole 早期初始化 - 确保在所有其他脚本之前执行
                                (function() {
                                    // 创建 script 标签动态加载 VConsole
                                    var script = document.createElement('script');
                                    script.src = 'https://unpkg.com/vconsole@3.15.1/dist/vconsole.min.js';
                                    
                                    script.onload = function() {
                                        // VConsole 加载完成后立即初始化
                                        window.vConsole = new window.VConsole({
                                            theme: 'dark',
                                            maxLogNumber: 1000,
                                            onReady: function() {
                                                console.log('[VConsole] 调试工具已初始化');
                                                console.log('[VConsole] 环境: 开发环境');
                                                console.log('[VConsole] 时间:', new Date().toLocaleString());
                                            }
                                        });
                                        
                                        // 添加自定义调试面板
                                        if (window.VConsole && window.VConsole.VConsolePlugin) {
                                            var myPlugin = new window.VConsole.VConsolePlugin('debug_info', '调试信息');
                                            
                                            myPlugin.on('renderTab', function(callback) {
                                                var html = [
                                                    '<div style="padding: 10px;">',
                                                    '<h4 style="margin: 10px 0;">环境信息</h4>',
                                                    '<p>当前域名: ' + location.hostname + '</p>',
                                                    '<p>页面路径: ' + location.pathname + '</p>',
                                                    '<p>查询参数: ' + location.search + '</p>',
                                                    '<p>用户代理: ' + navigator.userAgent + '</p>',
                                                    '<p>屏幕尺寸: ' + window.innerWidth + ' x ' + window.innerHeight + '</p>',
                                                    '<p>设备像素比: ' + window.devicePixelRatio + '</p>',
                                                    '<p>网络状态: ' + (navigator.onLine ? '在线' : '离线') + '</p>',
                                                    '</div>'
                                                ].join('');
                                                callback(html);
                                            });
                                            
                                            window.vConsole.addPlugin(myPlugin);
                                        }
                                    };
                                    
                                    script.onerror = function() {
                                        console.error('[VConsole] 加载失败');
                                    };
                                    
                                    // 尽早插入到 head 中
                                    document.head.appendChild(script);
                                })();
                            `,
                        }}
                    />
                )}
            </head>
            <body>
                {children}
                <Toaster position='top-right' richColors closeButton duration={4000} />
            </body>
        </html>
    )
}
