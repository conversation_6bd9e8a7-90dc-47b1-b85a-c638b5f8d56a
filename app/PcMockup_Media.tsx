/**
 * @fileoverview PC端图片媒体选择器组件
 * @description 专为PC端设计的图片上传和管理界面，集成设备预览和拖拽功能
 *
 * 核心功能：
 * - 🖱️ 高级拖拽：支持精确的设备级拖拽绑定
 * - 📱 设备预览：实时显示各设备的图片绑定状态
 * - ⚡ 智能交互：点击空设备上传，点击有图设备管理
 * - 🎯 布局感知：动态适应单/双/三设备布局
 * - 🔄 状态同步：与主画布和媒体管理器实时同步
 *
 * 技术特点：
 * - 基于react-dropzone的多区域拖拽系统
 * - 动态设备数量渲染（1-3个设备）
 * - 智能点击逻辑：有图弹窗，无图上传
 * - 布局配置驱动的设备渲染
 *
 * 与其他组件的协作：
 * - DisplayContainer: 共享图片数据和拖拽状态
 * - Public_MediaPickerModal: 图片管理和绑定操作
 * - 状态管理: 统一的图片绑定和设备选择逻辑
 *
 * <AUTHOR> Team
 * @version 2.0.0
 * @since 2025.01
 */

import { useState, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { useImageStore, getImageForDevice } from './ImageMange/imageMangeIndex'
import {
    LayoutConfig,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs,
    tripleDeviceLayoutConfigs,
    LayoutType,
} from './components/DisplayContainer/DisplayConfig'
import { HTML_ACCEPT_ATTRIBUTES } from './ImageMange/imageConfig'
import { useActiveLayout } from './features/viewDimensions/utils/重构/状态管理'

/**
 * @interface PcMockup_MediaProps
 * @description PC端图片媒体选择器组件的属性定义
 */
interface PcMockup_MediaProps {
    /** 媒体选择器模态框的激活状态 - 控制弹窗显示 */
    isActiveMediaPickerModal: boolean

    /** 设置媒体选择器模态框状态的函数 - 外部控制弹窗开关 */
    setIsActiveMediaPickerModal: (isActive: boolean) => void
}

/**
 * @component PcMockup_Media
 * @description PC端图片媒体选择器主组件
 *
 * 组件架构：
 * 1. 布局解析：根据activeLayout动态确定设备数量和配置
 * 2. 拖拽系统：为每个设备创建独立的拖拽区域
 * 3. 设备渲染：动态渲染1-3个设备的预览和交互
 * 4. 状态管理：与全局图片状态和设备选择状态同步
 *
 * 交互流程：
 * 设备点击 → 检查绑定状态 → 有图弹窗管理 / 无图触发上传
 * 文件拖拽 → 设备识别 → 智能绑定 → 视觉反馈更新
 *
 * 性能优化：
 * - 设备数量驱动的条件渲染
 * - 拖拽状态的精确控制
 * - 布局配置的深拷贝缓存
 *
 * @param {PcMockup_MediaProps} props 组件属性
 * @returns {JSX.Element} 渲染的PC端媒体选择器组件
 */
export const PcMockup_Media = ({
    isActiveMediaPickerModal,
    setIsActiveMediaPickerModal,
}: PcMockup_MediaProps) => {
    // 从Zustand Store获取当前激活的布局状态
    const activeLayout = useActiveLayout()
    // ==================== 引用和状态管理 ====================

    /** 文件输入控件的引用 - 用于程序化触发文件选择 */
    const fileInputRef = useRef<HTMLInputElement>(null)

    // 从状态管理中获取核心数据和操作方法
    const {
        addImages, // 添加图片到系统
        setImageDevice, // 智能绑定图片到设备
        setDragState, // 设置拖拽状态
        dragState, // 当前拖拽状态
        setCurrentSelectedDevice, // 设置当前选中设备
    } = useImageStore()

    // ==================== 布局配置解析 ====================

    /**
     * @function getActiveLayoutConfig
     * @description 根据布局标识解析完整的布局配置对象
     *
     * 解析逻辑：
     * 1. 根据布局类型选择对应的配置数组
     * 2. 通过ID匹配找到具体的布局配置
     * 3. 深拷贝配置避免意外修改原始数据
     * 4. 提供安全的默认值处理
     *
     * 支持的布局类型：
     * - Single: 单设备布局配置
     * - Dual: 双设备布局配置
     * - Triple: 三设备布局配置
     *
     * 错误处理：
     * - 未找到配置时使用默认单设备布局
     * - 打印警告信息便于调试
     *
     * @param {{type: LayoutType, id: number}} layout 布局标识对象
     * @returns {LayoutConfig} 完整的布局配置对象
     */
    const getActiveLayoutConfig = (layout: { type: LayoutType; id: number }): LayoutConfig => {
        let baseConfig: LayoutConfig | undefined

        // 根据布局类型从对应配置数组中查找
        if (layout.type === LayoutType.Single) {
            baseConfig = singleDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Dual) {
            baseConfig = dualDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Triple) {
            baseConfig = tripleDeviceLayoutConfigs.find(c => c.id === layout.id)
        }

        // 防御性编程：处理配置未找到的情况
        if (!baseConfig) {
            console.warn(`⚠️ 未找到布局配置 ${layout.type}-${layout.id}，使用默认布局`)
            // 返回安全的默认配置
            return singleDeviceLayoutConfigs[0]
        }

        // 深拷贝避免修改原始配置，确保数据安全
        return JSON.parse(JSON.stringify(baseConfig))
    }

    // 解析当前激活的布局配置
    const currentLayoutConfig = getActiveLayoutConfig(activeLayout)
    const deviceCount = currentLayoutConfig.devices.length

    // 动态获取各设备的图片绑定状态
    const deviceImages = Array.from({ length: deviceCount }, (_, index) => getImageForDevice(index))

    // ==================== 事件处理函数 ====================

    /**
     * @function handleMediaPickerModal
     * @description 处理媒体选择器弹窗的显示状态切换
     *
     * 功能说明：
     * - 切换弹窗的显示/隐藏状态
     * - 用于响应用户的弹窗控制操作
     * - 提供外部组件的弹窗控制接口
     */
    const handleMediaPickerModal = () => {
        setIsActiveMediaPickerModal(!isActiveMediaPickerModal)
    }

    /**
     * @function handleFileSelect
     * @description 处理文件选择器返回的文件
     *
     * 处理流程：
     * 1. 验证文件列表的有效性
     * 2. 异步处理文件上传到系统
     * 3. 自动绑定到指定设备
     * 4. 清理文件输入状态
     *
     * 绑定逻辑：
     * - 获取最新添加的图片
     * - 使用setImageDevice进行智能绑定
     * - 自动处理设备唯一性约束
     *
     * @param {React.ChangeEvent<HTMLInputElement>} event 文件选择事件
     * @param {number} deviceIndex 目标设备索引
     */
    const handleFileSelect = async (
        event: React.ChangeEvent<HTMLInputElement>,
        deviceIndex: number,
    ) => {
        const files = event.target.files

        if (files && files.length > 0) {
            // 异步处理文件上传
            await addImages(Array.from(files))

            // 获取最新添加的图片并绑定到指定设备
            const images = useImageStore.getState().images
            const newImage = images[images.length - 1]
            if (newImage) {
                // 使用智能绑定方法，自动处理设备唯一性
                setImageDevice(newImage.id, deviceIndex)
            }

            // 清理文件输入，允许重复选择
            event.target.value = ''
        }
    }

    /**
     * @function handleDeviceClick
     * @description 处理设备区域的点击事件 - 智能交互逻辑核心
     *
     * 智能交互算法：
     * 1. 设置当前选中设备（用于后续操作的上下文）
     * 2. 检查设备的图片绑定状态
     * 3. 有图片：弹出媒体管理器进行管理操作
     * 4. 无图片：直接触发文件上传流程
     *
     * 用户体验设计：
     * - 减少操作步骤：点击即可完成主要操作
     * - 上下文感知：根据当前状态提供合适的功能
     * - 状态反馈：通过日志提供操作确认
     *
     * 技术实现：
     * - 使用事件监听器的临时绑定避免内存泄漏
     * - 通过闭包保持设备索引的上下文
     *
     * @param {number} deviceIndex 被点击的设备索引
     */
    const handleDeviceClick = (deviceIndex: number) => {
        // 步骤1：设置当前选中的设备上下文
        setCurrentSelectedDevice(deviceIndex)

        // 步骤2：检查设备的图片绑定状态
        const deviceImage = getImageForDevice(deviceIndex)

        if (deviceImage) {
            // 路径A：设备已绑定图片 → 弹出媒体管理器
            setIsActiveMediaPickerModal(true)
            console.log(`📱 设备 ${deviceIndex} 已绑定图片，打开媒体管理器`)
        } else {
            // 路径B：设备未绑定图片 → 触发文件上传
            if (fileInputRef.current) {
                // 创建临时事件处理器，确保文件绑定到正确设备
                const tempHandler = (event: Event) => {
                    const inputEvent = event as any
                    handleFileSelect(inputEvent, deviceIndex)
                    // 清理临时监听器，避免内存泄漏
                    fileInputRef.current?.removeEventListener('change', tempHandler)
                }

                // 绑定临时处理器并触发文件选择
                fileInputRef.current.addEventListener('change', tempHandler)
                fileInputRef.current.click()
            }
            console.log(`📁 设备 ${deviceIndex} 未绑定图片，触发文件上传`)
        }
    }

    // ==================== 拖拽处理系统 ====================

    /**
     * @function handleDrop
     * @description 处理文件拖拽放置操作
     *
     * 拖拽处理流程：
     * 1. 验证拖拽文件的有效性
     * 2. 异步处理文件上传到系统
     * 3. 自动绑定到目标设备
     * 4. 清理拖拽状态
     *
     * 性能优化：
     * - 异步处理避免UI阻塞
     * - 状态清理确保视觉反馈正确
     * - 错误边界保护系统稳定性
     *
     * @param {File[]} files 拖拽的文件列表
     * @param {number} deviceIndex 目标设备索引
     */
    const handleDrop = async (files: File[], deviceIndex: number) => {
        if (files.length > 0) {
            // 异步处理文件上传
            await addImages(files)

            // 获取最新添加的图片并绑定到目标设备
            const images = useImageStore.getState().images
            const newImage = images[images.length - 1]
            if (newImage) {
                setImageDevice(newImage.id, deviceIndex)
            }
        }

        // 重要：清理拖拽状态，恢复正常UI
        setDragState({ dragOverTarget: null, isDragging: false })
    }

    // 为每个设备创建独立的拖拽区域（支持最多3个设备）

    /** 设备0的拖拽区域配置 */
    const device0Dropzone = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: acceptedFiles => handleDrop(acceptedFiles, 0),
        onDragEnter: () => setDragState({ dragOverTarget: 'device-0', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true, // 禁用默认点击，使用自定义点击处理
    })

    /** 设备1的拖拽区域配置 */
    const device1Dropzone = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: acceptedFiles => handleDrop(acceptedFiles, 1),
        onDragEnter: () => setDragState({ dragOverTarget: 'device-1', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true,
    })

    /** 设备2的拖拽区域配置 */
    const device2Dropzone = useDropzone({
        accept: { 'image/*': ['.png', '.jpg', '.jpeg', '.webp'] },
        onDrop: acceptedFiles => handleDrop(acceptedFiles, 2),
        onDragEnter: () => setDragState({ dragOverTarget: 'device-2', isDragging: true }),
        onDragLeave: () => setDragState({ dragOverTarget: null }),
        noClick: true,
    })

    // 将dropzone配置组织为数组，便于动态访问
    const deviceDropzones = [device0Dropzone, device1Dropzone, device2Dropzone]

    // ==================== 设备渲染函数 ====================

    /**
     * @function renderDeviceItem
     * @description 渲染单个设备项的预览组件
     *
     * 组件结构：
     * - 拖拽容器：处理文件拖拽交互
     * - 拖拽指示器：显示拖拽状态的视觉反馈
     * - 设备预览：显示设备外观和屏幕内容
     * - 内容区域：显示图片或空状态
     *
     * 交互逻辑：
     * - 拖拽悬停时显示投放指示器
     * - 点击触发智能选择逻辑
     * - 图片显示或空状态展示
     *
     * 样式系统：
     * - drop-active: 拖拽悬停时的高亮样式
     * - aspect-ratio: 维持设备屏幕比例
     * - mask-image: 使用SVG遮罩实现屏幕形状
     *
     * @param {number} deviceIndex 设备在布局中的索引
     * @returns {JSX.Element | null} 渲染的设备项或null
     */
    const renderDeviceItem = (deviceIndex: number) => {
        const deviceImage = deviceImages[deviceIndex]
        const dropzone = deviceDropzones[deviceIndex]

        // 检查拖拽状态，控制视觉反馈
        const isDropActive = dragState.dragOverTarget === `device-${deviceIndex}`

        // 安全检查：确保拖拽区域配置存在
        if (!dropzone) return null

        return (
            <div
                key={deviceIndex}
                {...dropzone.getRootProps()}
                className={`device-item ${isDropActive ? 'drop-active' : ''}`}
                onClick={() => handleDeviceClick(deviceIndex)}
            >
                {/* 拖拽投放指示器 - 仅在拖拽悬停时显示 */}
                <div className='drop-indicator'>
                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                        <path
                            fill='currentColor'
                            fillRule='evenodd'
                            d='M22.141 7.756q.858 1.99.859 4.25 0 2.248-.859 4.237a11.1 11.1 0 0 1-2.386 3.508 11.4 11.4 0 0 1-3.516 2.384q-1.99.864-4.239.865-2.25 0-4.24-.865a11.4 11.4 0 0 1-3.516-2.378 11 11 0 0 1-2.386-3.5A10.6 10.6 0 0 1 1 12.02a10.6 10.6 0 0 1 .858-4.25 11.2 11.2 0 0 1 2.386-3.515 11.3 11.3 0 0 1 3.516-2.39A10.5 10.5 0 0 1 12 .999q2.249 0 4.239.866a11.4 11.4 0 0 1 3.516 2.383 11.1 11.1 0 0 1 2.386 3.508M12.736 6.68q-.369-.368-.75-.368-.368 0-.736.368l-3.64 3.61q-.245.245-.245.654a.886.886 0 0 0 .913.913.97.97 0 0 0 .668-.273l1.159-1.171 1.049-1.308-.109 2.506v5.163q0 .409.266.674a.91.91 0 0 0 .675.266.91.91 0 0 0 .675-.266.91.91 0 0 0 .265-.674v-5.163l-.095-2.52 1.05 1.322 1.145 1.171a.9.9 0 0 0 .667.273.92.92 0 0 0 .662-.259.88.88 0 0 0 .265-.654.89.89 0 0 0-.259-.654z'
                        />
                    </svg>
                </div>

                {/* 设备预览区域 */}
                <div className='display-preview'>
                    <div className='safe-area'>
                        <div
                            className='device-screen'
                            style={{
                                aspectRatio: '430 / 932', // iPhone 15 Plus 屏幕比例
                                maskImage: 'url("/mockups/iPhone 15 Plus/portrait/display.svg")',
                                maskSize: '100% 100%',
                            }}
                        >
                            {deviceImage ? (
                                // 显示绑定的图片
                                <img
                                    crossOrigin='anonymous'
                                    loading='lazy'
                                    decoding='async'
                                    src={deviceImage.preview}
                                    alt={`设备${deviceIndex}的图片`}
                                />
                            ) : (
                                // 显示空状态：添加图片的提示
                                <div
                                    className='empty-state'
                                    style={{
                                        borderRadius: 0, // 移除圆角，适配设备形状
                                    }}
                                >
                                    {/* 添加图片图标 */}
                                    <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
                                        <path
                                            fill='currentColor'
                                            d='M4 11.664a.96.96 0 0 0 .949.949h5.765v5.765c0 .504.434.95.95.95.515 0 .937-.446.937-.95v-5.765h5.777a.96.96 0 0 0 .938-.949.96.96 0 0 0-.938-.95h-5.777V4.949c0-.504-.422-.949-.937-.949a.97.97 0 0 0-.95.949v5.765H4.949a.96.96 0 0 0-.949.95'
                                        />
                                    </svg>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // ==================== 主渲染结构 ====================

    return (
        <div className='panel-control undefined '>
            {/* 控制面板标签 */}
            <span className='label gray-text'>media</span>

            {/* 主控制区域 */}
            <div className='controls'>
                <div className='panel-control-grid col-1'>
                    {/* 设备网格容器 - 动态适应设备数量 */}
                    <div className={`mock-assets-control items-${deviceCount}`}>
                        {/* 隐藏的文件输入控件 - 用于程序化文件选择 */}
                        <input
                            ref={fileInputRef}
                            className='d-none'
                            type='file'
                            accept={HTML_ACCEPT_ATTRIBUTES.STANDARD_IMAGES}
                            multiple
                        />

                        {/* 动态渲染设备项 - 基于当前布局的设备数量 */}
                        {Array.from({ length: deviceCount }, (_, index) => renderDeviceItem(index))}
                    </div>

                    {/* 底部操作提示文本 */}
                    <span className='footnote gray-text' style={{ textAlign: 'center' }}>
                        Drop media or click to choose
                    </span>
                </div>
            </div>
        </div>
    )
}
