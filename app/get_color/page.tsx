'use client'

import React, { useState, useRef, useEffect } from 'react'
import { extractColors, ColorExtractionResult } from './imageToColors'

/**
 * 颜色提取结果状态枚举
 * @enum {string}
 */
enum ColorExtractionStatus {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 加载中状态 */
    LOADING = 'loading',
    /** 成功状态 */
    SUCCESS = 'success',
    /** 错误状态 */
    ERROR = 'error',
}

/**
 * 颜色提取组件的属性接口
 * @interface GetColorProps
 */
interface GetColorProps {
    /** 图片路径，默认使用 walller__1.jpg */
    imagePath?: string
    /** 要提取的颜色数量，默认为 4 */
    colorCount?: number
    /** 颜色提取完成后的回调函数 */
    onColorsExtracted?: (colors: string[]) => void
}

/**
 * 图片颜色提取组件
 *
 * 该组件使用 ColorThief 库从指定图片中提取主要颜色调色板
 * 基于中位切分算法，注重面积占比来提取颜色
 *
 * @component
 * @returns {JSX.Element} 颜色提取组件
 */
const GetColor: React.FC = () => {
    const onColorsExtracted = undefined // 页面组件不接收 props
    // 提取的颜色数组状态 - 存储十六进制颜色
    const [extractedColors, setExtractedColors] = useState<string[]>([])
    // 提取的 RGB 颜色数组状态
    const [extractedRgbColors, setExtractedRgbColors] = useState<number[][]>([])
    // 颜色提取状态
    const [status, setStatus] = useState<ColorExtractionStatus>(ColorExtractionStatus.IDLE)
    // 错误信息状态
    const [errorMessage, setErrorMessage] = useState<string>('')
    // 图片引用
    const imageRef = useRef<HTMLImageElement>(null)
    // 当前选择的图片
    const [selectedImage, setSelectedImage] = useState<string>('walller__1.jpg')
    // 当前选择的颜色数量
    const [colorCount, setColorCount] = useState<number>(5)

    // 获取当前图片路径
    const imagePath = `/__壁纸测试/${selectedImage}`

    /**
     * 处理图片加载完成事件
     * 使用提取的颜色提取颜色调色板
     */
    const handleImageLoad = async (): Promise<void> => {
        console.log('图片加载完成，开始提取颜色...')

        if (!imageRef.current) {
            console.error('图片元素引用不存在')
            setStatus(ColorExtractionStatus.ERROR)
            setErrorMessage('图片元素引用不存在')
            return
        }

        try {
            setStatus(ColorExtractionStatus.LOADING)
            setErrorMessage('')

            console.log('图片信息:', {
                src: imageRef.current.src,
                width: imageRef.current.naturalWidth,
                height: imageRef.current.naturalHeight,
                complete: imageRef.current.complete,
            })

            // 使用新的颜色提取模块
            const result = await extractColors(imageRef.current, { colorCount })

            if (result.success) {
                console.log('成功提取颜色:', result.hexColors)

                // 更新状态
                setExtractedColors(result.hexColors)
                setExtractedRgbColors(result.rgbColors)
                setStatus(ColorExtractionStatus.SUCCESS)

                // 调用回调函数
                if (onColorsExtracted) {
                    onColorsExtracted(result.hexColors)
                }
            } else {
                throw new Error(result.error || '未知错误')
            }
        } catch (error) {
            console.error('颜色提取失败:', error)
            setStatus(ColorExtractionStatus.ERROR)
            setErrorMessage(error instanceof Error ? error.message : '未知错误')
        }
    }

    /**
     * 处理图片加载错误事件
     */
    const handleImageError = (): void => {
        setStatus(ColorExtractionStatus.ERROR)
        setErrorMessage('图片加载失败，请检查图片路径是否正确')
    }

    /**
     * 重新提取颜色
     * 重置状态并重新触发颜色提取过程
     */
    const retryExtraction = (): void => {
        setStatus(ColorExtractionStatus.IDLE)
        setErrorMessage('')
        setExtractedColors([])
        setExtractedRgbColors([])

        if (imageRef.current) {
            // 重新触发图片加载
            imageRef.current.src = imagePath
        }
    }

    /**
     * 处理图片选择变化
     */
    const handleImageChange = (imageName: string): void => {
        setSelectedImage(imageName)
        setStatus(ColorExtractionStatus.IDLE)
        setExtractedColors([])
        setExtractedRgbColors([])
        setErrorMessage('')
    }

    /**
     * 处理颜色数量变化
     */
    const handleColorCountChange = (count: number): void => {
        setColorCount(count)
        setStatus(ColorExtractionStatus.IDLE)
        setExtractedColors([])
        setExtractedRgbColors([])
        setErrorMessage('')
    }

    /**
     * 自动提取颜色 - 当图片或颜色数量改变时
     */
    useEffect(() => {
        if (imageRef.current && imageRef.current.complete) {
            handleImageLoad()
        }
    }, [selectedImage, colorCount])

    return (
        <div style={styles.container}>
            <h2>图片颜色提取工具</h2>

            {/* 图片选择 */}
            <div style={styles.controlGroup}>
                <label>选择图片:</label>
                <select value={selectedImage} onChange={e => handleImageChange(e.target.value)}>
                    <option value='walller__1.jpg'>walller__1.jpg</option>
                    <option value='walller__2.jpg'>walller__2.jpg</option>
                </select>
            </div>

            {/* 颜色数量选择 */}
            <div style={styles.controlGroup}>
                <label>颜色数量: {colorCount}</label>
                <input
                    type='range'
                    min='4'
                    max='10'
                    value={colorCount}
                    onChange={e => handleColorCountChange(Number(e.target.value))}
                    style={styles.slider}
                />
                <div style={styles.sliderLabels}>
                    <span>4</span>
                    <span>5</span>
                    <span>6</span>
                    <span>7</span>
                    <span>8</span>
                    <span>9</span>
                    <span>10</span>
                </div>
            </div>

            <img
                ref={imageRef}
                src={imagePath}
                alt='颜色提取源图片'
                style={styles.image}
                onLoad={handleImageLoad}
                onError={handleImageError}
                crossOrigin='anonymous'
            />

            {status === ColorExtractionStatus.LOADING && <p>正在提取颜色...</p>}

            {status === ColorExtractionStatus.ERROR && (
                <div>
                    <p>错误: {errorMessage}</p>
                    <button onClick={retryExtraction}>重新提取</button>
                </div>
            )}

            <button onClick={handleImageLoad}>手动提取颜色</button>
            <p>
                当前状态: {status} | 图片: {selectedImage} | 颜色数量: {colorCount}
            </p>

            {status === ColorExtractionStatus.SUCCESS && extractedColors.length > 0 && (
                <div>
                    <h3>提取的颜色调色板</h3>
                    <div style={styles.colorsContainer}>
                        {extractedColors.map((color, index) => (
                            <div key={`${color}-${index}`} style={styles.colorItem}>
                                <div
                                    style={{
                                        ...styles.colorSwatch,
                                        backgroundColor: color,
                                    }}
                                    title={`颜色 ${index + 1}: ${color}`}
                                ></div>
                                <div style={styles.colorInfo}>
                                    <span style={styles.colorCode}>HEX: {color}</span>
                                    <span style={styles.colorCode}>
                                        RGB: ({extractedRgbColors[index]?.join(', ')})
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
}

// 简化样式对象
const styles = {
    container: {
        padding: '20px',
    },
    controlGroup: {
        marginBottom: '15px',
    },
    slider: {
        width: '200px',
        height: '6px',
        margin: '10px 0',
        cursor: 'pointer',
    },
    sliderLabels: {
        display: 'flex',
        justifyContent: 'space-between',
        width: '200px',
        fontSize: '12px',
        color: '#666',
    },
    image: {
        maxWidth: '400px',
        maxHeight: '300px',
    },
    colorsContainer: {
        display: 'flex',
        gap: '10px',
        flexWrap: 'wrap' as const,
    },
    colorItem: {
        textAlign: 'center' as const,
    },
    colorSwatch: {
        width: '80px',
        height: '80px',
        display: 'flex',
        alignItems: 'flex-end',
        justifyContent: 'center',
        paddingBottom: '5px',
        marginBottom: '5px',
    },
    colorInfo: {
        display: 'flex',
        flexDirection: 'column' as const,
        gap: '2px',
        height: '50px',
        alignItems: 'center',
    },
    colorCode: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        padding: '2px 4px',
        fontSize: '10px',
        height: '50px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
}

export default GetColor
