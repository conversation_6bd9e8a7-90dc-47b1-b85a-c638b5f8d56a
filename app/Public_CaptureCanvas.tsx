import DisplayContainer from './components/DisplayContainer/DisplayContainer'
import { useViewDimensions } from './hooks/useAppState'
import { useState } from 'react'
import {
    LayoutConfig,
    singleDeviceLayoutConfigs,
    dualDeviceLayoutConfigs,
    tripleDeviceLayoutConfigs,
    LayoutType,
} from './components/DisplayContainer/DisplayConfig'
import { useActiveLayout } from './features/viewDimensions/utils/重构/状态管理'

/**
 * @interface Public_CaptureCanvasProps
 * @description Public_CaptureCanvas 组件的属性 - 已移除所有props，直接从Zustand Store获取状态
 */
interface Public_CaptureCanvasProps {}

/**
 * @component Public_CaptureCanvas
 * @description 用于导出的画布组件
 * 与 CanvasContainer 组件结构相似，但专门用于截图导出功能
 * 动态获取当前视图尺寸，而不是硬编码固定值
 * 现在直接从Zustand Store获取activeLayout状态
 */
export const Public_CaptureCanvas = () => {
    // 从Zustand Store获取当前激活的布局状态
    const activeLayout = useActiveLayout()
    // 从状态管理中获取当前视图尺寸配置
    const currentViewDimensions = useViewDimensions()

    // 媒体选择器弹窗状态（用于传递给 DisplayContainer）
    const [isActiveMediaPickerModal, setIsActiveMediaPickerModal] = useState<boolean>(false)

    // 从状态中获取实际的宽度和高度，而不是硬编码的 1920x1440
    const canvasWidth = currentViewDimensions.useWidth // 例如：1920
    const canvasHeight = currentViewDimensions.useHeight // 例如：1440
    const ratioWidth = currentViewDimensions.ratioWidth // 例如：4
    const ratioHeight = currentViewDimensions.ratioHeight // 例如：3

    /**
     * @function getActiveLayoutConfig
     * @description 根据传入的布局对象，从配置数组中查找并返回完整的布局配置。
     * 这个逻辑与 CanvasContainer 中的实现完全一致，确保导出和预览使用相同的布局。
     * @param {{type: LayoutType, id: number}} layout - 需要查找的布局对象。
     * @returns {LayoutConfig} 返回找到的布局配置。如果找不到，则返回一个默认配置。
     */
    const getActiveLayoutConfig = (layout: { type: LayoutType; id: number }): LayoutConfig => {
        let baseConfig: LayoutConfig | undefined

        // 1. 根据布局类型，在对应的配置数组中查找匹配的 ID。
        if (layout.type === LayoutType.Single) {
            baseConfig = singleDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Dual) {
            baseConfig = dualDeviceLayoutConfigs.find(c => c.id === layout.id)
        } else if (layout.type === LayoutType.Triple) {
            baseConfig = tripleDeviceLayoutConfigs.find(c => c.id === layout.id)
        }

        // 2. 防御性编程：如果找不到配置，打印警告并返回一个安全的默认值。
        if (!baseConfig) {
            console.warn(
                `在 DisplayConfig.ts 中未找到布局 ${layout.type}-${layout.id} 的配置，将使用默认布局。`,
            )
            // 返回单设备布局的第一个作为默认值
            return singleDeviceLayoutConfigs[0]
        }

        // 3. 深拷贝基础配置以避免意外修改原始配置数组，这对于共享数据至关重要。
        return JSON.parse(JSON.stringify(baseConfig))
    }

    // 根据从 props 接收的 activeLayout 获取最终的布局配置
    const activeLayoutConfig = getActiveLayoutConfig(activeLayout)

    console.log('🖼️ 导出画布配置:', {
        width: canvasWidth,
        height: canvasHeight,
        source: currentViewDimensions.sourceType,
        key: currentViewDimensions.key,
        layoutType: activeLayout.type,
        layoutId: activeLayout.id,
        devicesCount: activeLayoutConfig.devices?.length || 0,
    })

    return (
        <div
            id='captureCanvas'
            className='frame'
            style={{
                aspectRatio: `${ratioWidth} / ${ratioHeight}`, // 动态宽高比
                width: canvasWidth, // 使用状态中的宽度
                height: canvasHeight, // 使用状态中的高度
                // zIndex: 1000, // 保持在最上层用于截图
                // position: 'fixed',
                zIndex: -1000, // 隐藏让用户不可见
            }}
        >
            <div className='frame-content'>
                <div
                    style={{
                        position: 'relative',
                        overflow: 'hidden',
                        width: canvasWidth, // 使用状态中的宽度
                        height: canvasHeight, // 使用状态中的高度
                        opacity: 1,
                    }}
                >
                    <div
                        style={{
                            width: canvasWidth, // 使用状态中的宽度
                            height: canvasHeight, // 使用状态中的高度
                            display: 'flex',
                            flexDirection: 'column',
                            position: 'absolute',
                            left: 0,
                            top: 0,
                            overflow: 'hidden',
                        }}
                    >
                        <DisplayContainer
                            layoutConfig={activeLayoutConfig}
                            setIsActiveMediaPickerModal={setIsActiveMediaPickerModal}
                            canvasWidth={canvasWidth} // 传递动态宽度
                            canvasHeight={canvasHeight} // 传递动态高度
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
