'use client'
import { useState, useEffect, useMemo } from 'react'
import { Public_CaptureCanvas } from './Public_CaptureCanvas'
import CanvasContainer from './components/CanvasContainer/CanvasContainer'
import TopBar from './components/TopBar/TopBar'
import { useMobileDetection, useCalculationActions } from './hooks/useAppState'
import { useKeyboardListener } from './utils/browser'
import { usePasteImage } from './hooks/usePasteImage'
import { useImageStore } from './ImageMange/imageMangeIndex'
import {
    EXTENDED_IMAGE_CONFIG,
    FILE_SIZE_LIMITS,
    UPLOAD_CONCURRENCY_CONFIG,
} from './ImageMange/imageConfig'
import { toast, Toaster } from 'sonner'

import { MobileMockup_Layout } from './MobileMockup_Layout'
import { Public_MockupStyle } from './Public_MockupStyle'
import { Public_MockupSmallLayout } from './Public_MockupSmallLayout'
import { Public_MockupShadow } from './Public_MockupShadow'
import { Public_MockupDetails } from './Public_MockupDetails'
import { Public_MockupModal } from './Public_MockupModal'
//
import { Public_FrameSizeModal } from './Public_FrameSizeModal'
import { Public_FrameScene } from './Public_FrameScene'
import { Public_FrameEffects } from './Public_FrameEffects'
import { Public_FrameCustomView } from './Public_FrameCustomView'
import { MobileFrame_Magic } from './MobileFrame_Magic'
import { MobileFrame_ColorsBackground } from './MobileFrame_ColorsBackground'
import { PublicFrame_CustomImage_Modal } from './PublicFrame_CustomImage_Modal'
import { PublicFrame_ColorPicker_Modal } from './PublicFrame_ColorPicker_Modal'
import { Public_MediaPickerModal } from './Public_MediaPickerModal'
import { PcFrame_WaterMarkModal } from './PcFrame_WaterMarkModal'
import { PcFrame_Effects__ModalDefault } from './PcFrame_Effects__ModalDefault'
import { PcFrame_Effects__ModalPortrait } from './PcFrame_Effects__ModalPortrait'
import { PcFrame_Effects__ModalVef } from './PcFrame_Effects__ModalVef'
import { PcFrame_ShapesItemsModal } from './PcFrame_ShapesItemsModal'
import { PcRightSlider } from './PcRightSlider'

//
import { Public_PanelTabs } from './Public_PanelTabs'
import { Public_MockUp_ModelSelector } from './Public_MockUp_ModelSelector'
import { Public_MockUp_SizeSelector } from './Public_MockUp_SizeSelector'
//
import { PcMockup_Media } from './PcMockup_Media'
import { PcMockup_Visibility } from './PcMockup_Visibility'
import { MobileMockup_Tabs } from './MobileMockup_Tabs'
import { MobileFrame_Tabs } from './MobileFrame_Tabs'

import { PcFrame_WaterMarkSection } from './PcFrame_WaterMarkSection'
import { ExportPopver } from './components/ExportPopver/ExportPopver'
import { LayoutType } from './components/DisplayContainer/DisplayConfig'
import { useAppStore, useActiveLayout } from './features/viewDimensions/utils/重构/状态管理'
/**
 * 主页面组件
 * 包含顶部导航栏和主内容区域
 * @returns {JSX.Element} 渲染的主页面
 */
export default function Home() {
    // 移动端控制面板Tabs
    const [mobileMockupTabsActive, setmobileMockupTabsActive] = useState<string>('modal')
    // 移动端画布控制面板Tabs
    const [mobileFrameTabsActive, setmobileFrameTabsActive] = useState<string>('magic')
    // 是否显示 模型选择器 弹窗
    const [isActiveMockModel, setIsActiveMockModel] = useState<boolean>(false)
    // 是否显示 画布尺寸选择器 弹窗
    const [isActiveFrameModel, setIsActiveFrameModel] = useState<boolean>(false)
    // 是否显示 媒体选择器 弹窗
    // 媒体选择器弹窗
    const [isActiveMediaPickerModal, setIsActiveMediaPickerModal] = useState<boolean>(false)
    // 是否显示 背景效果 弹窗
    const [isActiveBackgroundEffectsModal, setIsActiveBackgroundEffectsModal] =
        useState<boolean>(false)

    // 颜色选择器弹窗状态管理
    // 背景颜色选择器状态管理已迁移到 useBackgroundStore
    const [isActiveColorPickerModal, setIsActiveColorPickerModal] = useState<boolean>(false)
    // 是否显示 水印 弹窗
    const [isActiveWatermarkModal, setIsActiveWatermarkModal] = useState<boolean>(false)
    // 是否显示 场景 弹窗
    const [isActivePortraitModal, setIsActivePortraitModal] = useState<boolean>(false)
    // 是否显示 特效 弹窗
    const [isActiveVefModal, setIsActiveVefModal] = useState<boolean>(false)
    // 当前选中的背景颜色状态管理
    const [selectedBackgroundColor, setSelectedBackgroundColor] = useState<string>('#ffffff')

    // 图片选择器弹窗状态管理
    // 背景图片选择器状态管理已迁移到 useBackgroundStore 和 useCustomImageStore

    // 从Zustand Store获取activeLayout状态和操作方法
    const activeLayout = useActiveLayout()
    const setActiveLayout = useAppStore(state => state.setActiveLayout)

    // 使用集成了检测逻辑的钩子，安全处理 SSR 和客户端检测
    const { isMobile, isInitialized } = useMobileDetection()

    // 获取计算相关的操作方法
    const { calculateAndUpdate } = useCalculationActions()

    // 获取图片管理系统的上传功能
    const { addImages } = useImageStore()

    // 集成剪贴板粘贴功能 (Refactored to use Zustand store)
    // 此 Hook 现在不返回任何值，因为它所有的逻辑和状态都由 Zustand Store 管理。
    // 我们在这里对其进行配置，它会在全局范围内生效。
    usePasteImage({
        // 是否启用此功能
        enabled: true,
        // 单个文件的最大尺寸 (10MB) - 使用统一配置
        maxFileSize: FILE_SIZE_LIMITS.DEFAULT_MAX_SIZE,
        // 允许的图片MIME类型 - 使用扩展配置（包含GIF）
        allowedTypes: [...EXTENDED_IMAGE_CONFIG.allowedTypes],
        // 同时进行的最大并发批次数 - 使用统一配置
        maxConcurrentUploads: UPLOAD_CONCURRENCY_CONFIG.DEFAULT_MAX_CONCURRENT,
        // 每个批次包含的图片数量 - 使用统一配置
        batchSize: UPLOAD_CONCURRENCY_CONFIG.DEFAULT_BATCH_SIZE,
        /**
         * 核心上传逻辑：当 Store 需要上传一个文件批次时，此函数会被调用。
         * @param {File[]} files - 需要上传的文件数组（一个批次）。
         */
        onUpload: async (files: File[]) => {
            // 将文件批次交给图片管理 Store (`useImageStore`) 的 `addImages` action 处理。
            // `addImages` 内部会处理文件的验证、创建预览等操作。
            await addImages(files)
            console.log(`[Uploader] 批处理任务已发送: ${files.length} 张图片`)
        },
        /**
         * 所有批次都处理完成后的最终回调。
         * @param {object} result - 包含处理结果的对象。
         * @param {number} result.uploadedCount - 成功处理的图片总数。
         */
        onSuccess: result => {
            const { uploadedCount } = result
            if (uploadedCount > 0) {
                toast.success(`所有任务完成，成功处理 ${uploadedCount} 张图片！`, {
                    duration: 3000,
                    position: 'top-right',
                })
            }
        },
        /**
         * 每个批次成功上传后的回调。
         * @param {File[]} files - 该批次成功上传的文件数组。
         */
        onBatchSuccess: files => {
            // 给予用户即时的、分阶段的反馈。
            toast.info(`批次上传成功: ${files.length} 张图片`, {
                duration: 2000,
                position: 'top-right',
            })
        },
        /**
         * 发生严重错误时的回调。
         * @param {string} error - 错误信息。
         */
        onError: error => {
            console.error('❌ 剪贴板粘贴失败:', error)
            toast.error(`粘贴失败: ${error}`, {
                duration: 5000,
                position: 'top-right',
            })
        },
        /**
         * 发生警告时的回调（例如，粘贴了无效文件）。
         * @param {string} message - 警告信息。
         */
        onWarning: message => {
            console.warn('⚠️ 剪贴板粘贴警告:', message)
            toast.warning(message, {
                duration: 4000,
                position: 'top-right',
            })
        },
    })

    // 页面加载时自动计算并更新视图尺寸
    useEffect(() => {
        // 确保移动设备检测已初始化完成
        if (!isInitialized) return

        // 确保在客户端环境中运行
        if (typeof window === 'undefined') return

        console.log('📐 页面加载完成，开始计算默认视图尺寸')

        // 计算并更新视图尺寸
        calculateAndUpdate()

        console.log('✅ 默认视图尺寸计算完成')
    }, [isInitialized, calculateAndUpdate])

    // 公共控制面板切换
    enum ActiveTabInterface {
        mockup = 'mockup',
        frame = 'frame',
    }
    // 电脑端激活 tab 状态
    const [activeTab, setActiveTab] = useState<string>(
        ActiveTabInterface.mockup,
        // ActiveTabInterface.frame,
    )
    // 更新电脑端激活 tab 状态
    const updateActiveTab = (tab: string) => {
        setActiveTab(tab)
    }

    // 背景颜色选择和图片选择相关的函数已迁移到 useBackgroundStore 和 useCustomImageStore

    // 键盘快捷键监听 - 使用安全的浏览器工具
    useKeyboardListener((e: KeyboardEvent) => {
        // 转换为小写，确保大小写都能触发
        const key = e.key.toLowerCase()

        if (key === 'q') {
            setActiveTab(ActiveTabInterface.mockup)
        } else if (key === 'w') {
            setActiveTab(ActiveTabInterface.frame)
        } else if (key === 'a') {
            // 切换到代表性的单设备布局（倾斜视图）
            setActiveLayout({ type: LayoutType.Single, id: 1 })
            // setActiveLayout({ type: LayoutType.Single, id: 2 })
            console.log('快捷键切换: 单设备视图')
        } else if (key === 's') {
            // 切换到代表性的双设备布局（旋转视图）
            setActiveLayout({ type: LayoutType.Dual, id: 4 })
            console.log('快捷键切换: 双设备视图')
        } else if (key === 'd') {
            // 切换到代表性的三设备布局（中心突出）
            setActiveLayout({ type: LayoutType.Triple, id: 3 })
            console.log('快捷键切换: 三设备视图')
        }
    }, []) // 空依赖数组，只在组件挂载时执行一次

    // 在移动设备检测初始化完成之前，显示加载状态
    if (!isInitialized) {
        // if (1) {
        return (
            <main className='app-main'>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100vh',
                        gap: '24px',
                        backgroundColor: '#1d1d1dff',
                        fontFamily: 'Inter, sans-serif',
                    }}
                >
                    <img
                        src='/image/shots-logo.png'
                        alt='MockPix'
                        style={{
                            width: '150px',
                            height: '150px',
                        }}
                    />
                    <span
                        style={{
                            fontSize: '36px',
                            fontWeight: '600',
                            color: '#fcfcfc',
                            letterSpacing: '4px',
                        }}
                    >
                        MockPix
                    </span>
                </div>
            </main>
        )
    }

    // 公共控制面板Tabs
    const publicTabsView = () => {
        return (
            <Public_PanelTabs
                activeTab={activeTab}
                updateActiveTab={updateActiveTab}
                isMobile={isMobile}
            />
        )
    }

    // 公共控制面板滚动视图
    const publicControlPanelScrollView = (
        renderMockup: React.ReactNode,
        renderFrame: React.ReactNode,
    ) => {
        return (
            <div className='panel-scroll-view'>
                <section
                    className='panel-fragment'
                    id='mockupControls'
                    style={{
                        display: activeIsMockup ? 'block' : 'none',
                    }}
                >
                    <div className='panel-fragment-scroll-view  '>
                        <div className='panel-controls-stack'>{renderMockup}</div>
                    </div>
                </section>
                <div className='divide-line' />
                <section
                    className='panel-fragment'
                    id='frameControls'
                    style={{
                        display: activeIsFrame ? 'block' : 'none',
                    }}
                >
                    <div className='panel-fragment-scroll-view  '>
                        <div className='panel-controls-stack frame-controls'>{renderFrame}</div>
                    </div>
                </section>
            </div>
        )
    }

    const activeIsMockup = activeTab === 'mockup'
    const activeIsFrame = activeTab === 'frame'

    // 电脑端布局
    const MainPcContent = () => {
        return (
            <div className='app-main-panels'>
                {/* 左侧控制面板 */}
                <div className='sidebar'>
                    <div className='control-panel panel'>
                        {publicTabsView()}

                        {activeIsMockup && (
                            <Public_MockUp_ModelSelector
                                isActiveModel={isActiveMockModel}
                                setIsActiveModel={setIsActiveMockModel}
                            />
                        )}
                        {activeIsFrame && (
                            <Public_MockUp_SizeSelector
                                isActiveModel={isActiveFrameModel}
                                setIsActiveModel={setIsActiveFrameModel}
                            />
                        )}

                        {/* 公共控制面板滚动视图 */}
                        {publicControlPanelScrollView(
                            <>
                                <PcMockup_Media
                                    isActiveMediaPickerModal={isActiveMediaPickerModal}
                                    setIsActiveMediaPickerModal={setIsActiveMediaPickerModal}
                                />
                                <Public_MockupSmallLayout />
                                <Public_MockupStyle />
                                <Public_MockupShadow />
                                <PcMockup_Visibility />
                                <Public_MockupDetails />
                            </>,
                            <>
                                <PcFrame_WaterMarkSection
                                    isActiveWatermarkModal={isActiveWatermarkModal}
                                    setIsActiveWatermarkModal={setIsActiveWatermarkModal}
                                />
                                <Public_FrameEffects
                                    isActiveBackgroundEffectsModal={isActiveBackgroundEffectsModal}
                                    setIsActiveBackgroundEffectsModal={
                                        setIsActiveBackgroundEffectsModal
                                    }
                                    isActivePortraitModal={isActivePortraitModal}
                                    setIsActivePortraitModal={setIsActivePortraitModal}
                                    isActiveVefModal={isActiveVefModal}
                                    setIsActiveVefModal={setIsActiveVefModal}
                                />
                                <Public_FrameScene setActiveTab={() => {}} />
                                <Public_FrameCustomView />
                                <MobileFrame_Magic />
                                <MobileFrame_ColorsBackground />
                            </>,
                        )}
                    </div>
                </div>

                {/* 中间画布区域 */}
                <CanvasContainer setIsActiveMediaPickerModal={setIsActiveMediaPickerModal} />

                {/* 右侧控制面板 */}
                <PcRightSlider />
            </div>
        )
    }

    // 移动端布局
    const MainMobileContent = () => {
        return (
            <>
                {/* 画布区域 */}
                <CanvasContainer setIsActiveMediaPickerModal={setIsActiveMediaPickerModal} />

                {/* 控制布局 */}
                <div className='control-panel panel'>
                    {/* 底部Tabs切换 */}
                    {publicTabsView()}

                    {publicControlPanelScrollView(
                        <>
                            {mobileMockupTabsActive === 'layout' && <MobileMockup_Layout />}
                            {mobileMockupTabsActive === 'style' && <Public_MockupStyle />}
                            {mobileMockupTabsActive === 'shadow' && <Public_MockupShadow />}
                            {mobileMockupTabsActive === 'details' && <Public_MockupDetails />}

                            {/*  */}
                            <MobileMockup_Tabs
                                isActiveModel={isActiveMockModel}
                                setIsActiveModel={setIsActiveMockModel}
                                activeTab={mobileMockupTabsActive}
                                setActiveTab={setmobileMockupTabsActive}
                            />
                        </>,
                        <>
                            {isActiveFrameModel && <Public_FrameSizeModal />}
                            {mobileFrameTabsActive === 'effects' && (
                                <Public_FrameEffects
                                    isActiveBackgroundEffectsModal={isActiveBackgroundEffectsModal}
                                    setIsActiveBackgroundEffectsModal={
                                        setIsActiveBackgroundEffectsModal
                                    }
                                />
                            )}
                            {mobileFrameTabsActive === 'custom' && <Public_FrameCustomView />}
                            {mobileFrameTabsActive === 'magic' && <MobileFrame_Magic />}
                            {mobileFrameTabsActive === 'gradient' && (
                                <MobileFrame_ColorsBackground />
                            )}

                            {/*  */}
                            <MobileFrame_Tabs
                                isActiveModel={isActiveFrameModel}
                                setIsActiveModel={setIsActiveFrameModel}
                                activeTab={mobileFrameTabsActive}
                                setActiveTab={setmobileFrameTabsActive}
                            />
                        </>,
                    )}
                </div>
            </>
        )
    }

    return (
        <>
            <main
                id='app-main'
                // ${isMobile && (!!mobileMockupTabsActive || !!mobileFrameTabsActive || !!isActiveFrameModel || !!isActiveFrameModel) ? 'is-mobile-controls-open' : ''}
                className={`app-main  ${isMobile ? 'shots-mobile-ui' : ''} `}
            >
                {/* 顶部导航栏区域 */}
                <TopBar />
                {/* 根据设备类型渲染不同的内容 */}
                {isMobile ? MainMobileContent() : MainPcContent()}
            </main>
            {/* 弹窗集合 */}
            <div id='modal_root'>
                <PublicFrame_ColorPicker_Modal />

                {/* 图片选择器弹窗 */}
                <PublicFrame_CustomImage_Modal />

                {isMobile && (
                    <>
                        {mobileFrameTabsActive === 'scene' && (
                            <Public_FrameScene setActiveTab={setmobileFrameTabsActive} />
                        )}
                        <Public_MockupModal
                            isActiveModel={isActiveMockModel}
                            setIsActiveModel={setIsActiveMockModel}
                        />
                    </>
                )}
                {/* 电脑端相关弹窗组建封装 */}
                {!isMobile && (
                    <>
                        {isActiveMediaPickerModal && <ExportPopver />}
                        {isActiveWatermarkModal && <PcFrame_WaterMarkModal />}
                        {isActivePortraitModal && <PcFrame_Effects__ModalPortrait />}
                        {isActiveVefModal && <PcFrame_Effects__ModalVef />}
                        {/* <PcFrame_ShapesItemsModal /> */}
                        {isActiveBackgroundEffectsModal && <PcFrame_Effects__ModalDefault />}
                    </>
                )}

                {/* 选择图片弹窗 */}
                {isActiveMediaPickerModal && (
                    <Public_MediaPickerModal
                        isActiveMediaPickerModal={isActiveMediaPickerModal}
                        setIsActiveMediaPickerModal={setIsActiveMediaPickerModal}
                    />
                )}
            </div>

            {/* Sonner 通知系统 */}
            <Toaster position='top-right' richColors theme='dark' closeButton duration={3000} />

            {/* 导出 TODO:这里应该用户点击导出的时候，再渲染组件，不然内存消耗太高 */}
            <Public_CaptureCanvas />
        </>
    )
}
