/**
 * @file app/config/dragHandleConfig.ts
 * @description 拖拽手柄相关的统一配置文件
 * 所有与拖拽手柄位置、尺寸、变换相关的常量都集中在此文件中管理，
 * 避免硬编码参数分散在多个文件中导致的维护问题。
 */

/**
 * @interface DragHandlePosition
 * @description 拖拽手柄位置坐标接口
 */
export interface DragHandlePosition {
    x: number
    y: number
}

/**
 * @interface DragHandleDimensions
 * @description 拖拽手柄尺寸接口
 */
export interface DragHandleDimensions {
    width: number
    height: number
}

/**
 * @interface DragPadDimensions  
 * @description 拖拽区域尺寸接口
 */
export interface DragPadDimensions {
    width: number
    height: number
}

/**
 * @const DRAG_HANDLE_CONFIG
 * @description 拖拽手柄的统一配置对象
 * 包含所有与拖拽手柄相关的尺寸、位置、变换参数
 */
export const DRAG_HANDLE_CONFIG = {
    /**
     * 拖拽手柄的默认/初始/基准位置
     * 这个位置对应Canvas视图中的 translate(0%, 0%) 状态
     * 数值来源：通过实际测试和用户体验优化得出的最佳初始位置
     */
    BASE_POSITION: {
        x: 85.7143, // 基准X坐标（像素）- 约占拖拽区域宽度的 42.86%
        y: 63.4286, // 基准Y坐标（像素）- 约占拖拽区域高度的 42.86%
    } as DragHandlePosition,

    /**
     * 拖拽手柄元素自身的尺寸
     * 用于边界限制计算和碰撞检测
     */
    HANDLE_SIZE: {
        width: 28.5714,  // 拖拽手柄宽度（像素）
        height: 21.1429, // 拖拽手柄高度（像素）
    } as DragHandleDimensions,

    /**
     * 拖拽区域（drag-pad）的尺寸
     * 这是拖拽手柄可移动的有效范围
     * 数值来源：PcRightSlider.tsx 中的 drag-pad 容器尺寸
     */
    DRAG_PAD_SIZE: {
        width: 200,  // 拖拽区域宽度（像素）- 来自 208px 容器减去 4px*2 内边距
        height: 148, // 拖拽区域高度（像素）- 来自 156px 容器减去 4px*2 内边距
    } as DragPadDimensions,

    /**
     * 坐标转换相关配置
     * 用于将拖拽手柄的像素位移转换为Canvas视图的百分比变换
     */
    TRANSFORM: {
        /**
         * 是否应用反向逻辑
         * true: 拖拽手柄向右移动时，视图内容向左移动（推荐，符合用户直觉）
         * false: 拖拽手柄与视图内容同向移动
         */
        INVERT_DIRECTION: true,

        /**
         * 百分比精度
         * 控制 translate() 函数中百分比值的小数位数
         */
        PRECISION: 5,
    },

    /**
     * 默认的视图控制状态
     * 用于重置功能和初始化
     */
    DEFAULT_VIEW_CONTROL: {
        zoom: 100, // 默认缩放比例（100% = 1:1）
        dragHandlePosition: {
            x: 85.7143, // 使用基准位置作为默认值
            y: 63.4286,
        } as DragHandlePosition,
    },
} as const

/**
 * @function getDragHandleBasePosition
 * @description 获取拖拽手柄的基准位置
 * 提供类型安全的访问方式
 * @returns {DragHandlePosition} 基准位置坐标
 */
export const getDragHandleBasePosition = (): DragHandlePosition => {
    return { ...DRAG_HANDLE_CONFIG.BASE_POSITION }
}

/**
 * @function getDragHandleSize
 * @description 获取拖拽手柄的尺寸
 * @returns {DragHandleDimensions} 手柄尺寸
 */
export const getDragHandleSize = (): DragHandleDimensions => {
    return { ...DRAG_HANDLE_CONFIG.HANDLE_SIZE }
}

/**
 * @function getDragPadSize
 * @description 获取拖拽区域的尺寸
 * @returns {DragPadDimensions} 拖拽区域尺寸
 */
export const getDragPadSize = (): DragPadDimensions => {
    return { ...DRAG_HANDLE_CONFIG.DRAG_PAD_SIZE }
}

/**
 * @function getDefaultViewControl
 * @description 获取默认的视图控制状态
 * 用于组件初始化和重置功能
 * @returns {object} 包含 zoom 和 dragHandlePosition 的默认状态对象
 */
export const getDefaultViewControl = () => {
    return {
        zoom: DRAG_HANDLE_CONFIG.DEFAULT_VIEW_CONTROL.zoom,
        dragHandlePosition: { ...DRAG_HANDLE_CONFIG.DEFAULT_VIEW_CONTROL.dragHandlePosition },
    }
}

/**
 * @function calculatePercentageTransform
 * @description 计算拖拽手柄位置对应的百分比变换
 * 将像素偏移转换为Canvas视图的translate百分比值
 * @param {DragHandlePosition} currentPosition - 当前拖拽手柄位置
 * @param {DragHandlePosition} basePosition - 基准位置（可选，默认使用配置中的基准位置）
 * @param {DragPadDimensions} padSize - 拖拽区域尺寸（可选，默认使用配置中的尺寸）
 * @returns {object} 包含 translateX 和 translateY 百分比值的对象
 */
export const calculatePercentageTransform = (
    currentPosition: DragHandlePosition,
    basePosition: DragHandlePosition = DRAG_HANDLE_CONFIG.BASE_POSITION,
    padSize: DragPadDimensions = DRAG_HANDLE_CONFIG.DRAG_PAD_SIZE,
): { translateX: number; translateY: number } => {
    // 计算相对于基准位置的偏移量（像素）
    const deltaX = currentPosition.x - basePosition.x
    const deltaY = currentPosition.y - basePosition.y
    
    // 将像素偏移转换为百分比偏移
    const percentX = (deltaX / padSize.width) * 100
    const percentY = (deltaY / padSize.height) * 100
    
    // 应用反向逻辑（如果启用）
    const translateX = DRAG_HANDLE_CONFIG.TRANSFORM.INVERT_DIRECTION ? -percentX : percentX
    const translateY = DRAG_HANDLE_CONFIG.TRANSFORM.INVERT_DIRECTION ? -percentY : percentY
    
    return {
        translateX: Number(translateX.toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
        translateY: Number(translateY.toFixed(DRAG_HANDLE_CONFIG.TRANSFORM.PRECISION)),
    }
}