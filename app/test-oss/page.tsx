/**
 * OSS上传测试页面
 * 用于验证纯前端OSS上传功能
 */
'use client'

import { useState } from 'react'
import { uploadImageToOSS, preloadOSSSDK, isOSSSDKLoaded } from '@/app/utils/ossUploadBrowser'
import { exportImageAsBlob } from '@/app/utils/imageExport'
import { toast } from 'sonner'

export default function OSSUploadTest() {
    const [isUploading, setIsUploading] = useState(false)
    const [uploadResult, setUploadResult] = useState<string | null>(null)
    const [sdkStatus, setSdkStatus] = useState<'loading' | 'loaded' | 'error'>('loading')

    // 检查SDK状态
    const checkSDKStatus = async () => {
        if (isOSSSDKLoaded()) {
            setSdkStatus('loaded')
            return
        }

        try {
            const success = await preloadOSSSDK()
            setSdkStatus(success ? 'loaded' : 'error')
        } catch (error) {
            setSdkStatus('error')
            console.error('SDK加载失败:', error)
        }
    }

    // 测试图片上传（使用当前页面截图）
    const handleTestUpload = async () => {
        setIsUploading(true)
        setUploadResult(null)

        try {
            // 确保SDK已加载
            if (!isOSSSDKLoaded()) {
                toast.loading('正在加载OSS SDK...')
                await preloadOSSSDK()
                toast.dismiss()
            }

            // 导出当前页面为图片
            const blobResult = await exportImageAsBlob()
            if (!blobResult.success || !blobResult.blob) {
                throw new Error(blobResult.error || '图片导出失败')
            }

            toast.loading('正在上传到OSS...')

            // 上传到OSS
            const result = await uploadImageToOSS(blobResult.blob)
            
            if (result.success && result.url) {
                setUploadResult(result.url)
                toast.success('上传成功！')
            } else {
                throw new Error(result.error || '上传失败')
            }
        } catch (error) {
            console.error('上传测试失败:', error)
            toast.error(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
        } finally {
            setIsUploading(false)
            toast.dismiss()
        }
    }

    // 测试文件上传
    const handleFileUpload = async (file: File) => {
        if (!file.type.startsWith('image/')) {
            toast.error('请选择图片文件')
            return
        }

        setIsUploading(true)
        setUploadResult(null)

        try {
            // 确保SDK已加载
            if (!isOSSSDKLoaded()) {
                toast.loading('正在加载OSS SDK...')
                await preloadOSSSDK()
                toast.dismiss()
            }

            toast.loading('正在上传到OSS...')

            // 上传文件
            const result = await uploadImageToOSS(file, file.name)
            
            if (result.success && result.url) {
                setUploadResult(result.url)
                toast.success('文件上传成功！')
            } else {
                throw new Error(result.error || '上传失败')
            }
        } catch (error) {
            console.error('文件上传失败:', error)
            toast.error(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
        } finally {
            setIsUploading(false)
            toast.dismiss()
        }
    }

    return (
        <div className="min-h-screen bg-gray-50 py-12 px-4">
            <div className="max-w-2xl mx-auto">
                <div className="bg-white rounded-lg shadow-md p-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                        OSS上传功能测试
                    </h1>

                    {/* SDK状态 */}
                    <div className="mb-8 p-4 bg-gray-100 rounded-lg">
                        <h2 className="text-lg font-semibold mb-2">SDK状态</h2>
                        <div className="flex items-center gap-2">
                            <div 
                                className={`w-3 h-3 rounded-full ${
                                    sdkStatus === 'loaded' ? 'bg-green-500' :
                                    sdkStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                                }`}
                            />
                            <span>
                                {sdkStatus === 'loaded' && '✅ OSS SDK 已加载'}
                                {sdkStatus === 'error' && '❌ OSS SDK 加载失败'}
                                {sdkStatus === 'loading' && '🔄 检查SDK状态...'}
                            </span>
                            <button
                                onClick={checkSDKStatus}
                                className="ml-auto px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                            >
                                刷新状态
                            </button>
                        </div>
                    </div>

                    {/* 测试按钮 */}
                    <div className="space-y-4 mb-8">
                        <button
                            onClick={handleTestUpload}
                            disabled={isUploading || sdkStatus !== 'loaded'}
                            className="w-full px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                        >
                            {isUploading ? '上传中...' : '测试页面截图上传'}
                        </button>

                        {/* 文件上传 */}
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                                disabled={isUploading || sdkStatus !== 'loaded'}
                                className="hidden"
                                id="file-upload"
                            />
                            <label
                                htmlFor="file-upload"
                                className={`cursor-pointer inline-block px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 ${
                                    (isUploading || sdkStatus !== 'loaded') ? 'bg-gray-300 cursor-not-allowed' : ''
                                }`}
                            >
                                选择图片文件上传
                            </label>
                        </div>
                    </div>

                    {/* 上传结果 */}
                    {uploadResult && (
                        <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
                            <h3 className="text-lg font-semibold text-green-800 mb-2">上传成功！</h3>
                            <div className="space-y-2">
                                <p className="text-sm text-green-700">图片URL:</p>
                                <div className="flex items-center gap-2">
                                    <input
                                        type="text"
                                        value={uploadResult}
                                        readOnly
                                        className="flex-1 px-3 py-2 text-sm border border-green-300 rounded"
                                    />
                                    <button
                                        onClick={() => {
                                            navigator.clipboard.writeText(uploadResult)
                                            toast.success('链接已复制到剪贴板')
                                        }}
                                        className="px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600"
                                    >
                                        复制
                                    </button>
                                </div>
                                <a
                                    href={uploadResult}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                >
                                    在新窗口中查看图片
                                </a>
                            </div>
                        </div>
                    )}

                    {/* 说明信息 */}
                    <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h3 className="text-lg font-semibold text-blue-800 mb-2">功能说明</h3>
                        <ul className="text-sm text-blue-700 space-y-1">
                            <li>• 使用CDN版本的OSS SDK，避免Node.js依赖问题</li>
                            <li>• 支持页面截图和文件上传两种方式</li>
                            <li>• 自动生成唯一文件名，避免重名冲突</li>
                            <li>• 生成签名URL，支持私有存储桶访问</li>
                            <li>• 完整的错误处理和用户反馈</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    )
}