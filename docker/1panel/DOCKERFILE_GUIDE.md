# Dockerfile 使用指南

## 📋 文件说明

### `Dockerfile.1panel`
- **优化特性**：专为 1Panel 部署优化，使用国内镜像加速
- **镜像源**：
  - npm: 淘宝镜像 (registry.npmmirror.com)
  - Alpine: 阿里云镜像
- **Node 版本**：`node:20-alpine`
- **构建模式**：Next.js standalone 模式
- **镜像大小**：约 311MB

## 🎯 快速构建

```bash
# 构建生产镜像
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

## 🔧 构建选项

```bash
# 多架构构建（支持 amd64 和 arm64）
docker buildx build --platform linux/amd64,linux/arm64 -f docker/1panel/Dockerfile.1panel -t mockpix:latest .

# 指定标签版本
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:v1.0.0 .

# 导出镜像
docker save mockpix:latest | gzip > mockpix-latest.tar.gz
```

## 📊 技术细节

### 多阶段构建
1. **deps 阶段**：安装生产依赖
2. **builder 阶段**：编译 TypeScript 和构建 Next.js
3. **runner 阶段**：最小化运行环境

### 优化特性
- ✅ 使用 Alpine Linux 减小体积
- ✅ 非 root 用户运行（安全性）
- ✅ 健康检查配置
- ✅ 信号处理（dumb-init）
- ✅ standalone 模式（包含所有依赖）

## 🤔 常见问题

**Q: 为什么使用国内镜像源？**
A: 大幅提升构建速度，避免网络超时问题。

**Q: 如果需要在国外部署怎么办？**
A: 可以修改 Dockerfile，移除镜像源配置部分，或使用代理。

**Q: 镜像大小能否进一步优化？**
A: 当前已是优化后的大小。可考虑使用 distroless 镜像，但会增加复杂性。

---

*最后更新：2025-08*