# =============================================================================
# 1Panel Docker Compose 配置文件
# =============================================================================
# 说明：
# - 专为 1Panel 应用商店设计
# - 支持环境变量配置
# - 包含健康检查和资源限制
# - 支持数据持久化
# =============================================================================

# 1Panel 要求的版本格式
version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # Next.js 应用服务
  # -----------------------------------------------------------------------------
  next-wallpaper:
    # 容器名称，1Panel 中显示的名称
    container_name: ${CONTAINER_NAME:-next-wallpaper-1panel}
    
    # 镜像配置
    # 可以使用预构建镜像或从 Dockerfile 构建
    image: ${IMAGE_NAME:-next-wallpaper:1panel}
    
    # 如果需要从源码构建，取消下面的注释
    # build:
    #   context: ../..
    #   dockerfile: docker/1panel/Dockerfile.1panel
    
    # ---------------------------------------------------------------------------
    # 端口映射
    # ---------------------------------------------------------------------------
    ports:
      # 格式：宿主机端口:容器端口
      # 1Panel 会自动检测端口冲突
      - "${APP_PORT:-3000}:3000"
    
    # ---------------------------------------------------------------------------
    # 环境变量
    # ---------------------------------------------------------------------------
    # 这些变量可以在 1Panel 界面中配置
    environment:
      # Node.js 运行环境
      NODE_ENV: ${NODE_ENV:-production}
      
      # 应用配置
      PORT: 3000
      HOSTNAME: "0.0.0.0"
      
      # Next.js 配置
      NEXT_TELEMETRY_DISABLED: 1
      
      # 应用 URL（用于生成绝对路径）
      NEXT_PUBLIC_APP_URL: ${APP_URL:-http://localhost:3000}
      
      # 时区设置
      TZ: ${TZ:-Asia/Shanghai}
      
      # 自定义配置（可以在 1Panel 中添加）
      # CUSTOM_VAR: ${CUSTOM_VAR}
    
    # ---------------------------------------------------------------------------
    # 文件挂载
    # ---------------------------------------------------------------------------
    volumes:
      # 上传文件存储（如果应用需要持久化存储）
      # - ${DATA_PATH:-./data}/uploads:/app/uploads
      
      # 日志目录（便于在 1Panel 中查看）
      - ${DATA_PATH:-./data}/logs:/app/logs
      
      # 配置文件（如果需要自定义配置）
      # - ${CONFIG_PATH:-./config}/custom.json:/app/config/custom.json:ro
    
    # ---------------------------------------------------------------------------
    # 网络配置
    # ---------------------------------------------------------------------------
    networks:
      - next-wallpaper-network
    
    # ---------------------------------------------------------------------------
    # 健康检查
    # ---------------------------------------------------------------------------
    # 1Panel 会根据健康检查状态显示容器状态
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s          # 检查间隔
      timeout: 10s           # 超时时间
      retries: 3            # 重试次数
      start_period: 40s     # 启动等待时间
    
    # ---------------------------------------------------------------------------
    # 资源限制
    # ---------------------------------------------------------------------------
    # 防止容器占用过多资源
    deploy:
      resources:
        limits:
          cpus: '${CPU_LIMIT:-1}'        # CPU 限制（核数）
          memory: ${MEMORY_LIMIT:-512M}   # 内存限制
        reservations:
          cpus: '${CPU_RESERVE:-0.25}'    # CPU 预留
          memory: ${MEMORY_RESERVE:-256M} # 内存预留
    
    # ---------------------------------------------------------------------------
    # 重启策略
    # ---------------------------------------------------------------------------
    restart: ${RESTART_POLICY:-unless-stopped}
    
    # ---------------------------------------------------------------------------
    # 日志配置
    # ---------------------------------------------------------------------------
    logging:
      driver: "json-file"
      options:
        max-size: "${LOG_MAX_SIZE:-10m}"     # 单个日志文件最大大小
        max-file: "${LOG_MAX_FILES:-3}"      # 保留的日志文件数量
    
    # ---------------------------------------------------------------------------
    # 标签（1Panel 用于分类和管理）
    # ---------------------------------------------------------------------------
    labels:
      # 1Panel 识别标签
      onekey.app.name: "Next Wallpaper"
      onekey.app.version: "${APP_VERSION:-1.0.0}"
      onekey.app.description: "Next.js 壁纸应用"
      onekey.app.category: "Web"
      onekey.app.website: "https://github.com/yourusername/next-wallpaper"
      
      # Traefik 标签（如果使用 Traefik 反向代理）
      # traefik.enable: "true"
      # traefik.http.routers.next-wallpaper.rule: "Host(`${DOMAIN}`)"
      # traefik.http.services.next-wallpaper.loadbalancer.server.port: "3000"

# =============================================================================
# 网络定义
# =============================================================================
networks:
  next-wallpaper-network:
    driver: bridge
    name: next-wallpaper-1panel-network
    # 1Panel 会自动管理网络

# =============================================================================
# 卷定义（可选）
# =============================================================================
# volumes:
#   # 如果需要命名卷
#   app-data:
#     driver: local
#   app-logs:
#     driver: local

# =============================================================================
# 配置说明
# =============================================================================
# 环境变量：
# - CONTAINER_NAME: 容器名称（默认: next-wallpaper-1panel）
# - IMAGE_NAME: 镜像名称（默认: next-wallpaper:1panel）
# - APP_PORT: 应用端口（默认: 3000）
# - NODE_ENV: 运行环境（默认: production）
# - APP_URL: 应用访问 URL
# - DATA_PATH: 数据存储路径
# - CPU_LIMIT: CPU 限制
# - MEMORY_LIMIT: 内存限制
# - RESTART_POLICY: 重启策略
# =============================================================================