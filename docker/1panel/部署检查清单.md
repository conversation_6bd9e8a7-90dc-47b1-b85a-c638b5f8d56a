# ✅ MockPix 部署检查清单

> 部署到服务器 ************* 之前，请确认以下事项

## 📋 部署前检查

### 1. 域名配置 ✅
- [x] 域名 `mockpix.com` 已解析到 `*************`
- [x] 域名 `www.mockpix.com` 已解析到 `*************`
- [x] DNS 解析已生效（ping mockpix.com 应返回 *************）

### 2. 服务器准备 ✅
- [x] SSH 可以正常连接到 `*************`
- [x] 1Panel 已安装并可以访问
- [x] Docker 服务正常运行
- [x] 服务器有足够的磁盘空间（至少 2GB）
- [x] 服务器有足够的内存（至少 1GB）

### 3. 1Panel 配置 ✅
- [x] 已登录 1Panel 管理界面
- [x] Docker 功能正常
- [x] 已配置镜像加速器（如果需要）
- [x] SSL 证书管理已配置（用于 HTTPS）

### 4. 代码准备 ✅
- [x] 代码已推送到 GitHub: `https://github.com/gcordon/next_wallpaper`
- [x] 所有更改已提交
- [x] 本地测试通过

### 5. 配置文件检查 ✅
- [x] `.env.production` 已创建并配置
- [x] `SESSION_SECRET` 已更改为随机字符串
- [x] 端口配置正确（默认 3000）
- [x] 域名配置正确

## 🔐 SSL/HTTPS 配置 ✅

### 已完成配置
- [x] 在 1Panel 中申请了 SSL 证书
- [x] 证书已绑定域名 `mockpix.com` 和 `www.mockpix.com`
- [x] 已开启 HTTP 自动跳转 HTTPS
- [x] 反向代理配置完成

## 📦 文件准备

需要上传到服务器的文件：
- [ ] `docker/1panel/` 目录下的所有文件
- [ ] 构建好的 Docker 镜像（如果不在服务器构建）

## 🚀 部署步骤预览

1. **本地准备**
   ```bash
   # 提交代码到 GitHub
   git add .
   git commit -m "准备部署到生产环境"
   git push origin main
   ```

2. **构建镜像**
   ```bash
   # 在本地构建并导出
   ./scripts/1panel-deploy.sh --export
   ```

3. **上传文件**
   ```bash
   # 上传到服务器
   scp -r docker/1panel/* root@*************:/opt/1panel/apps/mockpix/
   ```

4. **服务器操作**
   - 登录 1Panel
   - 安装应用
   - 配置环境变量
   - 启动服务

5. **验证部署** ✅
   - [x] 访问 https://www.mockpix.com - 正常
   - [x] 访问 https://mockpix.com - 正常
   - [x] 检查所有功能正常
   - [x] 检查 HTTPS 证书有效
   - [x] HTTP 自动跳转 HTTPS

## ⚠️ 注意事项

1. **安全性**
   - 生产环境必须修改默认密码和密钥
   - 确保防火墙规则正确配置
   - 定期更新和打补丁

2. **备份**
   - 部署前备份现有数据（如果有）
   - 配置自动备份策略
   - 测试恢复流程

3. **监控**
   - 设置资源监控
   - 配置日志收集
   - 设置告警通知

## 📝 部署后任务

- [ ] 验证所有功能正常工作
- [ ] 配置自动备份
- [ ] 设置监控和告警
- [ ] 更新 DNS 记录（如果需要）
- [ ] 通知团队部署完成
- [ ] 记录部署版本和时间

## 🆘 问题处理

如果遇到问题：
1. 检查 Docker 日志：`docker logs mockpix-production`
2. 检查 1Panel 日志
3. 验证网络连接
4. 检查资源使用情况

## 📞 联系信息

- GitHub Issues: https://github.com/gcordon/next_wallpaper/issues
- 服务器 IP: *************
- 域名: https://www.mockpix.com

---

**部署时间**: 2025-08-18 01:40  
**部署人员**: zengruilin  
**版本号**: 1.0.0  
**部署分支**: story-1_完全使用shots样式-release-v1.1.0-1  
**部署状态**: ✅ 成功  
**备注**: 
- 成功部署到 https://www.mockpix.com
- 解决了 Docker 镜像架构兼容性问题
- HTTPS 已配置并正常工作