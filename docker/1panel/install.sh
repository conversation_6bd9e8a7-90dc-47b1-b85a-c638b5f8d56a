#!/bin/bash

# =============================================================================
# 1Panel 应用安装脚本
# =============================================================================
# 说明：
# - 此脚本会被 1Panel 在安装应用时自动执行
# - 执行环境：1Panel 服务器
# - 工作目录：应用安装目录
# =============================================================================

set -e

# -----------------------------------------------------------------------------
# 颜色输出定义
# -----------------------------------------------------------------------------
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# -----------------------------------------------------------------------------
# 日志函数
# -----------------------------------------------------------------------------
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# -----------------------------------------------------------------------------
# 环境检查
# -----------------------------------------------------------------------------
check_environment() {
    log_info "检查安装环境..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "找不到 docker-compose.yml 文件"
        exit 1
    fi
    
    # 检查端口占用
    local port="${APP_PORT:-3000}"
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用，请在环境变量中修改 APP_PORT"
    fi
    
    # 检查磁盘空间（至少需要 1GB）
    local available_space=$(df . | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 1048576 ]; then
        log_warning "磁盘空间不足 1GB，可能影响安装"
    fi
    
    log_success "环境检查通过"
}

# -----------------------------------------------------------------------------
# 加载配置
# -----------------------------------------------------------------------------
load_config() {
    log_info "加载配置文件..."
    
    # 如果存在 .env 文件，加载环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
        log_success "环境变量加载成功"
    else
        log_warning "未找到 .env 文件，使用默认配置"
    fi
    
    # 设置默认值
    export CONTAINER_NAME="${CONTAINER_NAME:-next-wallpaper-1panel}"
    export IMAGE_NAME="${IMAGE_NAME:-next-wallpaper:1panel}"
    export APP_PORT="${APP_PORT:-3000}"
    export DATA_PATH="${DATA_PATH:-./data}"
}

# -----------------------------------------------------------------------------
# 创建必要目录
# -----------------------------------------------------------------------------
create_directories() {
    log_info "创建必要目录..."
    
    # 创建数据目录
    mkdir -p "${DATA_PATH}/logs"
    mkdir -p "${DATA_PATH}/uploads"
    mkdir -p "${DATA_PATH}/config"
    
    # 设置权限
    chmod 755 "${DATA_PATH}"
    chmod 755 "${DATA_PATH}/logs"
    chmod 755 "${DATA_PATH}/uploads"
    
    log_success "目录创建成功"
}

# -----------------------------------------------------------------------------
# 拉取或构建镜像
# -----------------------------------------------------------------------------
prepare_image() {
    log_info "准备 Docker 镜像..."
    
    # 检查镜像是否存在
    if docker images | grep -q "${IMAGE_NAME%:*}"; then
        log_info "镜像已存在: $IMAGE_NAME"
    else
        # 尝试拉取镜像
        log_info "拉取镜像: $IMAGE_NAME"
        if ! docker pull "$IMAGE_NAME" 2>/dev/null; then
            # 如果拉取失败，尝试构建
            if [ -f "Dockerfile.1panel" ]; then
                log_warning "无法拉取镜像，尝试本地构建..."
                docker build -f Dockerfile.1panel -t "$IMAGE_NAME" . || {
                    log_error "镜像构建失败"
                    exit 1
                }
                log_success "镜像构建成功"
            else
                log_error "无法获取镜像，且找不到 Dockerfile"
                exit 1
            fi
        else
            log_success "镜像拉取成功"
        fi
    fi
}

# -----------------------------------------------------------------------------
# 启动应用
# -----------------------------------------------------------------------------
start_application() {
    log_info "启动应用容器..."
    
    # 使用 docker-compose 启动
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    # 等待容器启动
    log_info "等待容器启动..."
    sleep 5
    
    # 检查容器状态
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log_success "容器启动成功"
        
        # 等待应用就绪
        local max_attempts=30
        local attempt=0
        
        log_info "等待应用就绪..."
        while [ $attempt -lt $max_attempts ]; do
            if curl -s -o /dev/null -w "%{http_code}" "http://localhost:${APP_PORT}" | grep -q "200\|302"; then
                log_success "应用已就绪"
                break
            fi
            attempt=$((attempt + 1))
            sleep 2
        done
        
        if [ $attempt -eq $max_attempts ]; then
            log_warning "应用启动超时，请检查日志"
        fi
    else
        log_error "容器启动失败"
        docker-compose logs
        exit 1
    fi
}

# -----------------------------------------------------------------------------
# 显示安装信息
# -----------------------------------------------------------------------------
show_install_info() {
    echo
    echo "========================================"
    echo "     Next Wallpaper 安装成功！"
    echo "========================================"
    echo
    echo "访问地址: http://$(hostname -I | awk '{print $1}'):${APP_PORT}"
    echo "容器名称: $CONTAINER_NAME"
    echo "数据目录: $DATA_PATH"
    echo
    echo "管理命令:"
    echo "  查看日志: docker logs -f $CONTAINER_NAME"
    echo "  重启应用: docker-compose restart"
    echo "  停止应用: docker-compose down"
    echo
    echo "注意事项:"
    echo "1. 首次启动可能需要几分钟初始化"
    echo "2. 建议配置反向代理使用 HTTPS"
    echo "3. 定期备份 data 目录中的数据"
    echo
    echo "========================================"
}

# -----------------------------------------------------------------------------
# 错误处理
# -----------------------------------------------------------------------------
handle_error() {
    log_error "安装过程中发生错误"
    log_info "尝试清理..."
    
    # 停止并删除容器
    docker-compose down 2>/dev/null || true
    
    log_info "请检查错误信息后重试"
    exit 1
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    log_info "开始安装 Next Wallpaper..."
    
    # 设置错误处理
    trap handle_error ERR
    
    # 执行安装步骤
    check_environment
    load_config
    create_directories
    prepare_image
    start_application
    
    # 显示成功信息
    show_install_info
    
    log_success "安装完成！"
}

# -----------------------------------------------------------------------------
# 执行安装
# -----------------------------------------------------------------------------
main "$@"