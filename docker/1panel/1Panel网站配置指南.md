# 📖 1Panel 网站配置详细指南

> 本指南详细说明如何在 1Panel 中创建反向代理网站并配置 HTTPS

## 🎯 配置目标

将运行在 Docker 容器中的 Next.js 应用（端口 3000）通过反向代理发布到公网，并配置 HTTPS。

## 📝 前置条件

- ✅ Docker 容器已运行（mockpix-production）
- ✅ 应用在 3000 端口正常服务
- ✅ 域名已解析到服务器 IP
- ✅ 1Panel 已安装并可访问

## 🚀 创建反向代理网站

### 步骤 1：进入网站管理

1. 登录 1Panel 管理界面
2. 左侧菜单点击「网站」
3. 点击右上角「创建网站」按钮

### 步骤 2：选择网站类型

在弹出的创建窗口中，选择类型为「**反向代理**」

> 💡 反向代理类型说明：用于将外部请求代理到本机运行的服务

### 步骤 3：填写网站信息

#### 基本信息填写示例：

| 字段 | 填写内容 | 说明 |
|------|---------|------|
| **分组** | 默认 | 可选择或创建新分组 |
| **主域名** | www.mockpix.com | 主要访问域名 |
| **其他域名** | mockpix.com | 每行一个，支持多个域名 |
| **代号** | mockpix | 网站标识符，只能用字母数字 |
| **代理地址** | http://127.0.0.1:3000 | 必须包含 http:// 前缀 |
| **备注** | Next.js 壁纸应用 | 可选，方便识别 |

#### 填写要点：

1. **主域名**
   - 格式：`www.mockpix.com`
   - 不要加 `http://` 或 `https://`
   - 支持端口号：`domain.com:8080`

2. **其他域名**
   - 一行一个域名
   - 示例：
   ```
   mockpix.com
   api.mockpix.com
   ```
   - 支持通配符：`*.mockpix.com`
   - 支持 IP 地址：`*************`

3. **代号**
   - 只能包含：字母、数字、下划线
   - 长度：1-128 字符
   - 不能包含：`/\:*?'"<>|` 等特殊字符
   - 对应目录：`/opt/1panel/apps/openresty/openresty/www/sites/[代号]/`

4. **代理地址**
   - 格式：`http://127.0.0.1:端口`
   - 本地服务使用：`127.0.0.1` 或 `localhost`
   - 容器服务可用：容器名称作为主机名
   - 必须包含协议：`http://` 或 `https://`

### 步骤 4：高级配置（可选）

点击「高级设置」展开更多选项：

- **访问日志**：建议开启
- **错误日志**：建议开启
- **WebSocket**：如果应用需要，请开启
- **缓存**：静态资源可开启

### 步骤 5：创建网站

点击「确定」按钮创建网站。

## 🔒 配置 HTTPS

### 步骤 1：进入网站设置

1. 在网站列表中找到刚创建的网站
2. 点击网站名称或「设置」按钮

### 步骤 2：申请 SSL 证书

1. 选择「HTTPS」标签
2. 点击「申请证书」

#### Let's Encrypt 免费证书（推荐）

1. 选择证书类型：Let's Encrypt
2. 填写信息：
   - 邮箱：你的邮箱地址
   - 域名：自动填充（包含主域名和其他域名）
3. 点击「申请」
4. 等待验证完成（约 1-2 分钟）

#### 使用已有证书

1. 选择「导入证书」
2. 上传证书文件：
   - 证书文件（.crt 或 .pem）
   - 私钥文件（.key）
3. 点击「确定」

### 步骤 3：启用 HTTPS

1. 证书申请成功后，自动启用 HTTPS
2. 开启选项：
   - ✅ 启用 HTTPS
   - ✅ HTTP 自动跳转 HTTPS
   - ✅ 开启 HTTP/2（提升性能）

### 步骤 4：保存配置

点击「保存」使配置生效。

## ⚙️ 反向代理高级配置

### WebSocket 支持

如果应用使用 WebSocket（如实时通信），需要额外配置：

1. 在网站设置中找到「配置文件」
2. 在 `location /` 块中添加：

```nginx
proxy_http_version 1.1;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection 'upgrade';
proxy_cache_bypass $http_upgrade;
```

### 请求头配置

确保客户端真实 IP 和协议正确传递：

```nginx
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
```

### 超时配置

处理长时间运行的请求：

```nginx
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;
```

### 文件上传限制

如果需要上传大文件：

```nginx
client_max_body_size 100M;
```

## 🔍 验证配置

### 1. DNS 解析验证

```bash
# 检查域名解析
nslookup mockpix.com
dig mockpix.com

# 应返回服务器 IP
```

### 2. 端口连通性

```bash
# 检查 443 端口
telnet mockpix.com 443

# 检查 80 端口
telnet mockpix.com 80
```

### 3. SSL 证书验证

```bash
# 检查证书信息
openssl s_client -connect mockpix.com:443 -servername mockpix.com

# 使用 curl 测试
curl -I https://mockpix.com
```

### 4. 浏览器验证

1. 访问 http://mockpix.com → 应自动跳转到 https://
2. 检查浏览器地址栏的锁标志 🔒
3. 点击锁查看证书详情

## 🚨 常见问题

### 问题 1：502 Bad Gateway

**原因**：后端服务未运行或地址错误

**解决**：
```bash
# 检查容器运行状态
docker ps | grep mockpix

# 检查端口监听
netstat -tlnp | grep 3000

# 查看容器日志
docker logs mockpix-production
```

### 问题 2：证书申请失败

**原因**：域名解析未生效或防火墙阻止

**解决**：
1. 确认域名已解析到服务器
2. 检查防火墙 80/443 端口
3. 等待 DNS 生效（可能需要 10-30 分钟）

### 问题 3：网站无法访问

**检查清单**：
- [ ] Docker 容器正在运行
- [ ] 端口 3000 正常监听
- [ ] 反向代理地址正确
- [ ] 域名解析正确
- [ ] 防火墙规则正确
- [ ] 1Panel 服务正常

### 问题 4：HTTPS 不工作

**解决步骤**：
1. 检查证书是否成功申请
2. 确认 443 端口开放
3. 查看 nginx 错误日志
4. 重启 OpenResty 服务

## 📊 性能优化

### 1. 启用 Gzip 压缩

在网站配置中添加：
```nginx
gzip on;
gzip_types text/plain text/css text/javascript application/javascript application/json;
gzip_min_length 1000;
```

### 2. 静态资源缓存

```nginx
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 30d;
    add_header Cache-Control "public, immutable";
}
```

### 3. 限流配置

防止恶意请求：
```nginx
limit_req_zone $binary_remote_addr zone=one:10m rate=10r/s;
limit_req zone=one burst=20;
```

## 📋 配置检查清单

- [ ] 反向代理网站已创建
- [ ] 代理地址配置正确
- [ ] SSL 证书已申请
- [ ] HTTPS 已启用
- [ ] HTTP 跳转已开启
- [ ] WebSocket 已配置（如需要）
- [ ] 访问日志已开启
- [ ] 网站可正常访问

## 🔗 相关文档

- [快速部署指南](./快速部署指南.md)
- [部署成功验证清单](./部署成功验证清单.md)
- [架构兼容性说明](./架构兼容性说明.md)
- [1Panel 官方文档](https://1panel.cn/docs/)

---

*最后更新：2025-08-18*  
*适用版本：1Panel v2.x*