# 📦 Next Wallpaper 1Panel 部署指南

> 本指南详细说明如何在 1Panel 中部署 Next Wallpaper 应用

## 📋 目录

1. [快速开始](#快速开始)
2. [部署方式](#部署方式)
3. [配置说明](#配置说明)
4. [常见问题](#常见问题)
5. [维护指南](#维护指南)

---

## 🚀 快速开始

### 前置要求

- ✅ 1Panel 已安装并运行
- ✅ Docker 服务正常
- ✅ 至少 512MB 可用内存
- ✅ 至少 1GB 可用磁盘空间

### 三步部署

```bash
# 1. 构建应用包
cd /path/to/next_wallpaper
./scripts/1panel-deploy.sh

# 2. 上传到 1Panel 服务器
scp -r dist/1panel/* root@your-server:/opt/1panel/apps/next-wallpaper/

# 3. 在 1Panel 界面安装
# 登录 1Panel → 应用商店 → 本地应用 → 安装 Next Wallpaper
```

---

## 📝 部署方式

### 方式一：使用预构建镜像（推荐）

最简单快速的部署方式，适合大多数用户。

#### 步骤：

1. **准备应用文件**
   ```bash
   # 在项目目录执行
   ./scripts/1panel-deploy.sh --export
   ```

2. **上传到 1Panel**
   - 将 `dist/1panel/` 目录下的文件上传到 1Panel 服务器
   - 路径：`/opt/1panel/apps/next-wallpaper/`

3. **在 1Panel 安装**
   - 登录 1Panel 管理界面
   - 进入「应用商店」→「本地应用」
   - 找到 Next Wallpaper，点击「安装」
   - 配置环境变量
   - 启动应用

### 方式二：从源码构建

适合需要自定义构建的高级用户。

#### 步骤：

1. **上传源码**
   ```bash
   # 打包源码
   tar -czf next-wallpaper-source.tar.gz \
     --exclude=node_modules \
     --exclude=.next \
     --exclude=.git \
     .
   
   # 上传到服务器
   scp next-wallpaper-source.tar.gz root@server:/tmp/
   ```

2. **在服务器构建**
   ```bash
   # SSH 到服务器
   ssh root@server
   
   # 解压源码
   cd /opt/1panel/apps/
   tar -xzf /tmp/next-wallpaper-source.tar.gz -C next-wallpaper/
   
   # 构建镜像
   cd next-wallpaper
   docker build -f docker/1panel/Dockerfile.1panel -t next-wallpaper:1panel .
   ```

3. **配置并启动**
   ```bash
   # 复制环境变量配置
   cp docker/1panel/.env.example .env
   
   # 编辑配置
   vi .env
   
   # 通过 1Panel 或 docker-compose 启动
   docker-compose -f docker/1panel/docker-compose.yml up -d
   ```

### 方式三：使用 Docker Hub

如果镜像已推送到 Docker Hub。

#### 步骤：

1. **修改 docker-compose.yml**
   ```yaml
   services:
     next-wallpaper:
       image: yourusername/next-wallpaper:1panel  # 使用 Docker Hub 镜像
       # 移除 build 部分
   ```

2. **在 1Panel 中配置**
   - 上传修改后的 docker-compose.yml
   - 配置环境变量
   - 启动应用

---

## ⚙️ 配置说明

### 环境变量配置

主要环境变量说明：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `APP_PORT` | 3000 | 应用访问端口 |
| `NODE_ENV` | production | 运行环境 |
| `APP_URL` | http://localhost:3000 | 应用访问地址 |
| `TZ` | Asia/Shanghai | 时区设置 |
| `CPU_LIMIT` | 1 | CPU 限制（核数） |
| `MEMORY_LIMIT` | 512M | 内存限制 |

### 端口配置

```yaml
# 修改端口映射
ports:
  - "8080:3000"  # 将应用映射到 8080 端口
```

### 数据持久化

应用数据存储在以下目录：

```
/opt/1panel/apps/next-wallpaper/
├── data/
│   ├── logs/       # 日志文件
│   ├── uploads/    # 上传的图片
│   └── config/     # 配置文件
```

### 资源限制

根据服务器配置调整：

```yaml
# 小型服务器（1核1G）
CPU_LIMIT=0.5
MEMORY_LIMIT=256M

# 中型服务器（2核4G）
CPU_LIMIT=1
MEMORY_LIMIT=1G

# 大型服务器（4核8G+）
CPU_LIMIT=2
MEMORY_LIMIT=2G
```

---

## 🔧 常见问题

### Q1: 端口被占用

**问题**：启动失败，提示端口 3000 被占用

**解决方案**：
```bash
# 方法 1：修改 .env 文件
APP_PORT=3001  # 改为其他端口

# 方法 2：在 1Panel 界面修改环境变量
# 将 APP_PORT 改为未使用的端口
```

### Q2: 镜像拉取失败

**问题**：Docker 镜像拉取超时

**解决方案**：
```bash
# 配置 Docker 镜像加速器
# 在 1Panel → 容器 → 设置 → 镜像加速器中配置

# 或手动配置
cat > /etc/docker/daemon.json <<EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn"
  ]
}
EOF

systemctl restart docker
```

### Q3: 内存不足

**问题**：容器频繁重启，日志显示内存不足

**解决方案**：
```bash
# 降低内存限制
MEMORY_LIMIT=256M
MEMORY_RESERVE=128M

# 或增加服务器内存
```

### Q4: 访问慢或超时

**问题**：应用访问速度慢

**解决方案**：
1. 检查服务器性能
2. 配置 Nginx 反向代理
3. 启用 CDN 加速
4. 优化 Docker 资源配置

---

## 📊 维护指南

### 日常维护

#### 查看日志
```bash
# 通过 1Panel 查看
# 应用 → Next Wallpaper → 日志

# 或命令行
docker logs -f next-wallpaper-1panel --tail=100
```

#### 重启应用
```bash
# 通过 1Panel 界面
# 应用 → Next Wallpaper → 重启

# 或命令行
docker-compose -f /opt/1panel/apps/next-wallpaper/docker-compose.yml restart
```

#### 备份数据
```bash
# 备份脚本
#!/bin/bash
BACKUP_DIR="/backup/next-wallpaper"
APP_DIR="/opt/1panel/apps/local/next-wallpaper"

mkdir -p $BACKUP_DIR
tar -czf "$BACKUP_DIR/backup-$(date +%Y%m%d).tar.gz" "$APP_DIR/data"
```

### 更新应用

#### 方法 1：通过 1Panel
1. 停止应用
2. 更新镜像
3. 重启应用

#### 方法 2：命令行更新
```bash
cd /opt/1panel/apps/local/next-wallpaper

# 备份数据
cp -r data data.backup

# 拉取新镜像
docker pull next-wallpaper:latest

# 重启应用
docker-compose down
docker-compose up -d
```

### 性能优化

#### 1. 启用 Nginx 反向代理
```nginx
server {
    listen 80;
    server_name wallpaper.example.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 30d;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

#### 2. 配置 HTTPS
```bash
# 使用 1Panel 的证书管理功能
# 或使用 Certbot
certbot --nginx -d wallpaper.example.com
```

#### 3. 监控设置
```yaml
# 添加到 docker-compose.yml
labels:
  - "prometheus.io/scrape=true"
  - "prometheus.io/port=3000"
  - "prometheus.io/path=/metrics"
```

---

## 🐛 故障排查

### 调试模式

启用调试模式查看详细日志：

```bash
# 修改 .env
NODE_ENV=development
LOG_LEVEL=debug

# 重启容器
docker-compose restart
```

### 健康检查

```bash
# 检查容器状态
docker ps -a | grep next-wallpaper

# 检查健康状态
docker inspect next-wallpaper-1panel --format='{{.State.Health.Status}}'

# 手动健康检查
curl -f http://localhost:3000 || echo "Health check failed"
```

### 资源使用

```bash
# 查看资源使用
docker stats next-wallpaper-1panel

# 查看详细信息
docker inspect next-wallpaper-1panel
```

---

## 📚 相关文档

- [Docker 部署文档](../docs/README.md)
- [项目主文档](../../README.md)
- [1Panel 官方文档](https://1panel.cn/docs/)

---

## 🤝 支持

### 获取帮助

1. 查看 [常见问题](#常见问题)
2. 查看 [1Panel 社区](https://bbs.fit2cloud.com/c/1p/7)
3. 提交 [GitHub Issue](https://github.com/gcordon/next_wallpaper/issues)

### 反馈问题

提交问题时请提供：
- 1Panel 版本
- Docker 版本
- 错误日志
- 环境变量配置（隐藏敏感信息）

---

## 📝 更新日志

### v1.0.0 (2024-08)
- ✨ 初始版本
- ✨ 支持 1Panel 一键部署
- ✨ 支持环境变量配置
- ✨ 支持健康检查
- ✨ 支持数据持久化

---

*Last Updated: 2024-08*