# =============================================================================
# 1Panel 简化版 Docker Compose 配置
# =============================================================================
# 说明：
# - 这是实际运行成功的简化版本
# - 适用于 1Panel 编排管理
# - 已验证在生产环境正常运行
# =============================================================================

version: '3'

services:
  mockpix:
    # 镜像名称（需要在服务器上构建）
    image: mockpix:latest
    
    # 容器名称
    container_name: mockpix-production
    
    # 端口映射
    ports:
      - "3000:3000"
    
    # 环境变量
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - TZ=Asia/Shanghai
    
    # 引用 1Panel 的环境变量文件
    env_file:
      - 1panel.env
    
    # 重启策略
    restart: unless-stopped

# =============================================================================
# 使用说明：
# =============================================================================
# 1. 在服务器上构建镜像：
#    docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
#
# 2. 在 1Panel 创建编排时使用此配置
#
# 3. 环境变量（在 1Panel 界面配置）：
#    APP_URL=https://www.mockpix.com
#    DOMAIN=www.mockpix.com
#    SESSION_SECRET=你的随机密钥
# =============================================================================