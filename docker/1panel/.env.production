# =============================================================================
# MockPix 生产环境配置
# =============================================================================
# 服务器信息：
# - IP: *************
# - 域名: https://www.mockpix.com / https://mockpix.com
# - GitHub: https://github.com/gcordon/next_wallpaper
# =============================================================================

# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
# 容器名称
CONTAINER_NAME=mockpix-production

# 镜像名称和标签
IMAGE_NAME=mockpix:latest

# 应用版本
APP_VERSION=1.0.0

# -----------------------------------------------------------------------------
# 网络配置
# -----------------------------------------------------------------------------
# 应用访问端口（1Panel 可能会使用其他端口，根据实际情况调整）
APP_PORT=3000

# 应用访问 URL（使用 HTTPS）
APP_URL=https://www.mockpix.com

# 主域名配置
DOMAIN=www.mockpix.com

# 备用域名
ALT_DOMAIN=mockpix.com

# -----------------------------------------------------------------------------
# 运行环境
# -----------------------------------------------------------------------------
# 生产环境模式
NODE_ENV=production

# 时区设置
TZ=Asia/Shanghai

# -----------------------------------------------------------------------------
# 资源限制（根据服务器配置调整）
# -----------------------------------------------------------------------------
# CPU 限制
CPU_LIMIT=2

# 内存限制
MEMORY_LIMIT=1G

# CPU 预留
CPU_RESERVE=0.5

# 内存预留
MEMORY_RESERVE=512M

# -----------------------------------------------------------------------------
# 存储配置
# -----------------------------------------------------------------------------
# 数据存储路径
DATA_PATH=/opt/1panel/apps/mockpix/data

# 配置文件路径
CONFIG_PATH=/opt/1panel/apps/mockpix/config

# 上传文件存储路径
UPLOAD_PATH=/opt/1panel/apps/mockpix/data/uploads

# -----------------------------------------------------------------------------
# 日志配置
# -----------------------------------------------------------------------------
# 单个日志文件最大大小
LOG_MAX_SIZE=50m

# 保留的日志文件数量
LOG_MAX_FILES=10

# 日志级别
LOG_LEVEL=info

# -----------------------------------------------------------------------------
# 重启策略
# -----------------------------------------------------------------------------
# 生产环境始终重启
RESTART_POLICY=always

# -----------------------------------------------------------------------------
# Next.js 生产配置
# -----------------------------------------------------------------------------
# 禁用遥测
NEXT_TELEMETRY_DISABLED=1

# 公开的 API URL
NEXT_PUBLIC_API_URL=https://www.mockpix.com

# 站点名称
NEXT_PUBLIC_SITE_NAME=MockPix

# -----------------------------------------------------------------------------
# 安全配置（生产环境必须设置）
# -----------------------------------------------------------------------------
# 会话密钥（请更改为随机字符串）
SESSION_SECRET=please_change_this_to_a_random_string_in_production

# API 密钥（如果需要）
# API_SECRET_KEY=your_api_secret_key_here

# -----------------------------------------------------------------------------
# CDN 配置（可选）
# -----------------------------------------------------------------------------
# CDN_URL=https://cdn.mockpix.com

# -----------------------------------------------------------------------------
# 监控配置（可选）
# -----------------------------------------------------------------------------
# 启用监控
ENABLE_MONITORING=true

# 监控端口
METRICS_PORT=9090

# -----------------------------------------------------------------------------
# 备份配置
# -----------------------------------------------------------------------------
# 自动备份
AUTO_BACKUP=true

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份路径
BACKUP_PATH=/opt/1panel/apps/mockpix/backups

# =============================================================================
# 重要提示：
# =============================================================================
# 1. 部署前请修改 SESSION_SECRET 为随机字符串
# 2. 确保域名已经解析到服务器 IP (*************)
# 3. 1Panel 中需要配置 SSL 证书实现 HTTPS
# 4. 建议使用 Nginx 反向代理处理 SSL
# =============================================================================