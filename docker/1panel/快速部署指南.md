# 🚀 MockPix 快速部署指南

> 部署到生产服务器 ************* (mockpix.com)

## 📍 部署信息

- **服务器 IP**: *************
- **域名**: https://www.mockpix.com / https://mockpix.com
- **GitHub**: https://github.com/gcordon/next_wallpaper
- **部署方式**: 1Panel + Docker

---

## 🎯 第一步：本地准备（在你的开发机器上）

### 1.1 提交代码到 GitHub

```bash
# 进入项目目录
cd /Users/<USER>/2025_code/next_wallpaper

# 查看状态
git status

# 添加所有更改
git add .

# 提交更改
git commit -m "feat: 添加 1Panel 部署配置，准备部署到 mockpix.com"

# 推送到 GitHub
git push origin main
```

### 1.2 ⚠️ 架构兼容性说明

**重要提示**：由于 Mac（特别是 M1/M2/M3 芯片）与服务器架构不同，建议直接在服务器上构建镜像。

- **Mac M系列芯片**: ARM64 架构
- **服务器（Intel/AMD）**: x86_64/AMD64 架构
- **解决方案**: 在服务器上构建，确保架构匹配

如果需要在本地构建，请使用：
```bash
# 构建 AMD64 架构镜像（适用于 Mac M系列芯片）
docker buildx build --platform linux/amd64 \
  -f docker/1panel/Dockerfile.1panel \
  -t mockpix:latest .
```

---

## 🖥️ 第二步：服务器配置（在服务器上操作）

### 2.1 连接到服务器

```bash
# SSH 连接到服务器
ssh root@*************
```

### 2.2 准备应用目录

```bash
# 创建应用目录
mkdir -p /opt/1panel/apps/mockpix
cd /opt/1panel/apps/mockpix

# 如果是从 GitHub 克隆（推荐）
<NAME_EMAIL>:gcordon/next_wallpaper.git .

# 切换到正确的分支（如果不是 main）
# git checkout your-branch-name
```

### 2.3 配置生产环境

```bash
# 复制 1Panel 配置文件
cp -r docker/1panel/* ./

# 使用生产环境配置
cp docker/1panel/.env.production .env

# 编辑配置文件，修改 SESSION_SECRET
nano .env
# 将 SESSION_SECRET 改为随机字符串，例如：
# SESSION_SECRET=$(openssl rand -base64 32)

# 确保脚本有执行权限
chmod +x *.sh
```

### 2.4 构建镜像（推荐在服务器上构建）

```bash
# 【推荐】直接在服务器构建，避免架构问题
cd /opt/1panel/apps/mockpix
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .

# 验证镜像
docker images | grep mockpix
# 应该看到 mockpix:latest 镜像
```

⚠️ **为什么在服务器构建**：
- 确保镜像架构与服务器匹配
- 避免 "exec format error" 错误
- 构建过程约 3-5 分钟

---

## 🎮 第三步：1Panel 部署

### 3.1 通过 1Panel 界面操作

1. **登录 1Panel**
   - 访问: http://*************:1panel端口
   - 使用你的 1Panel 账号登录

2. **进入容器管理**
   - 左侧菜单 → 容器 → 编排

3. **创建编排项目**
   - 点击「创建编排」
   - 选择「编排」选项（手动输入）
   - **编排内容**（粘贴以下内容，注意第一行不能有空格）：
   ```yaml
   version: '3'
   services:
     mockpix:
       image: mockpix:latest
       container_name: mockpix-production
       ports:
         - "3000:3000"
       environment:
         - NODE_ENV=production
         - PORT=3000
         - HOSTNAME=0.0.0.0
         - TZ=Asia/Shanghai
       env_file:
         - 1panel.env
       restart: unless-stopped
   ```
   
4. **配置环境变量**（在环境变量输入框）：
   ```
   APP_URL=https://www.mockpix.com
   DOMAIN=www.mockpix.com
   SESSION_SECRET=生成的随机密钥
   ```

5. **启动应用**
   - 点击「确定」创建
   - 在编排列表中找到 mockpix
   - 点击进入详情页，点击「启动」按钮
   - 查看日志确认显示 "Ready"

### 3.2 或通过命令行操作

```bash
cd /opt/1panel/apps/mockpix

# 启动服务
docker-compose -f docker/1panel/docker-compose.yml up -d

# 查看日志
docker-compose -f docker/1panel/docker-compose.yml logs -f

# 检查容器状态
docker ps | grep mockpix
```

---

## 🔒 第四步：创建网站并配置 HTTPS

### 4.1 在 1Panel 创建反向代理网站

1. **进入网站管理**
   - 1Panel → 网站 → 点击「创建网站」

2. **选择反向代理类型**
   - 类型：选择「反向代理」

3. **填写网站信息**
   ```
   分组：默认
   主域名：www.mockpix.com
   其他域名：mockpix.com （一行一个）
   代号：mockpix
   代理地址：http://127.0.0.1:3000
   备注：Next.js 壁纸应用
   ```

4. **点击确定创建**

### 4.2 配置 HTTPS

1. **进入网站设置**
   - 在网站列表找到 mockpix.com
   - 点击网站名称进入设置

2. **配置 SSL 证书**
   - 选择「HTTPS」标签
   - 如已申请证书：选择已有证书
   - 如未申请：点击申请 Let's Encrypt 证书

3. **开启 HTTPS 跳转**
   - 开启「HTTP 自动跳转 HTTPS」
   - 保存设置

### 4.2 Nginx 配置（如果需要手动配置）

```bash
# 创建 Nginx 配置
cat > /etc/nginx/sites-available/mockpix.conf << 'EOF'
server {
    listen 80;
    server_name mockpix.com www.mockpix.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name mockpix.com www.mockpix.com;
    
    # SSL 证书（1Panel 自动申请的路径）
    ssl_certificate /opt/1panel/ssl/mockpix.com/fullchain.pem;
    ssl_certificate_key /opt/1panel/ssl/mockpix.com/privkey.pem;
    
    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    
    # 反向代理到 Next.js 应用
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 静态资源缓存
    location /_next/static {
        proxy_pass http://127.0.0.1:3000;
        proxy_cache_valid 60m;
        add_header Cache-Control "public, max-age=31536000, immutable";
    }
}
EOF

# 启用配置
ln -s /etc/nginx/sites-available/mockpix.conf /etc/nginx/sites-enabled/
nginx -t && nginx -s reload
```

---

## ✅ 第五步：验证部署

### 5.1 检查服务状态

```bash
# 检查容器运行状态
docker ps | grep mockpix

# 查看容器日志
docker logs mockpix-production --tail=50

# 测试本地访问
curl http://localhost:3000

# 测试域名访问
curl https://www.mockpix.com
```

### 5.2 浏览器访问测试

1. 访问 https://www.mockpix.com
2. 检查 HTTPS 证书是否有效（🔒 图标）
3. 测试主要功能：
   - 页面加载正常
   - 图片上传功能
   - 设备预览功能
   - 导出功能

---

## 🔧 常用运维命令

```bash
# 查看日志
docker logs -f mockpix-production

# 重启服务
docker-compose -f docker/1panel/docker-compose.yml restart

# 停止服务
docker-compose -f docker/1panel/docker-compose.yml down

# 进入容器调试
docker exec -it mockpix-production sh

# 查看资源使用
docker stats mockpix-production

# 备份数据
tar -czf mockpix-backup-$(date +%Y%m%d).tar.gz /opt/1panel/apps/mockpix/data
```

---

## 🚨 故障排查

### 如果网站无法访问

1. **检查域名解析**
   ```bash
   nslookup mockpix.com
   # 应该返回 *************
   ```

2. **检查防火墙**
   ```bash
   # 确保端口开放
   firewall-cmd --list-all
   # 或
   iptables -L -n
   ```

3. **检查容器状态**
   ```bash
   docker ps -a | grep mockpix
   docker logs mockpix-production
   ```

4. **检查 Nginx 配置**
   ```bash
   nginx -t
   systemctl status nginx
   ```

### 常见错误解决

| 错误 | 解决方法 |
|------|---------|
| 502 Bad Gateway | 检查容器是否运行，端口是否正确 |
| 证书错误 | 重新申请 SSL 证书 |
| 容器一直重启 | 查看日志，可能是内存不足 |
| 页面加载慢 | 检查服务器资源，优化配置 |
| Docker 构建失败 | Dockerfile 已内置国内加速，检查网络 |
| ESLint/TypeScript 错误 | 在 next.config.ts 配置忽略构建错误 |
| 网络超时 | 配置 Docker 镜像加速器 |

---

## 📱 部署成功后

1. **设置监控**
   - 1Panel 自带监控
   - 或使用云服务商的监控

2. **配置备份**
   ```bash
   # 添加定时备份任务
   crontab -e
   # 每天凌晨 2 点备份
   0 2 * * * /opt/1panel/apps/mockpix/backup.sh
   ```

3. **优化性能**
   - 配置 CDN（可选）
   - 调整资源限制
   - 启用缓存

---

## 🎉 恭喜！

如果一切顺利，你的网站应该已经可以通过 https://www.mockpix.com 访问了！

**需要帮助？**
- 查看日志：`docker logs mockpix-production`
- GitHub Issues: https://github.com/gcordon/next_wallpaper/issues

---

*部署日期: 2025-08*  
*文档版本: 1.2*  
*更新内容: 统一使用国内优化版 Dockerfile*