# 🚀 MockPix 立即部署步骤

> 由于 SSL 证书已配置，可以直接开始部署！

## 📌 当前信息确认
- ✅ 服务器: 8.138.135.223
- ✅ 域名: www.mockpix.com / mockpix.com
- ✅ HTTPS 证书: 已在 1Panel 配置
- ✅ GitHub: **************:gcordon/next_wallpaper.git

---

## 🎯 第一步：本地准备（5分钟）

### 1.1 提交代码到 GitHub

```bash
# 在你的本地项目目录执行
cd /Users/<USER>/2025_code/next_wallpaper

# 提交所有 Docker 配置
git add .
git commit -m "feat: 添加 1Panel 部署配置 - 准备部署到 mockpix.com"
git push origin story-1_完全使用shots样式-release-v1.1.0-1
```

### 1.2 准备部署（无需本地构建）

```bash
# 确保代码已提交到 GitHub
git status
# 如有未提交的更改，请先提交
```

⚠️ **架构说明**：
- 由于 Mac M系列芯片（ARM64）与服务器（AMD64）架构不同
- 建议直接在服务器上构建镜像，避免架构兼容性问题

---

## 🖥️ 第二步：服务器部署（10分钟）

### 2.1 连接服务器

```bash
# 新开一个终端窗口
ssh root@8.138.135.223
```

### 2.2 准备应用目录

```bash
# 创建应用目录
mkdir -p /opt/1panel/apps/mockpix
cd /opt/1panel/apps/mockpix

# 克隆代码（使用 HTTPS 避免 SSH 密钥问题）
<NAME_EMAIL>:gcordon/next_wallpaper.git .

# 如果需要切换到特定分支
git checkout story-1_完全使用shots样式-release-v1.1.0-1
```

### 2.3 构建镜像（在服务器上）

```bash
# 在服务器上构建镜像，确保架构匹配
cd /opt/1panel/apps/mockpix
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .

# 验证镜像构建成功
docker images | grep mockpix
echo "✅ 镜像构建完成"
```

### 2.4 配置环境

```bash
# 生成随机密钥
SESSION_SECRET=$(openssl rand -base64 32)
echo "SESSION_SECRET: $SESSION_SECRET"

# 记下这个密钥，稍后在 1Panel 界面中使用
```

---

## 🎮 第三步：启动应用（2分钟）

### 3.1 在 1Panel 界面创建编排

1. 登录 1Panel 管理界面
2. 进入「容器」→「编排」
3. 点击「创建编排」
4. 选择「编排」选项（手动输入）
5. 粘贴以下 docker-compose.yml 内容：

```yaml
version: '3'
services:
  mockpix:
    image: mockpix:latest
    container_name: mockpix-production
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
      - TZ=Asia/Shanghai
    env_file:
      - 1panel.env
    restart: unless-stopped
```

6. 在环境变量框中输入：
```
APP_URL=https://www.mockpix.com
DOMAIN=www.mockpix.com
SESSION_SECRET=上面生成的密钥
```

7. 点击确定创建，然后启动编排

### 3.2 验证容器运行

```bash
# 检查容器状态
docker ps | grep mockpix

# 测试本地访问
curl -I http://localhost:3000
# 应该返回 200 OK
```

---

## 🌐 第四步：创建网站并配置反向代理（5分钟）

### 4.1 创建反向代理网站

1. **登录 1Panel 管理界面**
2. **进入网站管理**
   - 左侧菜单 → 「网站」
   - 点击「创建网站」

3. **填写创建表单**
   - **类型**：反向代理
   - **分组**：默认
   - **主域名**：`www.mockpix.com`
   - **其他域名**：`mockpix.com`（换行输入）
   - **代号**：`mockpix`
   - **代理地址**：`http://127.0.0.1:3000`
   - **备注**：Next.js 壁纸应用

4. **点击确定创建**

### 4.2 配置 HTTPS

1. **进入网站设置**
   - 在网站列表中点击 mockpix.com

2. **配置 SSL 证书**
   - 选择「HTTPS」标签
   - 申请或选择已有证书
   - 开启「HTTP 自动跳转 HTTPS」

3. **保存设置**

### 4.2 或使用命令行快速配置

```bash
# 如果 1Panel 使用 OpenResty
cat > /www/sites/mockpix.com/proxy.conf << 'EOF'
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}

location /_next/static {
    proxy_pass http://127.0.0.1:3000;
    proxy_cache_valid 60m;
    add_header Cache-Control "public, max-age=31536000, immutable";
}
EOF

# 重载配置
nginx -s reload
```

---

## ✅ 第五步：验证部署（1分钟）

### 5.1 测试访问

```bash
# 在服务器上测试
curl -I https://www.mockpix.com
# 应该返回 200 OK

# 查看应用日志
docker logs mockpix-production --tail=20
```

### 5.2 浏览器访问

1. 打开浏览器访问: https://www.mockpix.com
2. 检查功能:
   - ✅ 页面正常加载
   - ✅ HTTPS 锁标志显示
   - ✅ 图片上传功能
   - ✅ 设备预览功能
   - ✅ 导出功能

---

## 🎉 部署完成！

你的应用现在应该可以通过 https://www.mockpix.com 访问了！

### 📊 快速命令参考

```bash
# 查看状态
docker ps | grep mockpix

# 查看日志
docker logs -f mockpix-production

# 重启应用
docker-compose -f docker/1panel/docker-compose.yml restart

# 查看资源使用
docker stats mockpix-production
```

### 🚨 如果遇到问题

1. **网站无法访问**: 检查防火墙和安全组规则
2. **502 错误**: 检查容器是否运行 `docker ps`
3. **证书问题**: 在 1Panel 中重新配置证书

---

## 📝 部署记录

- 部署时间: 2025-08-17
- 部署版本: 1.0.0
- 部署分支: story-1_完全使用shots样式-release-v1.1.0-1

---

**需要帮助？** 查看详细日志: `docker logs mockpix-production`