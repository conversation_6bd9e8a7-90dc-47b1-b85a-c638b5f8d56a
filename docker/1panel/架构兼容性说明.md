# 🔧 Docker 镜像架构兼容性说明

## 📌 问题描述

在部署过程中可能遇到的错误：
```
exec /usr/bin/dumb-init: exec format error
```

这个错误表明 Docker 镜像的架构与服务器架构不匹配。

## 🖥️ 架构差异

### Mac 开发环境
- **Apple Silicon (M1/M2/M3)**: ARM64 架构
- **Intel Mac**: x86_64/AMD64 架构

### 服务器环境
- **大多数云服务器**: x86_64/AMD64 架构
- **ARM 服务器**: ARM64 架构（较少见）

## ✅ 解决方案

### 方案 1：在服务器上构建（推荐）

直接在目标服务器上构建镜像，确保架构完全匹配：

```bash
# SSH 到服务器
ssh root@your-server-ip

# 克隆代码
<NAME_EMAIL>:gcordon/next_wallpaper.git
cd next_wallpaper

# 在服务器上构建
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

**优点**：
- ✅ 架构自动匹配
- ✅ 不需要传输大镜像文件
- ✅ 构建过程简单

**缺点**：
- ⚠️ 需要服务器有足够的资源进行构建
- ⚠️ 首次构建时间较长（3-5分钟）

### 方案 2：本地构建多架构镜像

在 Mac 上构建指定架构的镜像：

```bash
# 启用 buildx
docker buildx create --use

# 构建 AMD64 架构镜像
docker buildx build --platform linux/amd64 \
  -f docker/1panel/Dockerfile.1panel \
  -t mockpix:latest-amd64 \
  --load .

# 导出镜像
docker save mockpix:latest-amd64 | gzip > mockpix-amd64.tar.gz

# 上传到服务器
scp mockpix-amd64.tar.gz root@server-ip:/tmp/
```

**优点**：
- ✅ 可以在本地控制构建过程
- ✅ 可以预先测试

**缺点**：
- ⚠️ 需要配置 buildx
- ⚠️ 构建时间更长
- ⚠️ 需要上传大文件

### 方案 3：使用 Docker Hub

通过 Docker Hub 自动处理多架构：

```bash
# 构建并推送多架构镜像
docker buildx build --platform linux/amd64,linux/arm64 \
  -f docker/1panel/Dockerfile.1panel \
  -t yourusername/mockpix:latest \
  --push .

# 在服务器上拉取
docker pull yourusername/mockpix:latest
```

## 🔍 诊断命令

### 检查服务器架构
```bash
# 查看系统架构
uname -m
# x86_64 = AMD64
# aarch64 = ARM64

# 查看 Docker 架构
docker version --format '{{.Server.Arch}}'
```

### 检查镜像架构
```bash
# 查看镜像详细信息
docker inspect mockpix:latest | grep Architecture

# 查看镜像平台
docker image inspect mockpix:latest --format '{{.Architecture}}'
```

## 🚨 常见错误及解决

### 错误 1：exec format error
**原因**：架构不匹配
**解决**：在服务器上重新构建

### 错误 2：no matching manifest
**原因**：镜像不支持当前架构
**解决**：使用 buildx 构建多架构镜像

### 错误 3：构建失败
**原因**：网络问题或依赖下载失败
**解决**：
1. 配置 Docker 镜像加速器
2. 使用国内镜像源（Dockerfile 中已配置）
3. 重试构建

## 📝 最佳实践

1. **开发阶段**：
   - 使用 Docker Compose 本地开发
   - 不用关心架构问题

2. **部署阶段**：
   - 优先在服务器上构建
   - 或使用 CI/CD 自动构建多架构镜像

3. **生产环境**：
   - 使用固定版本标签
   - 记录镜像架构信息
   - 保持开发和生产架构一致

## 🔗 相关文档

- [Docker Buildx 文档](https://docs.docker.com/buildx/working-with-buildx/)
- [多架构镜像最佳实践](https://docs.docker.com/build/building/multi-platform/)
- [1Panel 部署指南](./快速部署指南.md)

---

*更新时间：2025-08*  
*适用版本：Docker 20.10+*