# =============================================================================
# 1Panel 专用 Dockerfile - Next.js 生产环境优化版（国内镜像加速）
# =============================================================================
# 说明：
# - 基于 Next.js standalone 模式构建
# - 针对 1Panel 环境优化
# - 最小化镜像体积
# - 支持多架构（amd64/arm64）
# - 使用阿里云镜像加速
# =============================================================================

# -----------------------------------------------------------------------------
# 阶段 1: 依赖安装层
# -----------------------------------------------------------------------------
# 只安装生产环境必需的依赖，减小最终镜像体积
# 使用 Docker Hub 官方镜像
FROM node:20-alpine AS deps

# 配置 npm 和 pnpm 使用淘宝镜像
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@9 && \
    pnpm config set registry https://registry.npmmirror.com

WORKDIR /app

# 复制包管理文件
# 利用 Docker 缓存机制，只有这些文件变化时才重新安装依赖
COPY package.json pnpm-lock.yaml ./

# 安装生产依赖
# --frozen-lockfile: 确保使用 lock 文件中的确切版本
# --prod: 只安装 dependencies，不安装 devDependencies
RUN pnpm install --frozen-lockfile --prod

# -----------------------------------------------------------------------------
# 阶段 2: 构建层
# -----------------------------------------------------------------------------
# 编译 TypeScript 和构建 Next.js 应用
FROM node:20-alpine AS builder

# 配置 npm 和 pnpm 使用淘宝镜像
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@9 && \
    pnpm config set registry https://registry.npmmirror.com

WORKDIR /app

# 复制包管理文件并安装所有依赖（包括开发依赖）
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 设置构建时环境变量
# 这些变量会被内联到构建产物中
ENV NEXT_TELEMETRY_DISABLED=1 \
    NODE_ENV=production

# 构建 Next.js 应用
# 使用 standalone 模式，生成独立的生产构建
RUN pnpm build

# -----------------------------------------------------------------------------
# 阶段 3: 生产运行层
# -----------------------------------------------------------------------------
# 最终的轻量级生产镜像
FROM node:20-alpine AS runner

# 配置 apk 使用阿里云镜像
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache dumb-init

WORKDIR /app

# 设置生产环境变量
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    # 1Panel 特定配置
    PORT=3000 \
    HOSTNAME="0.0.0.0"

# 创建非 root 用户
# 提高安全性，1Panel 推荐使用非 root 用户运行容器
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制 standalone 构建产物
# standalone 模式会生成一个包含所有依赖的独立目录
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# 复制 public 目录（如果存在）
# standalone 模式不包含 public 目录，需要手动复制
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# 切换到非 root 用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
# 1Panel 会使用这个健康检查来监控容器状态
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000', (res) => { \
        process.exit(res.statusCode === 200 ? 0 : 1); \
    })" || exit 1

# 使用 dumb-init 作为入口点，确保信号正确传递
ENTRYPOINT ["dumb-init", "--"]

# 启动 Next.js 服务器
# standalone 模式下使用 server.js
CMD ["node", "server.js"]

# =============================================================================
# 构建命令示例：
# =============================================================================
# 构建镜像: docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
# 多架构构建: docker buildx build --platform linux/amd64,linux/arm64 -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
# =============================================================================