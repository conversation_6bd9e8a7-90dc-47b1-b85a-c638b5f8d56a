#!/bin/bash

# =============================================================================
# 1Panel 应用卸载脚本
# =============================================================================
# 说明：
# - 此脚本会被 1Panel 在卸载应用时自动执行
# - 执行环境：1Panel 服务器
# - 工作目录：应用安装目录
# =============================================================================

set -e

# -----------------------------------------------------------------------------
# 颜色输出定义
# -----------------------------------------------------------------------------
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# -----------------------------------------------------------------------------
# 日志函数
# -----------------------------------------------------------------------------
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# -----------------------------------------------------------------------------
# 加载配置
# -----------------------------------------------------------------------------
load_config() {
    log_info "加载配置..."
    
    # 加载环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # 设置默认值
    export CONTAINER_NAME="${CONTAINER_NAME:-next-wallpaper-1panel}"
    export IMAGE_NAME="${IMAGE_NAME:-next-wallpaper:1panel}"
    export DATA_PATH="${DATA_PATH:-./data}"
    export REMOVE_DATA="${REMOVE_DATA:-false}"
    export REMOVE_IMAGE="${REMOVE_IMAGE:-false}"
}

# -----------------------------------------------------------------------------
# 停止并删除容器
# -----------------------------------------------------------------------------
stop_and_remove_container() {
    log_info "停止并删除容器..."
    
    # 使用 docker-compose 停止
    if [ -f "docker-compose.yml" ]; then
        if command -v docker-compose &> /dev/null; then
            docker-compose down --remove-orphans || true
        else
            docker compose down --remove-orphans || true
        fi
        log_success "容器已通过 docker-compose 停止并删除"
    else
        # 直接使用 docker 命令
        if docker ps -a | grep -q "$CONTAINER_NAME"; then
            docker stop "$CONTAINER_NAME" 2>/dev/null || true
            docker rm "$CONTAINER_NAME" 2>/dev/null || true
            log_success "容器已停止并删除: $CONTAINER_NAME"
        else
            log_warning "容器不存在或已删除: $CONTAINER_NAME"
        fi
    fi
}

# -----------------------------------------------------------------------------
# 清理网络
# -----------------------------------------------------------------------------
clean_network() {
    log_info "清理 Docker 网络..."
    
    local network_name="next-wallpaper-1panel-network"
    
    if docker network ls | grep -q "$network_name"; then
        docker network rm "$network_name" 2>/dev/null || true
        log_success "网络已删除: $network_name"
    fi
}

# -----------------------------------------------------------------------------
# 清理镜像（可选）
# -----------------------------------------------------------------------------
clean_image() {
    if [ "$REMOVE_IMAGE" = "true" ]; then
        log_info "删除 Docker 镜像..."
        
        if docker images | grep -q "${IMAGE_NAME%:*}"; then
            docker rmi "$IMAGE_NAME" 2>/dev/null || true
            log_success "镜像已删除: $IMAGE_NAME"
        else
            log_warning "镜像不存在: $IMAGE_NAME"
        fi
    else
        log_info "保留 Docker 镜像: $IMAGE_NAME"
    fi
}

# -----------------------------------------------------------------------------
# 备份数据
# -----------------------------------------------------------------------------
backup_data() {
    if [ -d "$DATA_PATH" ] && [ "$(ls -A $DATA_PATH)" ]; then
        log_info "备份应用数据..."
        
        local backup_dir="./backup"
        local backup_file="${backup_dir}/next-wallpaper-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
        
        mkdir -p "$backup_dir"
        tar -czf "$backup_file" "$DATA_PATH" 2>/dev/null || {
            log_warning "数据备份失败"
            return
        }
        
        log_success "数据已备份到: $backup_file"
    fi
}

# -----------------------------------------------------------------------------
# 清理数据（可选）
# -----------------------------------------------------------------------------
clean_data() {
    if [ "$REMOVE_DATA" = "true" ]; then
        log_warning "即将删除所有应用数据..."
        
        # 给用户 5 秒时间取消
        echo "按 Ctrl+C 取消删除..."
        sleep 5
        
        if [ -d "$DATA_PATH" ]; then
            rm -rf "$DATA_PATH"
            log_success "应用数据已删除"
        fi
    else
        log_info "保留应用数据: $DATA_PATH"
        log_info "如需删除数据，请手动删除或设置 REMOVE_DATA=true"
    fi
}

# -----------------------------------------------------------------------------
# 清理其他资源
# -----------------------------------------------------------------------------
clean_other_resources() {
    log_info "清理其他资源..."
    
    # 清理未使用的卷
    local volumes=$(docker volume ls -q | grep "next-wallpaper" || true)
    if [ -n "$volumes" ]; then
        echo "$volumes" | xargs docker volume rm 2>/dev/null || true
        log_success "相关卷已清理"
    fi
    
    # 清理悬空镜像
    docker image prune -f 2>/dev/null || true
}

# -----------------------------------------------------------------------------
# 显示卸载信息
# -----------------------------------------------------------------------------
show_uninstall_info() {
    echo
    echo "========================================"
    echo "     Next Wallpaper 卸载完成"
    echo "========================================"
    echo
    echo "已执行操作:"
    echo "  ✓ 停止并删除容器"
    echo "  ✓ 清理 Docker 网络"
    
    if [ "$REMOVE_IMAGE" = "true" ]; then
        echo "  ✓ 删除 Docker 镜像"
    else
        echo "  - 保留 Docker 镜像"
    fi
    
    if [ "$REMOVE_DATA" = "true" ]; then
        echo "  ✓ 删除应用数据"
    else
        echo "  - 保留应用数据"
    fi
    
    echo
    echo "注意事项:"
    
    if [ "$REMOVE_DATA" != "true" ] && [ -d "$DATA_PATH" ]; then
        echo "• 应用数据保留在: $DATA_PATH"
        echo "• 如需彻底删除，请手动删除该目录"
    fi
    
    if [ -d "./backup" ]; then
        echo "• 数据备份保存在: ./backup"
    fi
    
    echo
    echo "========================================"
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    log_info "开始卸载 Next Wallpaper..."
    
    # 加载配置
    load_config
    
    # 询问是否删除数据（如果是交互式环境）
    if [ -t 0 ] && [ "$REMOVE_DATA" != "true" ]; then
        read -p "是否删除应用数据？(y/N) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            REMOVE_DATA="true"
        fi
    fi
    
    # 询问是否删除镜像
    if [ -t 0 ] && [ "$REMOVE_IMAGE" != "true" ]; then
        read -p "是否删除 Docker 镜像？(y/N) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            REMOVE_IMAGE="true"
        fi
    fi
    
    # 执行卸载步骤
    stop_and_remove_container
    clean_network
    
    # 备份数据（如果要删除数据的话）
    if [ "$REMOVE_DATA" = "true" ]; then
        backup_data
    fi
    
    clean_data
    clean_image
    clean_other_resources
    
    # 显示完成信息
    show_uninstall_info
    
    log_success "卸载完成！"
}

# -----------------------------------------------------------------------------
# 执行卸载
# -----------------------------------------------------------------------------
main "$@"