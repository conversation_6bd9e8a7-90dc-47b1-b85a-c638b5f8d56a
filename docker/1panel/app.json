{"name": "next-wallpaper", "title": "Next Wallpaper", "version": "1.0.0", "description": "基于 Next.js 的现代化壁纸管理应用，支持多设备预览和导出", "author": "MockPix Team", "website": "https://github.com/gcordon/next_wallpaper", "category": "Web应用", "tags": ["Next.js", "React", "壁纸", "图片管理", "前端应用"], "icon": "logo.png", "requirements": {"docker": ">=20.10.0", "docker-compose": ">=1.29.0", "cpu": "1核", "memory": "512MB", "disk": "1GB", "description": "建议配置：2核CPU、1GB内存、2GB磁盘空间"}, "ports": [{"port": 3000, "protocol": "http", "description": "Web 访问端口", "required": true, "configurable": true}], "volumes": [{"path": "/app/logs", "description": "日志目录", "required": false, "mode": "rw"}, {"path": "/app/uploads", "description": "上传文件存储", "required": false, "mode": "rw"}], "environments": [{"key": "NODE_ENV", "value": "production", "description": "运行环境", "required": true, "type": "select", "options": ["production", "development"], "configurable": true}, {"key": "APP_PORT", "value": "3000", "description": "应用端口", "required": true, "type": "number", "min": 1024, "max": 65535, "configurable": true}, {"key": "APP_URL", "value": "http://localhost:3000", "description": "应用访问地址", "required": false, "type": "string", "placeholder": "http://your-domain.com", "configurable": true}, {"key": "TZ", "value": "Asia/Shanghai", "description": "时区设置", "required": false, "type": "select", "options": ["Asia/Shanghai", "Asia/Tokyo", "America/New_York", "Europe/London", "UTC"], "configurable": true}, {"key": "CPU_LIMIT", "value": "1", "description": "CPU 限制（核数）", "required": false, "type": "string", "configurable": true}, {"key": "MEMORY_LIMIT", "value": "512M", "description": "内存限制", "required": false, "type": "string", "pattern": "^[0-9]+[MG]$", "configurable": true}, {"key": "LOG_MAX_SIZE", "value": "10m", "description": "单个日志文件最大大小", "required": false, "type": "string", "pattern": "^[0-9]+[mk]$", "configurable": true}, {"key": "LOG_MAX_FILES", "value": "3", "description": "保留的日志文件数量", "required": false, "type": "number", "min": 1, "max": 10, "configurable": true}], "install": {"script": "install.sh", "timeout": 300, "description": "安装过程包括：拉取镜像、创建容器、启动服务"}, "uninstall": {"script": "uninstall.sh", "description": "卸载时会：停止容器、删除容器、清理数据（可选）"}, "operations": {"start": {"command": "docker-compose up -d", "description": "启动应用"}, "stop": {"command": "docker-compose down", "description": "停止应用"}, "restart": {"command": "docker-compose restart", "description": "重启应用"}, "logs": {"command": "docker-compose logs -f --tail=100", "description": "查看日志"}, "update": {"command": "docker-compose pull && docker-compose up -d", "description": "更新应用"}, "backup": {"command": "tar -czf backup.tar.gz data/", "description": "备份数据"}}, "healthcheck": {"enabled": true, "url": "http://localhost:3000", "interval": 30, "timeout": 10, "retries": 3}, "dependencies": [], "notes": ["首次启动可能需要几分钟时间来初始化", "建议使用反向代理（如 Nginx）配置 HTTPS", "数据文件存储在 ./data 目录下", "更多配置选项请参考项目文档"], "changelog": {"1.0.0": ["初始版本发布", "支持多设备预览", "支持图片导出", "支持 Docker 部署"]}, "support": {"documentation": "https://github.com/gcordon/next_wallpaper/wiki", "issues": "https://github.com/gcordon/next_wallpaper/issues", "forum": "https://github.com/gcordon/next_wallpaper/discussions"}}