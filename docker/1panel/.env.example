# =============================================================================
# 1Panel 环境变量配置模板
# =============================================================================
# 说明：
# 1. 复制此文件为 .env 并修改配置
# 2. 这些变量会被 docker-compose.yml 使用
# 3. 在 1Panel 界面中也可以直接修改这些配置
# =============================================================================

# -----------------------------------------------------------------------------
# 基础配置
# -----------------------------------------------------------------------------
# 容器名称（在 1Panel 中显示的名称）
CONTAINER_NAME=next-wallpaper-1panel

# 镜像名称和标签
IMAGE_NAME=next-wallpaper:1panel

# 应用版本
APP_VERSION=1.0.0

# -----------------------------------------------------------------------------
# 网络配置
# -----------------------------------------------------------------------------
# 应用访问端口（默认 3000）
# 如果端口冲突，可以修改为其他端口如 3001, 8080 等
APP_PORT=3000

# 应用访问 URL
# 用于生成绝对路径，如果有域名请填写实际域名
APP_URL=http://localhost:3000

# 域名配置（如果使用反向代理）
# DOMAIN=wallpaper.example.com

# -----------------------------------------------------------------------------
# 运行环境
# -----------------------------------------------------------------------------
# Node.js 运行环境（production/development）
NODE_ENV=production

# 时区设置
TZ=Asia/Shanghai

# -----------------------------------------------------------------------------
# 资源限制
# -----------------------------------------------------------------------------
# CPU 限制（核数，如 0.5, 1, 2）
CPU_LIMIT=1

# 内存限制（如 256M, 512M, 1G）
MEMORY_LIMIT=512M

# CPU 预留（最小保证）
CPU_RESERVE=0.25

# 内存预留（最小保证）
MEMORY_RESERVE=256M

# -----------------------------------------------------------------------------
# 存储配置
# -----------------------------------------------------------------------------
# 数据存储路径（相对路径或绝对路径）
DATA_PATH=./data

# 配置文件路径
CONFIG_PATH=./config

# 上传文件存储路径
UPLOAD_PATH=./data/uploads

# -----------------------------------------------------------------------------
# 日志配置
# -----------------------------------------------------------------------------
# 单个日志文件最大大小（如 10m, 50m, 100m）
LOG_MAX_SIZE=10m

# 保留的日志文件数量
LOG_MAX_FILES=3

# 日志级别（error, warn, info, debug）
LOG_LEVEL=info

# -----------------------------------------------------------------------------
# 重启策略
# -----------------------------------------------------------------------------
# 重启策略（no, always, on-failure, unless-stopped）
RESTART_POLICY=unless-stopped

# -----------------------------------------------------------------------------
# 应用特定配置
# -----------------------------------------------------------------------------
# Next.js 配置
NEXT_TELEMETRY_DISABLED=1

# 自定义配置（根据应用需求添加）
# CUSTOM_CONFIG_1=value1
# CUSTOM_CONFIG_2=value2

# -----------------------------------------------------------------------------
# 数据库配置（如果需要）
# -----------------------------------------------------------------------------
# DATABASE_URL=
# DATABASE_HOST=
# DATABASE_PORT=
# DATABASE_NAME=
# DATABASE_USER=
# DATABASE_PASSWORD=

# -----------------------------------------------------------------------------
# 缓存配置（如果需要）
# -----------------------------------------------------------------------------
# REDIS_URL=
# REDIS_HOST=
# REDIS_PORT=
# REDIS_PASSWORD=

# -----------------------------------------------------------------------------
# 监控和告警（如果需要）
# -----------------------------------------------------------------------------
# ENABLE_MONITORING=false
# METRICS_PORT=9090
# ALERT_EMAIL=

# -----------------------------------------------------------------------------
# 备份配置
# -----------------------------------------------------------------------------
# 自动备份（true/false）
AUTO_BACKUP=false

# 备份保留天数
BACKUP_RETENTION_DAYS=7

# 备份路径
BACKUP_PATH=./backups

# =============================================================================
# 注意事项：
# =============================================================================
# 1. 生产环境请务必修改默认密码和密钥
# 2. 敏感信息不要提交到版本控制系统
# 3. 定期备份重要数据
# 4. 根据实际负载调整资源限制
# =============================================================================