# Docker 构建故障排除指南

## 🔧 常见问题及解决方案

### 1. Docker Hub 网络连接超时

**错误信息：**
```
ERROR: failed to solve: DeadlineExceeded: failed to fetch anonymous token: 
Get "https://auth.docker.io/token...": dial tcp ***************:443: i/o timeout
```

**解决方案：**

#### 方案 A：配置 Docker 镜像加速器
```bash
# 运行配置脚本
./scripts/setup-docker-mirror.sh

# 重启 Docker Desktop
# macOS: 点击 Docker 图标 → Restart
# Linux: sudo systemctl restart docker

# 重新构建
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

#### 方案 B：使用 1Panel Dockerfile（已内置国内镜像）
```bash
# 1Panel Dockerfile 已配置国内镜像加速
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

#### 方案 C：手动配置 Docker daemon
编辑 `~/.docker/daemon.json`：
```json
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerpull.com",
    "https://docker.1panel.live",
    "https://hub.rat.dev"
  ]
}
```

---

### 2. ESLint 警告导致构建失败

**错误信息：**
```
info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
ELIFECYCLE  Command failed with exit code 1.
```

**解决方案：**

在 `next.config.ts` 中添加配置：
```typescript
const nextConfig = {
  output: 'standalone',
  eslint: {
    ignoreDuringBuilds: true  // 忽略 ESLint 警告
  }
}
```

---

### 3. TypeScript 类型错误

**错误信息：**
```
Type error: Page "app/get_color/page.tsx" has an invalid "default" export:
  Type "GetColorProps" is not valid.
```

**解决方案：**

#### 方案 A：修复类型错误
Next.js 15 页面组件不应接收 props：
```typescript
// ❌ 错误
const Page: React.FC<PageProps> = ({ prop }) => { ... }

// ✅ 正确
const Page: React.FC = () => { ... }
```

#### 方案 B：忽略构建时类型错误
在 `next.config.ts` 中添加：
```typescript
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true  // 忽略类型错误
  }
}
```

---

### 4. Next.js 配置错误

**错误信息：**
```
⚠ Invalid next.config.ts options detected: 
⚠     Unrecognized key(s) in object: 'telemetry'
```

**解决方案：**

删除无效的配置项：
```typescript
// ❌ 错误（telemetry 应该在环境变量中设置）
const nextConfig = {
  telemetry: { disabled: true }
}

// ✅ 正确（通过环境变量设置）
// 在 Dockerfile 或 .env 中设置
NEXT_TELEMETRY_DISABLED=1
```

---

### 5. pnpm 安装依赖失败

**错误信息：**
```
 WARN  GET https://registry.npmjs.org/... error (ERR_SOCKET_TIMEOUT)
```

**解决方案：**

在 Dockerfile 中配置 npm/pnpm 镜像：
```dockerfile
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@9 && \
    pnpm config set registry https://registry.npmmirror.com
```

---

### 6. Alpine Linux apk 安装失败

**错误信息：**
```
fetch https://dl-cdn.alpinelinux.org/alpine/... temporary error (try again later)
```

**解决方案：**

配置阿里云 Alpine 镜像：
```dockerfile
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache dumb-init
```

---

### 7. 构建缓存问题

**症状：**
- 代码已更新但构建结果没有变化
- 依赖没有正确安装

**解决方案：**

```bash
# 清理 Docker 缓存并重新构建
docker build --no-cache -f docker/1panel/Dockerfile.1panel -t mockpix:latest .

# 或清理所有 Docker 缓存
docker system prune -a
```

---

### 8. 容器运行时错误

**错误信息：**
```
exec /usr/local/bin/docker-entrypoint.sh: exec format error
```

**解决方案：**

检查架构兼容性：
```bash
# 检查系统架构
uname -m

# 为特定架构构建
docker buildx build --platform linux/amd64 -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

---

### 9. 端口占用

**错误信息：**
```
bind: address already in use
```

**解决方案：**

```bash
# 查看端口占用
lsof -i :3000

# 停止占用端口的进程
kill -9 <PID>

# 或使用不同端口
docker run -p 3001:3000 mockpix:latest
```

---

### 10. 内存不足

**错误信息：**
```
JavaScript heap out of memory
```

**解决方案：**

在 Dockerfile 中增加 Node.js 内存限制：
```dockerfile
ENV NODE_OPTIONS="--max-old-space-size=2048"
```

或在 docker-compose.yml 中设置资源限制：
```yaml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 2G
```

---

## 🚀 快速诊断脚本

创建 `scripts/diagnose.sh`：
```bash
#!/bin/bash

echo "🔍 Docker 诊断开始..."

# 检查 Docker 版本
echo "Docker 版本："
docker --version

# 检查 Docker 服务状态
echo -e "\nDocker 服务状态："
docker info | grep -E "Server Version|Storage Driver|Logging Driver"

# 检查网络连接
echo -e "\n测试 Docker Hub 连接："
curl -I https://hub.docker.com --connect-timeout 5 2>/dev/null | head -n 1

# 检查镜像加速配置
echo -e "\nDocker 镜像加速配置："
cat ~/.docker/daemon.json 2>/dev/null || echo "未配置镜像加速"

# 检查磁盘空间
echo -e "\n磁盘空间："
df -h | grep -E "/$|/var/lib/docker"

# 检查 Docker 镜像
echo -e "\n已有镜像："
docker images | grep -E "mockpix|node"

echo -e "\n✅ 诊断完成"
```

---

## 📞 获取帮助

如果以上方案都无法解决问题：

1. **查看详细错误日志**
   ```bash
   docker build --progress=plain -f docker/1panel/Dockerfile.1panel -t mockpix:latest . 2>&1 | tee build.log
   ```

2. **提交 Issue**
   - GitHub: https://github.com/gcordon/next_wallpaper/issues
   - 附上 `build.log` 文件内容

3. **环境信息收集**
   ```bash
   echo "系统信息：$(uname -a)"
   echo "Docker 版本：$(docker --version)"
   echo "Docker Compose 版本：$(docker-compose --version)"
   echo "Node 版本：$(node --version)"
   ```

---

*最后更新：2025-08*  
*版本：1.0*