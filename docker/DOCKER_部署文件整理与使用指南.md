# 🐳 Docker 部署文件整理与使用指南

> 本文档对项目中所有Docker和部署相关文件进行了系统整理，明确每个文件的用途、使用场景和保留建议。

## 📊 文件分类与用途

### 一、核心部署文件（必须保留）

#### 1. 根目录核心文件
| 文件 | 用途 | 使用场景 | 依赖关系 |
|-----|------|---------|---------|
| `Dockerfile` | 通用多阶段构建文件 | 本地开发/生产部署 | 被所有docker-compose文件引用 |
| `docker-compose.yml` | 基础配置文件 | 定义共享服务配置 | 可被其他环境继承 |
| `docker-compose.local.yml` | 本地开发配置 | 本地快速开发，支持热重载 | 依赖Dockerfile的development阶段 |

#### 2. 1Panel专用文件
| 文件路径 | 用途 | 使用场景 |
|---------|------|---------|
| `docker/1panel/Dockerfile.1panel` | 1Panel优化版Dockerfile | 1Panel部署，国内镜像加速，standalone模式 |
| `docker/1panel/docker-compose.yml` | 1Panel编排配置 | 1Panel应用商店部署 |
| `docker/1panel/app.json` | 1Panel应用元数据 | 定义应用在1Panel中的配置项 |
| `docker/1panel/README.md` | 1Panel部署指南 | 详细的1Panel部署步骤 |

### 二、部署脚本（建议保留）

| 脚本路径 | 用途 | 使用场景 | 重要程度 |
|---------|------|---------|---------|
| `scripts/local-start.sh` | 本地开发快速启动 | 一键启动本地开发环境 | ⭐⭐⭐ |
| `scripts/1panel-deploy.sh` | 1Panel部署脚本 | 构建1Panel应用包 | ⭐⭐⭐ |
| `scripts/quick-deploy.sh` | 快速部署到服务器 | MockPix项目专用部署 | ⭐⭐ |
| `scripts/setup-docker-mirror.sh` | Docker镜像加速配置 | 国内环境优化 | ⭐⭐ |

### 三、文档文件（建议保留）

| 文档路径 | 内容 | 目标用户 |
|---------|------|---------|
| `docker/docs/DOCKER_DEPLOYMENT.md` | 完整部署指南 | 所有用户 |
| `docker/docs/DOCKER_README.md` | Docker基础使用指南 | Docker新手 |
| `docker/1panel/*.md` | 1Panel相关文档 | 1Panel用户 |
| `docker/docs/Docker基础概念.md` | Docker学习资料 | 初学者 |

### 四、可以清理的文件

| 文件路径 | 原因 | 建议操作 |
|---------|------|---------|
| `docker/docs/archive/docker-compose.dev.yml` | 旧版开发配置 | 已被docker-compose.local.yml替代，可删除 |
| `docker/docs/archive/docker-compose.prod.yml` | 旧版生产配置 | 已被整合到其他文件，可删除 |
| `docker/1panel/docker/1panel/*` | 重复嵌套目录 | 路径混乱，建议整理到docker/1panel/ |

## 🎯 使用场景指南

### 场景一：本地开发（最常用）

**适用人群**：开发者日常开发调试

**使用文件**：
- `docker-compose.local.yml` - 主配置文件
- `Dockerfile` (development阶段) - 构建开发镜像
- `scripts/local-start.sh` - 快速启动脚本

**启动命令**：
```bash
# 方式1：使用脚本（推荐）
./scripts/local-start.sh

# 方式2：直接使用docker-compose
docker-compose -f docker-compose.local.yml up

# 国内用户加速
./scripts/local-start.sh --china-mirror
```

**特点**：
- ✅ 支持热重载
- ✅ 源码挂载
- ✅ 包含调试工具
- ✅ 资源占用适中

### 场景二：1Panel部署（服务器部署）

**适用人群**：使用1Panel管理服务器的用户

**使用文件**：
- `docker/1panel/Dockerfile.1panel` - 优化版镜像
- `docker/1panel/docker-compose.yml` - 编排配置
- `docker/1panel/app.json` - 应用配置
- `scripts/1panel-deploy.sh` - 部署脚本

**部署流程**：
```bash
# 1. 本地构建
./scripts/1panel-deploy.sh --export

# 2. 上传到服务器
scp -r dist/1panel/* root@server:/opt/1panel/apps/

# 3. 在1Panel界面安装配置
```

**特点**：
- ✅ 国内镜像加速
- ✅ standalone模式，体积小
- ✅ 图形化管理界面
- ✅ 自动健康检查

### 场景三：独立Docker部署

**适用人群**：熟悉Docker的运维人员

**使用文件**：
- `Dockerfile` (runner阶段) - 生产镜像
- `docker-compose.yml` - 基础配置
- 自定义docker-compose.prod.yml（需创建）

**部署命令**：
```bash
# 构建生产镜像
docker build --target runner -t app:prod .

# 运行容器
docker run -d -p 3000:3000 app:prod
```

**特点**：
- ✅ 最小镜像体积（~150MB）
- ✅ 安全性高（非root用户）
- ✅ 资源占用低
- ✅ 适合集群部署

### 场景四：快速部署到MockPix

**适用人群**：MockPix项目特定部署

**使用文件**：
- `scripts/quick-deploy.sh` - 自动化部署脚本
- `docker/1panel/Dockerfile.1panel` - 构建镜像

**使用方法**：
```bash
./scripts/quick-deploy.sh
```

**特点**：
- ✅ 一键部署
- ✅ 自动构建和上传
- ✅ 包含服务器配置

## 🔧 优化建议

### 1. 文件结构优化
```
建议的目录结构：
docker/
├── Dockerfile              # 通用Dockerfile
├── docker-compose.yml      # 基础配置
├── docker-compose.local.yml # 本地开发
├── 1panel/                 # 1Panel相关
│   ├── Dockerfile         # 1Panel优化版
│   ├── docker-compose.yml
│   ├── app.json
│   └── README.md
├── scripts/               # 部署脚本
│   ├── local-start.sh
│   ├── 1panel-deploy.sh
│   └── setup-mirror.sh
└── docs/                  # 文档
    ├── README.md
    └── troubleshooting.md
```

### 2. 清理冗余文件
```bash
# 删除archive目录（已过时）
rm -rf docker/docs/archive/

# 清理重复嵌套目录
rm -rf docker/1panel/docker/
```

### 3. 统一配置管理
建议创建`.env.example`文件，统一管理环境变量：
```env
# 通用配置
NODE_ENV=production
PORT=3000
TZ=Asia/Shanghai

# 1Panel配置
APP_URL=https://your-domain.com
CPU_LIMIT=1
MEMORY_LIMIT=512M

# 开发配置
WATCHPACK_POLLING=true
```

## 📋 维护检查清单

### 定期检查项
- [ ] Dockerfile中的基础镜像版本是否需要更新
- [ ] docker-compose.yml中的配置是否与应用匹配
- [ ] 脚本文件是否有执行权限
- [ ] 文档是否与实际配置同步
- [ ] 是否有未使用的Docker镜像需要清理

### 部署前检查
- [ ] next.config.ts中output是否设置为'standalone'
- [ ] 是否配置了ESLint和TypeScript忽略选项
- [ ] 端口3000是否可用
- [ ] Docker和Docker Compose版本是否满足要求

## 🎯 推荐使用方案

### 对于不同用户群体的推荐：

1. **开发者日常开发**
   - 主用：`docker-compose.local.yml` + `scripts/local-start.sh`
   - 优势：快速启动，支持热重载

2. **生产环境部署**
   - 有1Panel：使用`docker/1panel/`目录下的文件
   - 无1Panel：使用`Dockerfile`的runner阶段 + 自定义配置

3. **CI/CD集成**
   - 使用：`Dockerfile` + 环境变量配置
   - 配合：GitHub Actions或其他CI工具

4. **测试环境**
   - 复用：`docker-compose.local.yml`
   - 调整：资源限制和环境变量

## 📊 资源占用对比

| 部署方式 | 镜像大小 | 内存占用 | CPU占用 | 启动时间 |
|---------|---------|---------|---------|---------|
| 本地开发 | ~500MB | 1-2GB | 1-2核 | 30-40秒 |
| 生产部署 | ~150MB | 256-512MB | 0.5-1核 | 10-15秒 |
| 1Panel部署 | ~311MB | 512MB-1GB | 1核 | 15-20秒 |

## 📄 环境变量文件说明

项目中包含多个环境变量文件，用于不同场景：

| 文件 | 用途 | 使用场景 |
|------|------|---------|
| `.env.example` | 环境变量模板 | 所有环境的参考模板 |
| `.env.local` | 本地开发环境变量 | docker-compose.local.yml 使用 |
| `.env.production` | 生产环境变量 | docker-compose.prod.yml 使用 |
| `.env.docker` | Docker通用环境变量 | Docker环境共享配置 |
| `docker/1panel/.env.example` | 1Panel环境变量模板 | 1Panel部署参考 |
| `docker/1panel/.env.production` | 1Panel生产配置 | 1Panel实际部署使用 |

### 配置示例
```bash
# 复制模板创建实际配置
cp .env.example .env.local     # 本地开发
cp .env.example .env.production # 生产环境
```

## ⚠️ 修复记录

### 已修复的问题
✅ **恢复了误删文件**：
- `docker/1panel/Dockerfile.1panel` - 1Panel专用Dockerfile
- `docker/1panel/docker-compose.simple.yml` - 简化版生产配置

✅ **修复了脚本引用**：
- `scripts/dev.sh` - 更新为使用 docker-compose.local.yml
- `scripts/prod.sh` - 现在引用新创建的 docker-compose.prod.yml

✅ **创建了缺失配置**：
- `docker-compose.prod.yml` - 新的生产环境配置文件
- `.env.production` - 生产环境变量文件（基于 .env.example 创建）

### 恢复备份的方法
如果需要恢复备份的文件：
```bash
# 查看备份内容
ls -la docker-backup-*/

# 恢复特定文件
cp -r docker-backup-*/archive/* docker/docs/archive/

# 恢复所有文件
cp -r docker-backup-*/* docker/
```

### 推荐的最终结构
```
项目根目录/
├── Dockerfile                    # 通用多阶段构建
├── docker-compose.yml            # 基础配置
├── docker-compose.local.yml     # 本地开发（主要使用）
├── .dockerignore                 # Docker忽略文件
├── docker/
│   ├── 1panel/                  # 1Panel部署文件
│   │   ├── Dockerfile.1panel    
│   │   ├── docker-compose.yml
│   │   ├── app.json
│   │   └── *.md                 # 相关文档
│   ├── docs/                    # Docker文档
│   └── DOCKER_部署文件整理与使用指南.md
└── scripts/
    ├── local-start.sh           # 本地开发启动（推荐）
    ├── 1panel-deploy.sh         # 1Panel部署
    ├── quick-deploy.sh          # 快速部署
    └── setup-docker-mirror.sh  # 镜像加速配置
```

## 🔗 相关链接

- [Docker官方文档](https://docs.docker.com/)
- [1Panel官方文档](https://1panel.cn/docs/)
- [Next.js部署指南](https://nextjs.org/docs/deployment)
- [项目GitHub](https://github.com/gcordon/next_wallpaper)

---

*最后更新：2025-08-18*
*维护者：Next Wallpaper团队*