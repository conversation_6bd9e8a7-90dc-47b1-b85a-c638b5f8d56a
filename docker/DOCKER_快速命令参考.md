# 🚀 Docker 快速命令参考

> 通过 `package.json` 中的 npm/pnpm scripts 快速使用 Docker

## 📋 可用命令

### 本地开发环境

```bash
# 启动本地开发环境（推荐）
pnpm run docker:dev
# 或
npm run docker:dev

# 强制重新构建镜像
pnpm run docker:dev:rebuild

# 使用国内镜像加速（适合网络较慢时）
pnpm run docker:dev:china
```

### 生产环境

```bash
# 启动生产环境
pnpm run docker:prod

# 1Panel 部署准备
pnpm run docker:1panel
```

### 管理命令

```bash
# 停止开发环境容器
pnpm run docker:stop

# 查看容器日志（实时）
pnpm run docker:logs

# 清理 Docker 资源（谨慎使用）
pnpm run docker:clean
```

## 🔄 常用工作流程

### 日常开发
```bash
# 1. 启动开发环境
pnpm run docker:dev

# 2. 查看日志（新终端窗口）
pnpm run docker:logs

# 3. 开发完成后停止
pnpm run docker:stop
```

### 首次使用或更新依赖
```bash
# 重新构建镜像
pnpm run docker:dev:rebuild
```

### 国内用户优化
```bash
# 使用镜像加速
pnpm run docker:dev:china
```

## 📊 命令对比

| npm/pnpm 命令 | 等效的直接命令 | 说明 |
|---------------|----------------|------|
| `pnpm run docker:dev` | `./scripts/local-start.sh` | 本地开发 |
| `pnpm run docker:dev:rebuild` | `./scripts/local-start.sh --rebuild` | 重新构建 |
| `pnpm run docker:prod` | `./scripts/prod.sh` | 生产环境 |
| `pnpm run docker:stop` | `docker-compose -f docker-compose.local.yml down` | 停止容器 |
| `pnpm run docker:logs` | `docker-compose -f docker-compose.local.yml logs -f` | 查看日志 |

## 💡 使用技巧

### 1. 后台运行 + 日志监控
```bash
# 终端 1：启动开发环境
pnpm run docker:dev

# 终端 2：监控日志
pnpm run docker:logs
```

### 2. 快速重启
```bash
# 停止 + 启动
pnpm run docker:stop && pnpm run docker:dev
```

### 3. 清理后重新开始
```bash
# 停止容器
pnpm run docker:stop

# 清理资源（可选）
pnpm run docker:clean

# 重新构建启动
pnpm run docker:dev:rebuild
```

## 🔧 故障排查

### 端口被占用
```bash
# 停止可能占用端口的容器
pnpm run docker:stop

# 检查端口占用
lsof -i :3000
```

### 构建失败
```bash
# 清理后重建
pnpm run docker:clean
pnpm run docker:dev:rebuild
```

### 网络问题（国内用户）
```bash
# 使用国内镜像加速
pnpm run docker:dev:china
```

---

*最后更新：2025-08-18*
*这些命令让 Docker 使用更加便捷！*