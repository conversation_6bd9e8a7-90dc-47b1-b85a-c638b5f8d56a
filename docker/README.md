# 📦 Docker 部署架构总览

## 🏗️ 目录结构

```
docker/
├── README.md                    # 本文档
├── DOCKER_部署文件整理与使用指南.md  # 文件结构详细说明
├── TROUBLESHOOTING.md           # 故障排查指南
├── 1panel/                      # 1Panel 部署专用
│   ├── Dockerfile.1panel        # 1Panel优化版Dockerfile
│   ├── docker-compose.yml       # 1Panel编排配置
│   ├── docker-compose.simple.yml # 简化版配置
│   └── *.md                     # 1Panel相关文档
└── docs/                        # Docker文档集合
    ├── DOCKER_DEPLOYMENT.md     # 完整部署指南
    ├── DOCKER_README.md         # 快速使用参考
    └── *.md                     # 其他指南文档
```

## 🚀 快速开始

根据你的需求选择合适的部署方式：

### 1. 本地开发环境
```bash
./scripts/local-start.sh
# 或
docker-compose -f docker-compose.local.yml up
```

### 2. 生产环境部署
```bash
./scripts/prod.sh
# 或
docker-compose -f docker-compose.prod.yml up -d
```

### 3. 1Panel 部署
```bash
./scripts/1panel-deploy.sh
# 然后在1Panel界面导入配置
```

## 📚 文档导航

| 文档 | 描述 | 适合人群 |
|-----|------|---------|
| [快速使用参考](docs/DOCKER_README.md) | 常用命令和基础配置 | 开发者日常使用 |
| [完整部署指南](docs/DOCKER_DEPLOYMENT.md) | 所有部署场景详解 | 运维部署 |
| [文件整理指南](DOCKER_部署文件整理与使用指南.md) | 架构说明和文件用途 | 项目维护者 |
| [1Panel部署](1panel/README.md) | 1Panel专用指南 | 1Panel用户 |
| [故障排查](TROUBLESHOOTING.md) | 常见问题解决 | 所有用户 |

## 🎯 配置文件说明

### 根目录配置
- `Dockerfile` - 通用多阶段构建文件
- `docker-compose.yml` - 基础配置模板
- `docker-compose.local.yml` - 本地开发环境
- `docker-compose.prod.yml` - 生产环境配置

### 1Panel 专用
- `docker/1panel/Dockerfile.1panel` - 优化版构建文件
- `docker/1panel/docker-compose.yml` - 1Panel编排配置

## 🔧 维护说明

- 所有Docker相关文档已整理到 `docker/` 目录
- 根目录只保留必要的配置文件
- 脚本文件在 `scripts/` 目录
- 环境变量模板在 `.env.example`

---

*最后更新：2025-08-18*