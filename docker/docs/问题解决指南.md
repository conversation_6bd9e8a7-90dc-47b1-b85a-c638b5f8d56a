# 🔧 Docker 问题解决指南

> 遇到问题不要慌！这里收集了最常见的问题和解决方案。

## 🚨 错误速查表

| 错误关键词 | 可能原因 | 跳转章节 |
|-----------|---------|---------|
| `permission denied` | 权限问题 | [权限相关](#权限相关问题) |
| `port is already allocated` | 端口占用 | [端口相关](#端口相关问题) |
| `no space left` | 磁盘空间不足 | [存储相关](#存储相关问题) |
| `cannot connect to Docker` | Docker 未启动 | [Docker 服务](#docker-服务问题) |
| `network not found` | 网络配置错误 | [网络相关](#网络相关问题) |
| `exec format error` | 架构不匹配 | [架构兼容](#架构兼容问题) |

---

## 🔴 Docker 服务问题

### 问题：Cannot connect to the Docker daemon

**错误信息：**
```
Cannot connect to the Docker daemon at unix:///var/run/docker.sock. 
Is the docker daemon running?
```

**解决方案：**

#### Windows 用户
1. **检查 Docker Desktop 是否启动**
   ```powershell
   # 查看 Docker 服务状态
   Get-Service docker
   
   # 启动 Docker Desktop
   Start-Process "C:\Program Files\Docker\Docker\Docker Desktop.exe"
   ```

2. **检查 WSL2**
   ```powershell
   # 安装/更新 WSL2
   wsl --install
   wsl --update
   
   # 设置 WSL2 为默认
   wsl --set-default-version 2
   ```

3. **重启 Docker Desktop**
   - 系统托盘 → 右键 Docker 图标 → Restart

#### Mac 用户
1. **启动 Docker Desktop**
   ```bash
   # 通过命令行启动
   open -a Docker
   
   # 或通过 Launchpad 启动
   ```

2. **检查进程**
   ```bash
   # 查看 Docker 进程
   ps aux | grep docker
   
   # 强制重启
   killall Docker && open -a Docker
   ```

#### Linux 用户
1. **启动 Docker 服务**
   ```bash
   # systemd 系统
   sudo systemctl start docker
   sudo systemctl enable docker
   
   # 检查状态
   sudo systemctl status docker
   ```

2. **添加用户到 docker 组**
   ```bash
   sudo usermod -aG docker $USER
   # 注销并重新登录
   ```

---

## 🔵 端口相关问题

### 问题：Port is already allocated

**错误信息：**
```
Error: bind: address already in use
docker: Error response from daemon: Ports are not available: listen tcp 0.0.0.0:3000: bind: address already in use.
```

**解决方案：**

1. **查找占用端口的进程**
   
   ```bash
   # Windows PowerShell
   netstat -ano | findstr :3000
   # 找到 PID 后
   taskkill /PID [进程ID] /F
   
   # Mac/Linux
   lsof -i :3000
   # 找到 PID 后
   kill -9 [进程ID]
   ```

2. **修改项目端口**
   
   编辑 `docker-compose.dev.yml`：
   ```yaml
   ports:
     - "3001:3000"  # 改为其他端口
   ```
   
   然后访问 http://localhost:3001

3. **使用脚本自动检测可用端口**
   
   ```bash
   #!/bin/bash
   # 查找可用端口
   for port in {3000..3010}; do
     if ! lsof -i:$port > /dev/null 2>&1; then
       echo "可用端口: $port"
       break
     fi
   done
   ```

---

## 💾 存储相关问题

### 问题：No space left on device

**错误信息：**
```
failed to register layer: Error processing tar file(exit status 1): write /app/node_modules/...: no space left on device
```

**解决方案：**

1. **清理 Docker 占用空间**
   
   ```bash
   # 查看 Docker 占用空间
   docker system df
   
   # 清理停止的容器
   docker container prune
   
   # 清理未使用的镜像
   docker image prune -a
   
   # 清理未使用的卷
   docker volume prune
   
   # 清理未使用的网络
   docker network prune
   
   # 一键清理所有（慎用！）
   docker system prune -a --volumes
   ```

2. **调整 Docker Desktop 存储限制**
   
   - Docker Desktop → Settings → Resources → Disk image size
   - 增加到至少 20GB

3. **移动 Docker 数据目录**
   
   ```bash
   # Linux - 修改 Docker 数据目录
   sudo systemctl stop docker
   sudo mv /var/lib/docker /新路径/docker
   sudo ln -s /新路径/docker /var/lib/docker
   sudo systemctl start docker
   ```

---

## 🔐 权限相关问题

### 问题：Permission denied

**错误信息：**
```
Got permission denied while trying to connect to the Docker daemon socket
```

**解决方案：**

#### Linux 用户
```bash
# 添加当前用户到 docker 组
sudo usermod -aG docker $USER

# 重新加载组信息（或重新登录）
newgrp docker

# 验证
docker run hello-world
```

#### Windows 用户
1. 以管理员身份运行 PowerShell
2. 或在 Docker Desktop 设置中启用 "Expose daemon on tcp://localhost:2375 without TLS"

#### 文件权限问题
```bash
# 修改脚本执行权限
chmod +x scripts/*.sh

# 修改文件所有者
sudo chown -R $(whoami):$(whoami) .
```

---

## 🌐 网络相关问题

### 问题：Network not found

**错误信息：**
```
Error response from daemon: network next-wallpaper-network not found
```

**解决方案：**

1. **创建网络**
   ```bash
   # 创建网络
   docker network create next-wallpaper-network
   
   # 或让 docker-compose 自动创建
   docker-compose -f docker-compose.dev.yml up
   ```

2. **清理并重建网络**
   ```bash
   # 停止所有容器
   docker-compose down
   
   # 清理网络
   docker network prune
   
   # 重新启动
   docker-compose up
   ```

### 问题：容器间无法通信

**解决方案：**

1. **检查网络配置**
   ```bash
   # 查看网络
   docker network ls
   
   # 检查容器网络
   docker inspect [容器名] | grep NetworkMode
   
   # 确保容器在同一网络
   docker network connect [网络名] [容器名]
   ```

2. **使用服务名通信**
   ```yaml
   # 在 docker-compose.yml 中
   services:
     app:
       networks:
         - app-network
     db:
       networks:
         - app-network
   
   # app 容器访问 db
   # 使用: http://db:端口
   ```

---

## 🏗 构建相关问题

### 问题：镜像构建失败

**常见原因和解决方案：**

1. **网络问题导致依赖下载失败**
   ```dockerfile
   # 使用国内镜像源
   RUN npm config set registry https://registry.npmmirror.com
   RUN pnpm config set registry https://registry.npmmirror.com
   ```

2. **缓存问题**
   ```bash
   # 不使用缓存重新构建
   docker-compose build --no-cache
   
   # 或使用脚本
   ./scripts/dev.sh --build
   ```

3. **内存不足**
   ```bash
   # 增加 Docker Desktop 内存限制
   # Settings → Resources → Memory → 4GB+
   
   # 或在构建时限制并发
   docker build --memory=2g .
   ```

### 问题：COPY failed: no such file or directory

**解决方案：**

1. **检查 .dockerignore**
   ```bash
   # 确保文件没有被忽略
   cat .dockerignore | grep [文件名]
   ```

2. **检查路径**
   ```dockerfile
   # 使用正确的相对路径
   COPY ./package.json /app/
   # 而不是
   COPY package.json /app/
   ```

---

## 💻 架构兼容问题

### 问题：exec format error

**错误信息：**
```
standard_init_linux.go:228: exec user process caused: exec format error
```

**解决方案：**

1. **M1/M2 Mac 用户**
   ```bash
   # 指定平台构建
   docker build --platform linux/amd64 .
   
   # 或在 docker-compose.yml 中
   services:
     app:
       platform: linux/amd64
   ```

2. **使用多架构镜像**
   ```dockerfile
   # 使用支持多架构的基础镜像
   FROM --platform=$BUILDPLATFORM node:20-alpine
   ```

---

## 🔥 性能相关问题

### 问题：容器运行缓慢

**解决方案：**

1. **增加资源限制**
   
   Docker Desktop → Settings → Resources：
   - CPUs: 至少 2 核
   - Memory: 至少 4GB
   - Swap: 1GB
   - Disk image size: 20GB+

2. **优化挂载卷**
   ```yaml
   volumes:
     # 使用 delegated 提高 Mac 性能
     - ./app:/app:delegated
     
     # 或使用 cached（读多写少）
     - ./public:/app/public:cached
   ```

3. **使用 .dockerignore**
   ```
   node_modules
   .next
   .git
   *.log
   ```

### 问题：热重载不工作

**解决方案：**

1. **启用轮询模式**
   ```yaml
   environment:
     WATCHPACK_POLLING: true
     CHOKIDAR_USEPOLLING: true
   ```

2. **检查文件挂载**
   ```bash
   # 进入容器检查
   docker exec -it next-wallpaper-dev sh
   ls -la /app
   # 确认文件同步
   ```

---

## 📋 日志和调试

### 如何查看详细错误信息

1. **查看容器日志**
   ```bash
   # 查看所有日志
   docker logs next-wallpaper-dev
   
   # 实时查看日志
   docker logs -f next-wallpaper-dev
   
   # 查看最后 100 行
   docker logs --tail 100 next-wallpaper-dev
   ```

2. **进入容器调试**
   ```bash
   # 进入运行中的容器
   docker exec -it next-wallpaper-dev sh
   
   # 查看进程
   ps aux
   
   # 查看环境变量
   env
   
   # 测试网络
   ping google.com
   wget http://localhost:3000
   ```

3. **启用调试模式**
   ```yaml
   environment:
     DEBUG: "*"
     NODE_ENV: development
   ```

---

## 🆘 终极解决方案

### 完全重置 Docker 环境

当所有方法都失败时，试试完全重置：

```bash
#!/bin/bash
# 警告：这会删除所有 Docker 数据！

echo "⚠️  警告：即将删除所有 Docker 数据"
read -p "确定继续？(yes/no) " -r
if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    # 停止所有容器
    docker stop $(docker ps -aq) 2>/dev/null || true
    
    # 删除所有容器
    docker rm $(docker ps -aq) 2>/dev/null || true
    
    # 删除所有镜像
    docker rmi $(docker images -q) 2>/dev/null || true
    
    # 清理系统
    docker system prune -a --volumes -f
    
    # 重启 Docker
    # Mac
    killall Docker && open -a Docker
    # Linux
    # sudo systemctl restart docker
    # Windows - 手动重启 Docker Desktop
    
    echo "✅ Docker 环境已重置"
fi
```

---

## 📚 获取更多帮助

### 1. 查看 Docker 版本信息
```bash
docker version
docker info
docker-compose version
```

### 2. 收集诊断信息
```bash
# 创建诊断报告
docker system info > docker-diagnosis.txt
docker ps -a >> docker-diagnosis.txt
docker images >> docker-diagnosis.txt
docker network ls >> docker-diagnosis.txt
```

### 3. 寻求帮助时提供
- 完整的错误信息
- 执行的命令
- Docker 版本
- 操作系统版本
- docker-compose.yml 内容

### 4. 求助渠道
- 项目 Issues：[GitHub Issues]
- Docker 论坛：https://forums.docker.com/
- Stack Overflow：标签 [docker]
- Docker 中文社区：https://www.docker.org.cn/

---

## 💡 预防措施

### 日常维护建议

1. **定期清理**
   ```bash
   # 每周执行一次
   docker system prune -f
   ```

2. **监控资源**
   ```bash
   # 查看资源使用
   docker system df
   docker stats
   ```

3. **备份重要数据**
   ```bash
   # 备份镜像
   docker save next-wallpaper:dev | gzip > backup.tar.gz
   ```

4. **保持更新**
   - 定期更新 Docker Desktop
   - 更新项目依赖

---

> 💪 **记住**：99% 的 Docker 问题都可以通过重启 Docker Desktop 解决！

---

*问题没有解决？在项目 Issues 中描述你的问题，我们会帮助你！*