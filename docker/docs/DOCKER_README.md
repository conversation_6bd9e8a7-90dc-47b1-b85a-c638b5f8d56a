# Docker 运行环境使用指南

> **文档定位**：Docker快速使用参考，常用命令和基础配置
> 
> **相关文档**：
> - `DOCKER_DEPLOYMENT.md` - 完整部署指南（同目录）
> - `../DOCKER_部署文件整理与使用指南.md` - 文件结构说明

## 🚀 快速开始

### 前置要求
- Docker >= 20.10
- Docker Compose >= 2.0
- Node.js 20.18.3（仅本地开发需要）

### ⚠️ 重要配置
在构建前，请确保 `next.config.ts` 已配置以下选项：
```typescript
{
  output: 'standalone',  // 必须：用于 Docker 部署
  eslint: {
    ignoreDuringBuilds: true  // 避免构建时 ESLint 警告导致失败
  },
  typescript: {
    ignoreBuildErrors: true   // 避免构建时类型错误导致失败
  }
}
```

### 一键启动

#### 开发环境
```bash
# 使用脚本启动
./scripts/dev.sh

# 或使用 docker-compose
docker-compose -f docker-compose.local.yml up
```

#### 生产环境
```bash
# 使用脚本启动
./scripts/prod.sh

# 或使用 docker-compose
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 文件结构

```
.
├── Dockerfile                 # 多阶段构建配置
├── .dockerignore             # Docker 忽略文件
├── docker-compose.local.yml    # 开发环境配置
├── docker-compose.prod.yml   # 生产环境配置
├── docker-compose.yml        # 默认配置
├── docker/
│   └── 1panel/
│       ├── Dockerfile.1panel     # 1Panel 专用 Dockerfile（国内优化版）
│       ├── DOCKERFILE_GUIDE.md   # Dockerfile 使用指南
│       └── 快速部署指南.md        # 1Panel 部署文档
├── .env.example              # 环境变量示例
└── scripts/
    ├── dev.sh               # 开发环境启动脚本
    ├── prod.sh              # 生产环境启动脚本
    ├── build.sh             # 镜像构建脚本
    └── setup-docker-mirror.sh # Docker 镜像加速配置

## 🛠️ 使用方法

### 1. 环境变量配置

```bash
# 复制环境变量示例文件
cp .env.example .env.local      # 开发环境
cp .env.example .env.production # 生产环境

# 编辑环境变量
vi .env.local
```

### 2. 开发环境

开发环境支持热重载，代码修改会自动更新。

```bash
# 启动开发环境
./scripts/dev.sh

# 查看日志
docker-compose -f docker-compose.local.yml logs -f

# 进入容器
docker exec -it next-wallpaper-dev sh

# 停止服务
docker-compose -f docker-compose.local.yml down
```

### 3. 生产环境

生产环境使用优化后的构建，体积更小，性能更好。

```bash
# 构建生产镜像
./scripts/build.sh

# 启动生产环境
./scripts/prod.sh

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 停止服务
docker-compose -f docker-compose.prod.yml down
```

### 4. 镜像构建

```bash
# 构建默认镜像（生产）
./scripts/build.sh

# 构建开发镜像
./scripts/build.sh -d

# 构建带标签的镜像
./scripts/build.sh -t v1.0.0

# 构建并推送到仓库
./scripts/build.sh -t v1.0.0 -p -r registry.example.com

# 构建 1Panel 版本（已内置国内镜像加速）
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

## 🔧 常用命令

### Docker Compose 命令

```bash
# 启动服务
docker-compose -f docker-compose.[dev|prod].yml up -d

# 停止服务
docker-compose -f docker-compose.[dev|prod].yml down

# 重启服务
docker-compose -f docker-compose.[dev|prod].yml restart

# 查看日志
docker-compose -f docker-compose.[dev|prod].yml logs -f

# 查看服务状态
docker-compose -f docker-compose.[dev|prod].yml ps

# 重新构建镜像
docker-compose -f docker-compose.[dev|prod].yml build --no-cache
```

### Docker 命令

```bash
# 查看运行的容器
docker ps

# 进入容器
docker exec -it [container-name] sh

# 查看容器日志
docker logs -f [container-name]

# 查看镜像
docker images | grep next-wallpaper

# 删除镜像
docker rmi next-wallpaper:latest

# 清理未使用的资源
docker system prune -a
```

## 📊 资源配置

### 开发环境资源限制
- CPU: 最大 2 核，预留 1 核
- 内存: 最大 2GB，预留 1GB

### 生产环境资源限制
- CPU: 最大 1 核，预留 0.5 核
- 内存: 最大 1GB，预留 512MB

## 🔍 健康检查

两个环境都配置了健康检查：
- 检查间隔：30 秒
- 超时时间：10 秒
- 重试次数：3 次
- 启动等待：40 秒

## 🐛 故障排查

### 容器无法启动
```bash
# 检查日志
docker-compose -f docker-compose.[dev|prod].yml logs

# 检查端口占用
lsof -i :3000
```

### 热重载不工作（开发环境）
1. 确保 `WATCHPACK_POLLING=true` 在环境变量中
2. 检查文件挂载是否正确
3. 重启容器

### 构建失败
```bash
# 清理缓存重新构建
docker-compose -f docker-compose.[dev|prod].yml build --no-cache

# 查看详细构建日志
docker build --progress=plain -f Dockerfile .
```

#### 常见构建错误解决：

**1. Docker Hub 网络超时**
```bash
# 配置 Docker 镜像加速
./scripts/setup-docker-mirror.sh
# 重启 Docker Desktop 后重试构建
```

**2. ESLint/TypeScript 错误**
在 `next.config.ts` 中添加：
```typescript
eslint: {
  ignoreDuringBuilds: true
},
typescript: {
  ignoreBuildErrors: true
}
```

**3. 镜像拉取失败**
1Panel Dockerfile 已内置国内加速：
```bash
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

### 性能问题
1. 调整资源限制配置
2. 检查 Docker 守护进程资源设置
3. 使用生产构建而不是开发构建

## 📝 注意事项

1. **开发环境**：适合本地开发，包含所有开发依赖，支持热重载
2. **生产环境**：优化后的镜像，仅包含运行时依赖，体积更小
3. **端口冲突**：确保 3000 端口未被占用
4. **文件权限**：脚本需要执行权限 (`chmod +x scripts/*.sh`)
5. **环境变量**：敏感信息不要提交到版本控制

## 🔄 更新部署

```bash
# 1. 拉取最新代码
git pull

# 2. 重新构建镜像
./scripts/build.sh -t v1.0.1

# 3. 停止旧服务
docker-compose -f docker-compose.prod.yml down

# 4. 启动新服务
docker-compose -f docker-compose.prod.yml up -d
```

## 📦 镜像说明

Dockerfile 使用多阶段构建：

1. **deps 阶段**：安装生产依赖
2. **builder 阶段**：编译 TypeScript 和构建 Next.js
3. **runner 阶段**：生产运行环境（默认）
4. **development 阶段**：开发环境

镜像大小对比：
- 开发镜像：约 500MB
- 生产镜像：约 150MB
- 1Panel 优化版：约 311MB（使用 standalone 模式）

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！