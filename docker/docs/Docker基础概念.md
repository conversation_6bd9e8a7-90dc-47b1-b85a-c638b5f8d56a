# 🎯 Docker 基础概念详解

> 本文档帮助你理解 Docker 的核心概念，让你知其然更知其所以然。

## 📚 核心概念图解

```
┌─────────────────────────────────────────────────────┐
│                    你的电脑（宿主机）                    │
│                                                     │
│  ┌─────────────────────────────────────────────┐  │
│  │            Docker Desktop                    │  │
│  │                                              │  │
│  │  ┌──────────┐  ┌──────────┐  ┌──────────┐  │  │
│  │  │  容器 1   │  │  容器 2   │  │  容器 3   │  │  │
│  │  │          │  │          │  │          │  │  │
│  │  │ Next.js  │  │ Database │  │  Redis   │  │  │
│  │  │   App    │  │          │  │          │  │  │
│  │  └──────────┘  └──────────┘  └──────────┘  │  │
│  │       ↑             ↑             ↑         │  │
│  │       └─────────────┼─────────────┘         │  │
│  │                     ↓                       │  │
│  │              Docker 网络                     │  │
│  └─────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────┘
```

---

## 🏗 三个核心概念

### 1. 镜像（Image）= 📀 安装光盘

**什么是镜像？**
- 想象成**装系统的光盘**或**手机 App 安装包**
- 包含了运行程序所需的所有文件和配置
- **只读**的，不能修改

**实际例子：**
```bash
# 我们项目的镜像
next-wallpaper:dev     # 开发版镜像
next-wallpaper:latest  # 最新版镜像
next-wallpaper:v1.0.0  # 特定版本镜像

# 就像手机 App 的不同版本
微信:8.0.40  # 特定版本
微信:latest  # 最新版本
```

### 2. 容器（Container）= 🏃 运行的程序

**什么是容器？**
- 镜像运行起来就变成了容器
- 就像**安装包安装后正在运行的程序**
- 可以启动、停止、删除

**类比理解：**
| Docker | 手机 App | Windows |
|--------|----------|---------|
| 镜像 | App 安装包 | 安装程序.exe |
| 容器 | 运行中的 App | 正在运行的程序 |
| 启动容器 | 点击 App 图标 | 双击运行程序 |
| 停止容器 | 关闭 App | 关闭程序 |

### 3. 仓库（Registry）= 📱 应用商店

**什么是仓库？**
- 存放镜像的地方
- 就像 **App Store** 或 **应用宝**
- 可以上传（push）和下载（pull）镜像

**常见仓库：**
- **Docker Hub**：官方仓库（像 Google Play）
- **阿里云镜像**：国内镜像（下载更快）
- **私有仓库**：公司内部的（像企业应用商店）

---

## 📁 Dockerfile 详解

### 什么是 Dockerfile？

Dockerfile 就像是**做菜的食谱** 📖，告诉 Docker 如何一步步构建镜像。

```dockerfile
# 这是我们项目的 Dockerfile 简化版

# 1. 选择基础镜像（像选择锅具）
FROM node:20-alpine
# 解释：使用 Node.js 20 版本的轻量级 Linux 系统

# 2. 设置工作目录（像准备案板）
WORKDIR /app
# 解释：在容器里创建 /app 文件夹并进入

# 3. 复制配置文件（像准备调料）
COPY package.json pnpm-lock.yaml ./
# 解释：先复制依赖配置文件

# 4. 安装依赖（像准备食材）
RUN pnpm install
# 解释：安装项目需要的所有包

# 5. 复制源代码（像放入主料）
COPY . .
# 解释：把所有代码复制到容器

# 6. 构建项目（像烹饪过程）
RUN pnpm build
# 解释：编译 TypeScript，打包代码

# 7. 设置启动命令（像上菜）
CMD ["pnpm", "start"]
# 解释：容器启动时运行的命令
```

### Dockerfile 指令说明

| 指令 | 作用 | 类比 |
|------|------|------|
| FROM | 选择基础镜像 | 选择手机型号 |
| WORKDIR | 设置工作目录 | 创建文件夹 |
| COPY | 复制文件 | 复制粘贴 |
| RUN | 执行命令 | 运行安装程序 |
| CMD | 启动命令 | 设置开机启动 |
| EXPOSE | 声明端口 | 开放网络端口 |
| ENV | 设置环境变量 | 系统设置 |

---

## 🐙 Docker Compose 详解

### 什么是 Docker Compose？

Docker Compose 就像是**项目管理器**，可以同时管理多个容器。

**类比：开餐厅** 🍽
- **Docker**：管理单个设备（烤箱、冰箱、咖啡机）
- **Docker Compose**：管理整个厨房（所有设备协同工作）

### docker-compose.yml 解析

```yaml
# 这是简化版的配置文件

services:
  # 定义一个服务（像定义一个岗位）
  app:
    # 使用的镜像（像指定设备型号）
    image: next-wallpaper:dev
    
    # 端口映射（像设置营业窗口）
    ports:
      - "3000:3000"
      # 格式：宿主机端口:容器端口
      # 类比：店面窗口:厨房窗口
    
    # 环境变量（像设置工作参数）
    environment:
      NODE_ENV: development
      # 告诉程序现在是开发模式
    
    # 挂载卷（像共享文件夹）
    volumes:
      - ./app:/app/app
      # 格式：本地路径:容器路径
      # 作用：代码修改立即生效

# 网络配置（像内部通讯系统）
networks:
  app-network:
    driver: bridge
```

---

## 🔄 Docker 工作流程

### 完整的工作流程

```
1. 编写代码
     ↓
2. 编写 Dockerfile
     ↓
3. 构建镜像 (docker build)
     ↓
4. 运行容器 (docker run)
     ↓
5. 测试应用
     ↓
6. 推送镜像 (docker push)
     ↓
7. 部署到服务器
```

### 日常开发流程

```
早上开始工作
     ↓
启动 Docker Desktop
     ↓
运行 ./scripts/dev.sh
     ↓
开始编码（代码自动同步）
     ↓
查看浏览器实时效果
     ↓
下班前运行 docker-compose down
```

---

## 💡 理解端口映射

### 什么是端口映射？

想象你住在一栋公寓楼：
- **公寓楼** = 你的电脑
- **房间** = Docker 容器
- **门牌号** = 端口号

```
你的电脑（公寓楼）
├── 3000号门 ←→ 容器内部3000号门
├── 8080号门 ←→ 容器内部80号门
└── 5432号门 ←→ 数据库容器5432号门
```

### 端口映射配置

```yaml
ports:
  - "3000:3000"  # 本地3000 映射到 容器3000
  - "8080:80"    # 本地8080 映射到 容器80
  
# 访问方式：
# http://localhost:3000 → 容器内的3000端口
# http://localhost:8080 → 容器内的80端口
```

---

## 📂 挂载卷（Volumes）理解

### 什么是挂载卷？

挂载卷就像**共享文件夹**，让容器和你的电脑可以共享文件。

**类比：云盘同步** ☁️
- 你在电脑上修改文件
- 自动同步到容器内
- 实现代码热重载

### 三种挂载方式

```yaml
volumes:
  # 1. 绑定挂载（最常用）
  - ./app:/app/app
  # 本地的 ./app 文件夹 同步到 容器的 /app/app
  
  # 2. 匿名卷
  - /app/node_modules
  # 容器内独立管理，不与本地同步
  
  # 3. 命名卷
  - data-volume:/data
  # Docker 管理的持久化存储
```

---

## 🌐 Docker 网络

### 容器间通信

容器之间可以通过网络互相通信，就像手机连接 WiFi。

```
Docker 网络（像 WiFi 路由器）
├── Next.js 容器（***********）
├── 数据库容器（***********）
└── Redis 容器（***********）

# 容器间可以用服务名访问
Next.js → http://database:5432
Next.js → http://redis:6379
```

---

## ⚙️ 环境变量

### 什么是环境变量？

环境变量就像**程序的配置开关**，告诉程序该如何运行。

**类比：手机设置** 📱
- 亮度调节 = NODE_ENV
- 音量大小 = LOG_LEVEL
- WiFi 开关 = DEBUG_MODE

### 常见环境变量

```bash
# 运行模式
NODE_ENV=development  # 开发模式（显示详细错误）
NODE_ENV=production   # 生产模式（隐藏错误细节）

# 端口设置
PORT=3000            # 应用运行在3000端口

# 功能开关
NEXT_TELEMETRY_DISABLED=1  # 关闭数据收集
DEBUG=*                     # 开启所有调试信息
```

---

## 🔍 常见误区澄清

### 误区 1：Docker 是虚拟机？
**❌ 错误**：Docker 是虚拟机
**✅ 正确**：Docker 是容器，比虚拟机轻量很多

| 特性 | 虚拟机 | Docker 容器 |
|------|--------|------------|
| 启动速度 | 分钟级 | 秒级 |
| 资源占用 | GB 级别 | MB 级别 |
| 系统内核 | 独立内核 | 共享内核 |

### 误区 2：必须懂 Linux？
**❌ 错误**：用 Docker 必须精通 Linux
**✅ 正确**：Docker Desktop 提供了图形界面，基础操作不需要 Linux 知识

### 误区 3：Docker 很占资源？
**❌ 错误**：Docker 会让电脑变慢
**✅ 正确**：合理配置的 Docker 占用资源很少

---

## 📖 进阶学习路径

### 第一阶段：基础使用（1-2周）
- ✅ 安装 Docker Desktop
- ✅ 运行现有项目
- ✅ 基本命令操作

### 第二阶段：理解原理（2-4周）
- 📚 理解镜像分层
- 📚 学习 Dockerfile 编写
- 📚 掌握 docker-compose

### 第三阶段：实践应用（1-2月）
- 🚀 构建自己的镜像
- 🚀 多容器编排
- 🚀 性能优化

### 第四阶段：生产部署（2-3月）
- ⚡ CI/CD 集成
- ⚡ Kubernetes 入门
- ⚡ 监控和日志

---

## 🎓 学习资源推荐

### 视频教程
- [Docker 入门到实践](https://www.bilibili.com/video/xxx)
- [10分钟学会 Docker](https://www.youtube.com/xxx)

### 在线实验
- [Play with Docker](https://labs.play-with-docker.com/)
- [Katacoda Docker 课程](https://www.katacoda.com/courses/docker)

### 书籍推荐
- 《Docker 技术入门与实战》
- 《深入浅出 Docker》

---

## ❓ 思考题

1. **为什么要用 Docker？**
   - 想想：如果没有 Docker，部署项目会遇到什么问题？

2. **镜像和容器的关系？**
   - 类比：镜像是类（Class），容器是实例（Instance）

3. **为什么要分层构建？**
   - 提示：缓存机制，提高构建效率

---

> 💡 **记住**：Docker 的核心价值是 **"一次构建，到处运行"**

---

*最后更新：2024年*