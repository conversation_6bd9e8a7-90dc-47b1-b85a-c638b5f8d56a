# 📋 Docker 快速参考手册

> 本手册提供常用命令和操作的快速查询，适合已经了解基础的用户。

## 🚀 快速启动

### 一键命令
```bash
# 开发环境
./scripts/dev.sh

# 生产环境
./scripts/prod.sh

# 构建镜像
./scripts/build.sh
```

---

## 📝 Docker Compose 命令

### 基础操作
```bash
# 启动服务
docker-compose -f docker-compose.dev.yml up -d

# 停止服务
docker-compose -f docker-compose.dev.yml down

# 重启服务
docker-compose -f docker-compose.dev.yml restart

# 查看状态
docker-compose -f docker-compose.dev.yml ps

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f
```

### 高级操作
```bash
# 重新构建并启动
docker-compose -f docker-compose.dev.yml up --build -d

# 强制重新创建容器
docker-compose -f docker-compose.dev.yml up --force-recreate

# 扩展服务副本
docker-compose -f docker-compose.dev.yml up --scale app=3

# 只构建不启动
docker-compose -f docker-compose.dev.yml build

# 验证配置文件
docker-compose -f docker-compose.dev.yml config
```

---

## 🐳 Docker 命令

### 容器管理
```bash
# 列出运行中的容器
docker ps

# 列出所有容器
docker ps -a

# 启动容器
docker start [容器名/ID]

# 停止容器
docker stop [容器名/ID]

# 重启容器
docker restart [容器名/ID]

# 删除容器
docker rm [容器名/ID]

# 进入容器
docker exec -it [容器名] sh

# 查看容器日志
docker logs -f [容器名]

# 查看容器资源使用
docker stats [容器名]

# 查看容器详情
docker inspect [容器名]
```

### 镜像管理
```bash
# 列出镜像
docker images

# 搜索镜像
docker search [镜像名]

# 拉取镜像
docker pull [镜像名:标签]

# 删除镜像
docker rmi [镜像ID]

# 构建镜像
docker build -t [镜像名:标签] .

# 标记镜像
docker tag [源镜像] [新镜像名:标签]

# 推送镜像
docker push [镜像名:标签]

# 导出镜像
docker save -o [文件名.tar] [镜像名]

# 导入镜像
docker load -i [文件名.tar]
```

### 网络管理
```bash
# 列出网络
docker network ls

# 创建网络
docker network create [网络名]

# 删除网络
docker network rm [网络名]

# 查看网络详情
docker network inspect [网络名]

# 连接容器到网络
docker network connect [网络名] [容器名]

# 断开容器网络连接
docker network disconnect [网络名] [容器名]
```

### 卷管理
```bash
# 列出卷
docker volume ls

# 创建卷
docker volume create [卷名]

# 删除卷
docker volume rm [卷名]

# 查看卷详情
docker volume inspect [卷名]

# 清理未使用的卷
docker volume prune
```

---

## 🔧 环境变量

### 开发环境变量
```bash
NODE_ENV=development
NEXT_TELEMETRY_DISABLED=1
WATCHPACK_POLLING=true
PORT=3000
```

### 生产环境变量
```bash
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
PORT=3000
HOSTNAME=0.0.0.0
```

---

## 📁 文件结构速查

```
项目根目录/
├── Dockerfile                # 镜像构建文件
├── .dockerignore            # Docker 忽略文件
├── docker-compose.yml       # 基础配置
├── docker-compose.dev.yml  # 开发环境
├── docker-compose.prod.yml # 生产环境
├── .env.example            # 环境变量示例
├── .env.local             # 开发环境变量
├── .env.production        # 生产环境变量
└── scripts/               # 脚本目录
    ├── dev.sh            # 开发启动脚本
    ├── prod.sh           # 生产启动脚本
    └── build.sh          # 构建脚本
```

---

## 🎯 常用场景

### 场景 1：更新依赖后重新构建
```bash
# 停止容器
docker-compose -f docker-compose.dev.yml down

# 重新构建
./scripts/dev.sh --build

# 或一步完成
docker-compose -f docker-compose.dev.yml up --build -d
```

### 场景 2：清理所有 Docker 资源
```bash
# 停止所有容器
docker stop $(docker ps -aq)

# 删除所有容器
docker rm $(docker ps -aq)

# 删除所有镜像
docker rmi $(docker images -q)

# 清理系统
docker system prune -a --volumes
```

### 场景 3：调试容器内部
```bash
# 进入运行中的容器
docker exec -it next-wallpaper-dev sh

# 在容器内执行命令
docker exec next-wallpaper-dev ls -la

# 复制文件到容器
docker cp local-file.txt next-wallpaper-dev:/app/

# 从容器复制文件
docker cp next-wallpaper-dev:/app/file.txt ./
```

### 场景 4：备份和恢复
```bash
# 备份镜像
docker save next-wallpaper:dev | gzip > backup.tar.gz

# 恢复镜像
gunzip -c backup.tar.gz | docker load

# 备份容器
docker export next-wallpaper-dev | gzip > container-backup.tar.gz

# 恢复容器
gunzip -c container-backup.tar.gz | docker import - next-wallpaper:backup
```

---

## 📊 资源监控

### 实时监控
```bash
# 查看容器资源使用
docker stats

# 查看特定容器
docker stats next-wallpaper-dev

# 查看容器进程
docker top next-wallpaper-dev
```

### 日志分析
```bash
# 查看最后 100 行日志
docker logs --tail 100 next-wallpaper-dev

# 查看特定时间后的日志
docker logs --since 2024-01-01 next-wallpaper-dev

# 导出日志到文件
docker logs next-wallpaper-dev > logs.txt 2>&1
```

---

## ⚡ 性能优化

### Docker 构建优化
```bash
# 使用构建缓存
docker build --cache-from next-wallpaper:dev -t next-wallpaper:dev .

# 多阶段构建（已在 Dockerfile 中实现）
# 查看构建历史
docker history next-wallpaper:dev

# 分析镜像大小
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
```

### 资源限制
```yaml
# 在 docker-compose.yml 中设置
services:
  app:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G
```

---

## 🔐 安全建议

### 基础安全
```bash
# 不要以 root 运行
USER nextjs

# 只暴露必要端口
EXPOSE 3000

# 使用 .dockerignore
echo "node_modules" >> .dockerignore
echo ".git" >> .dockerignore
echo ".env*" >> .dockerignore
```

### 镜像扫描
```bash
# 扫描镜像漏洞
docker scan next-wallpaper:dev

# 查看镜像层
docker history next-wallpaper:dev
```

---

## 🎨 VSCode 集成

### 推荐扩展
- Docker
- Docker Compose
- Remote - Containers

### 快捷操作
- `Ctrl+Shift+P` → "Docker: Compose Up"
- 右键 `docker-compose.yml` → "Compose Up"
- 在 Docker 面板管理容器

---

## 🔗 有用的别名

添加到 `~/.bashrc` 或 `~/.zshrc`：

```bash
# Docker 别名
alias dc='docker-compose'
alias dcu='docker-compose up -d'
alias dcd='docker-compose down'
alias dcl='docker-compose logs -f'
alias dps='docker ps'
alias di='docker images'

# 项目特定别名
alias dev='./scripts/dev.sh'
alias prod='./scripts/prod.sh'
alias build='./scripts/build.sh'
```

---

## 📚 更多资源

- [Docker 官方文档](https://docs.docker.com/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [Dockerfile 最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [项目 Wiki](../README.md)

---

*快速参考手册 v1.0*