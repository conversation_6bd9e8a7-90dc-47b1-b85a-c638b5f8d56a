# 📚 Docker 文档中心

> 欢迎来到 Next Wallpaper 项目的 Docker 文档中心！这里有你需要的一切。

## 🎯 快速导航

### 👶 新手入门
- 📖 [**新手入门指南**](./新手入门指南.md) - 从零开始，手把手教学
- 🎓 [**Docker基础概念**](./Docker基础概念.md) - 理解 Docker 核心概念
- ⚡ [**快速参考手册**](./快速参考手册.md) - 常用命令速查

### 🔧 问题解决
- 🚨 [**问题解决指南**](./问题解决指南.md) - 常见问题和解决方案
- 💡 故障排查技巧

### 🚀 进阶内容
- 生产环境部署指南（即将推出）
- 性能优化最佳实践（即将推出）
- CI/CD 集成指南（即将推出）

---

## 📝 文档说明

### 文档结构
```
docker/docs/
├── README.md              # 本文档（导航中心）
├── 新手入门指南.md         # 适合第一次使用的用户
├── Docker基础概念.md      # 深入理解 Docker
├── 快速参考手册.md         # 命令和配置速查
└── 问题解决指南.md         # 故障排查和解决方案
```

### 阅读建议

#### 🆕 如果你是 Docker 新手
1. 先阅读 → [新手入门指南](./新手入门指南.md)
2. 实践操作 → 跟着指南运行项目
3. 深入理解 → [Docker基础概念](./Docker基础概念.md)
4. 遇到问题 → [问题解决指南](./问题解决指南.md)

#### 💻 如果你有 Docker 经验
1. 快速开始 → [快速参考手册](./快速参考手册.md)
2. 查看配置 → 项目根目录的 docker-compose.*.yml
3. 遇到问题 → [问题解决指南](./问题解决指南.md)

---

## 🚀 5分钟快速开始

### 前置要求
- ✅ 安装 Docker Desktop
- ✅ 克隆项目代码
- ✅ 基础命令行知识

### 三步启动
```bash
# 1. 进入项目目录
cd next_wallpaper

# 2. 给脚本执行权限（仅首次）
chmod +x scripts/*.sh

# 3. 启动开发环境
./scripts/dev.sh
```

🎉 **完成！** 访问 http://localhost:3000

---

## 🏗 项目 Docker 架构

```
┌─────────────────────────────────────┐
│         Docker Desktop              │
├─────────────────────────────────────┤
│                                     │
│  ┌─────────────────────────────┐   │
│  │   next-wallpaper-dev        │   │
│  │   (开发环境容器)             │   │
│  │                             │   │
│  │  - Node.js 20.18.3         │   │
│  │  - pnpm 包管理器            │   │
│  │  - Next.js 应用             │   │
│  │  - 端口: 3000              │   │
│  └─────────────────────────────┘   │
│                                     │
│  ┌─────────────────────────────┐   │
│  │   next-wallpaper-prod       │   │
│  │   (生产环境容器)             │   │
│  │                             │   │
│  │  - 优化的生产镜像           │   │
│  │  - 安全配置                │   │
│  │  - 资源限制                │   │
│  └─────────────────────────────┘   │
│                                     │
└─────────────────────────────────────┘
```

---

## 📊 核心配置文件

| 文件 | 用途 | 何时修改 |
|------|------|----------|
| `Dockerfile` | 定义镜像构建步骤 | 添加系统依赖时 |
| `docker-compose.dev.yml` | 开发环境配置 | 调整开发设置时 |
| `docker-compose.prod.yml` | 生产环境配置 | 部署配置时 |
| `.dockerignore` | 忽略文件列表 | 优化构建时 |
| `.env.example` | 环境变量示例 | 添加新配置时 |

---

## 🛠 常用命令一览

### 日常开发
```bash
# 启动开发环境
./scripts/dev.sh

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

### 故障排查
```bash
# 查看容器状态
docker ps -a

# 进入容器
docker exec -it next-wallpaper-dev sh

# 清理资源
docker system prune -a
```

更多命令请查看 → [快速参考手册](./快速参考手册.md)

---

## 🤝 贡献文档

### 如何改进文档？

1. **发现问题**
   - 文档有错误？
   - 缺少某些说明？
   - 有更好的解释方式？

2. **提交改进**
   ```bash
   # 1. Fork 项目
   # 2. 修改文档
   # 3. 提交 PR
   ```

3. **文档规范**
   - 使用中文为主
   - 保持简洁明了
   - 多用示例和类比
   - 添加表情符号增加可读性

---

## 📈 学习路线图

```mermaid
graph LR
    A[安装 Docker] --> B[运行项目]
    B --> C[理解概念]
    C --> D[日常使用]
    D --> E[故障排查]
    E --> F[优化配置]
    F --> G[生产部署]
```

### 推荐学习时间
- 🟢 **基础使用**：1-2 天
- 🟡 **熟练操作**：1-2 周
- 🔴 **精通优化**：1-2 月

---

## 💬 常见问题

### Q: Docker 和虚拟机有什么区别？
**A:** Docker 更轻量，启动更快，资源占用更少。详见 → [Docker基础概念](./Docker基础概念.md)

### Q: 为什么要用 Docker？
**A:** 统一开发环境，避免"在我电脑上能跑"的问题。

### Q: Docker 会让电脑变慢吗？
**A:** 合理配置的 Docker 占用资源很少，不会明显影响性能。

### Q: 需要懂 Linux 吗？
**A:** 基础使用不需要，Docker Desktop 提供了友好的界面。

更多问题 → [问题解决指南](./问题解决指南.md)

---

## 📞 获取帮助

### 遇到问题怎么办？

1. **查看文档**
   - 本文档中心
   - [Docker 官方文档](https://docs.docker.com/)

2. **搜索问题**
   - 项目 Issues
   - Stack Overflow
   - Google/百度

3. **寻求帮助**
   - 在项目 Issues 中提问
   - Docker 社区论坛
   - 技术交流群

---

## 🎯 下一步

### 根据你的需求选择：

- **想快速开始** → [新手入门指南](./新手入门指南.md)
- **想深入理解** → [Docker基础概念](./Docker基础概念.md)
- **遇到了问题** → [问题解决指南](./问题解决指南.md)
- **查找命令** → [快速参考手册](./快速参考手册.md)

---

## 📅 更新记录

| 日期 | 版本 | 更新内容 |
|------|------|----------|
| 2024-08 | v1.0 | 初始版本发布 |
| 2024-08 | v1.1 | 添加问题解决指南 |
| 2024-08 | v1.2 | 优化新手入门指南 |

---

## 📄 许可证

本文档遵循项目主许可证。

---

> 💡 **提示**：建议收藏本页面，方便随时查阅！

---

*Happy Dockering! 🐳*