# 🚀 Next Wallpaper Docker 新手入门指南

> 本指南专为第一次使用 Docker 的开发者编写，将手把手教你如何使用 Docker 运行本项目。

## 📚 目录

1. [什么是 Docker？](#什么是-docker)
2. [环境准备](#环境准备)
3. [快速开始（5分钟上手）](#快速开始)
4. [详细教程](#详细教程)
5. [常用操作](#常用操作)
6. [常见问题](#常见问题)
7. [故障排查](#故障排查)
8. [进阶使用](#进阶使用)

---

## 🐳 什么是 Docker？

### 简单理解
Docker 就像是一个**轻量级的虚拟机**，它可以：
- 📦 把你的应用和所有依赖打包在一起
- 🚢 在任何电脑上都能运行，不用担心环境问题
- 🔧 一键启动，无需复杂配置

### 为什么使用 Docker？
| 传统方式的痛点 | Docker 的解决方案 |
|--------------|-----------------|
| "在我电脑上能跑" | 统一的运行环境 |
| 安装各种依赖很麻烦 | 一键启动所有服务 |
| 环境配置容易出错 | 标准化的配置文件 |
| 团队协作环境不一致 | 每个人都用相同环境 |

---

## 🛠 环境准备

### 第一步：安装 Docker Desktop

#### Windows 用户
1. 访问 [Docker Desktop for Windows](https://docs.docker.com/desktop/install/windows-install/)
2. 下载安装包（约 500MB）
3. 双击安装，一路 Next
4. 重启电脑
5. 启动 Docker Desktop（会在系统托盘显示小鲸鱼图标）

#### Mac 用户
1. 访问 [Docker Desktop for Mac](https://docs.docker.com/desktop/install/mac-install/)
2. 选择你的芯片版本：
   - **Intel 芯片**：下载 Intel 版本
   - **Apple 芯片（M1/M2/M3）**：下载 Apple Silicon 版本
3. 双击 .dmg 文件，拖拽到 Applications
4. 启动 Docker Desktop

#### Linux 用户
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 添加当前用户到 docker 组（避免每次都用 sudo）
sudo usermod -aG docker $USER
# 重新登录后生效
```

### 第二步：验证安装

打开终端（Windows 用户打开 PowerShell 或 CMD），运行：

```bash
# 检查 Docker 版本
docker --version
# 应该显示：Docker version 20.10.x 或更高

# 检查 Docker Compose 版本
docker compose version
# 应该显示：Docker Compose version v2.x.x

# 测试 Docker 是否正常工作
docker run hello-world
# 应该显示：Hello from Docker! 等信息
```

---

## ⚡ 快速开始

> 🎯 目标：5分钟内让项目跑起来！

### 1️⃣ 克隆项目
```bash
# 克隆项目到本地
git clone [项目地址]
cd next_wallpaper
```

### 2️⃣ 启动开发环境
```bash
# 给脚本执行权限（只需要第一次）
chmod +x scripts/*.sh

# 启动开发环境
./scripts/dev.sh
```

### 3️⃣ 访问应用
等待看到 "🚀 开发环境启动成功！" 后，打开浏览器访问：
- 🌐 http://localhost:3000

**恭喜！你已经成功运行了项目！** 🎉

---

## 📖 详细教程

### 理解项目结构

```
next_wallpaper/
├── Dockerfile           # Docker 镜像配置（像是装修图纸）
├── docker-compose.dev.yml   # 开发环境配置
├── docker-compose.prod.yml  # 生产环境配置
├── .dockerignore       # 告诉 Docker 忽略哪些文件
├── .env.example        # 环境变量示例
├── scripts/           # 便捷脚本
│   ├── dev.sh        # 开发环境启动脚本
│   ├── prod.sh       # 生产环境启动脚本
│   └── build.sh      # 构建脚本
└── docker/docs/       # Docker 文档（你正在看的）
```

### 开发环境详细步骤

#### 1. 环境变量配置
```bash
# 复制环境变量示例文件
cp .env.example .env.local

# 编辑环境变量（可选）
# Windows 用户可以用记事本打开
# Mac/Linux 用户可以用 vim 或 nano
nano .env.local
```

#### 2. 启动服务（详细版）
```bash
# 方式一：使用便捷脚本（推荐）
./scripts/dev.sh

# 方式二：使用 docker-compose 命令
docker-compose -f docker-compose.dev.yml up

# 方式三：后台运行
docker-compose -f docker-compose.dev.yml up -d
```

#### 3. 查看运行状态
```bash
# 查看容器状态
docker ps

# 应该看到类似这样的输出：
# CONTAINER ID   IMAGE               STATUS         PORTS
# abc123...      next-wallpaper:dev  Up (healthy)   0.0.0.0:3000->3000/tcp
```

### 生产环境部署

⚠️ **注意**：生产环境需要更多配置，建议先熟悉开发环境。

```bash
# 1. 创建生产环境配置
cp .env.example .env.production
# 编辑 .env.production，设置生产环境变量

# 2. 构建并启动
./scripts/prod.sh

# 3. 确认部署
# 脚本会要求你输入 "yes" 确认
```

---

## 🔧 常用操作

### 日常开发命令

| 操作 | 命令 | 说明 |
|-----|------|-----|
| 启动服务 | `./scripts/dev.sh` | 启动开发环境 |
| 查看日志 | `docker-compose -f docker-compose.dev.yml logs -f` | 实时查看日志 |
| 停止服务 | `docker-compose -f docker-compose.dev.yml down` | 停止并删除容器 |
| 重启服务 | `docker-compose -f docker-compose.dev.yml restart` | 重启容器 |
| 进入容器 | `docker exec -it next-wallpaper-dev sh` | 进入容器内部 |

### 实用技巧

#### 1. 查看实时日志
```bash
# 查看所有日志
docker-compose -f docker-compose.dev.yml logs -f

# 只看最新的 50 行
docker-compose -f docker-compose.dev.yml logs --tail=50

# 按 Ctrl+C 退出日志查看
```

#### 2. 重新构建镜像
```bash
# 当依赖更新时，需要重新构建
./scripts/dev.sh --build

# 或者
docker-compose -f docker-compose.dev.yml up --build
```

#### 3. 清理 Docker 资源
```bash
# 停止所有容器
docker stop $(docker ps -aq)

# 删除所有容器
docker rm $(docker ps -aq)

# 清理未使用的镜像
docker image prune -a

# 清理所有未使用的资源（慎用）
docker system prune -a
```

---

## ❓ 常见问题

### Q1: 端口 3000 已被占用怎么办？

**错误信息**：
```
Error: bind: address already in use
```

**解决方法**：
```bash
# 方法 1：找出占用端口的进程并关闭
# Windows
netstat -ano | findstr :3000
taskkill /PID [进程ID] /F

# Mac/Linux
lsof -i :3000
kill -9 [进程ID]

# 方法 2：修改项目端口
# 编辑 docker-compose.dev.yml
# 将 ports: - "3000:3000" 改为 - "3001:3000"
# 然后访问 http://localhost:3001
```

### Q2: Docker Desktop 启动失败？

**Windows 用户**：
1. 确保开启了虚拟化（在 BIOS 中启用）
2. 确保 Windows 版本是专业版或企业版
3. 安装 WSL2：
   ```powershell
   wsl --install
   ```

**Mac 用户**：
1. 确保 macOS 版本 >= 10.15
2. 给 Docker Desktop 足够的权限
3. 重启 Docker Desktop

### Q3: 镜像构建很慢？

**解决方法**：
1. 使用国内镜像源（在 Docker Desktop 设置中配置）
2. 确保网络连接稳定
3. 第一次构建会下载依赖，后续会使用缓存

### Q4: 如何完全重置环境？

```bash
# 1. 停止并删除所有容器
docker-compose -f docker-compose.dev.yml down -v

# 2. 删除镜像
docker rmi next-wallpaper:dev

# 3. 重新构建
./scripts/dev.sh --build
```

---

## 🔍 故障排查

### 诊断步骤

#### 1. 检查 Docker 状态
```bash
# Docker 是否运行
docker info

# 查看所有容器（包括停止的）
docker ps -a

# 查看容器日志
docker logs next-wallpaper-dev
```

#### 2. 检查网络
```bash
# 列出所有网络
docker network ls

# 检查网络详情
docker network inspect next-wallpaper-dev-network
```

#### 3. 检查镜像
```bash
# 列出所有镜像
docker images

# 查看镜像详情
docker inspect next-wallpaper:dev
```

### 常见错误及解决

| 错误 | 原因 | 解决方法 |
|------|------|---------|
| `Cannot connect to Docker daemon` | Docker 未启动 | 启动 Docker Desktop |
| `No space left on device` | 磁盘空间不足 | 清理 Docker 缓存：`docker system prune -a` |
| `Permission denied` | 权限问题 | Linux: 将用户添加到 docker 组 |
| `Network not found` | 网络配置问题 | 删除网络重建：`docker network prune` |

---

## 🎓 进阶使用

### 使用 Visual Studio Code

1. 安装 Docker 扩展
2. 在 VSCode 中右键点击 `docker-compose.dev.yml`
3. 选择 "Compose Up" 启动服务

### 调试模式

```bash
# 启动调试模式
docker-compose -f docker-compose.dev.yml up

# 不使用 -d 参数，可以直接看到所有输出
```

### 性能优化

1. **调整 Docker Desktop 资源**：
   - 设置 -> Resources
   - CPU: 至少 2 核
   - Memory: 至少 4GB

2. **使用 .dockerignore**：
   - 确保 node_modules 被忽略
   - 减少构建上下文大小

### 多环境管理

```bash
# 开发环境
./scripts/dev.sh

# 测试环境（如果有）
docker-compose -f docker-compose.test.yml up

# 生产环境
./scripts/prod.sh
```

---

## 📞 获取帮助

### 资源链接
- 📖 [Docker 官方文档](https://docs.docker.com/)
- 💬 [Docker 中文社区](https://www.docker.org.cn/)
- 🎥 [Docker 入门视频教程](https://www.youtube.com/watch?v=fqMOX6JJhGo)

### 项目相关
- 查看脚本帮助：`./scripts/dev.sh --help`
- 查看项目文档：`/docker/docs/`
- 提交问题：在项目 GitHub Issues 中提交

---

## 🎉 恭喜！

如果你读到这里，说明你已经掌握了 Docker 的基本使用！

**下一步建议**：
1. 尝试修改代码，看看热重载是否生效
2. 学习 Dockerfile 的编写
3. 了解 docker-compose 的更多功能
4. 探索生产环境部署

---

> 💡 **小贴士**：遇到问题不要慌，大部分问题都可以通过重启 Docker Desktop 或重新构建镜像解决！

---

*最后更新：2024年*
*作者：Next Wallpaper Team*