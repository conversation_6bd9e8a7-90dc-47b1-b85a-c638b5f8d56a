# 🐳 Docker 部署完整指南

> **文档定位**：完整的Docker部署指南，涵盖所有部署场景和详细配置
> 
> **相关文档**：
> - `DOCKER_README.md` - Docker基础使用快速参考（同目录）
> - `../DOCKER_部署文件整理与使用指南.md` - 文件结构和清理指南

本指南涵盖本地开发和生产部署（包括 1Panel）的所有 Docker 相关配置。

## 📚 目录

- [快速开始](#快速开始)
- [本地开发](#本地开发)
- [生产部署](#生产部署)
- [1Panel 部署](#1panel-部署)
- [架构说明](#架构说明)
- [故障排查](#故障排查)
- [最佳实践](#最佳实践)

---

## 🚀 快速开始

### 选择你的部署方式

| 部署方式 | 适用场景 | 配置文件 | 启动命令 |
|---------|---------|---------|---------|
| **本地开发** | 本地开发，热重载 | `docker-compose.local.yml` | `./scripts/local-start.sh` |
| **生产部署** | 独立服务器部署 | `docker/1panel/docker-compose.simple.yml` | `docker-compose up -d` |
| **1Panel 部署** | 使用 1Panel 管理 | `docker/1panel/` 目录 | 通过 1Panel 界面 |

### 前置要求

- Docker >= 20.10
- Docker Compose >= 2.0
- Node.js 20.18.3（仅本地开发需要）

---

## 💻 本地开发

### 一键启动

```bash
# 最简单的方式
./scripts/local-start.sh

# 如果在国内，使用镜像加速
./scripts/local-start.sh --china-mirror

# 强制重新构建
./scripts/local-start.sh --rebuild
```

### 手动启动

```bash
# 1. 复制环境变量文件
cp .env.docker .env.local

# 2. 构建并启动
docker-compose -f docker-compose.local.yml up --build

# 3. 后台运行
docker-compose -f docker-compose.local.yml up -d
```

### 开发环境特性

- ✅ **热重载**：代码修改自动更新
- ✅ **源码挂载**：直接编辑本地文件
- ✅ **调试支持**：可配置 Node.js 调试端口
- ✅ **日志输出**：实时查看应用日志

### 常用命令

```bash
# 查看日志
docker-compose -f docker-compose.local.yml logs -f

# 进入容器
docker exec -it next-wallpaper-local sh

# 停止服务
docker-compose -f docker-compose.local.yml down

# 清理所有
docker-compose -f docker-compose.local.yml down -v
```

---

## 🏭 生产部署

### 方式一：独立服务器部署

#### 1. 准备环境变量

```bash
# 复制并编辑生产环境配置
cp .env.docker .env.production
vi .env.production

# 修改以下配置
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
SESSION_SECRET=your-strong-secret-key
```

#### 2. 构建生产镜像

```bash
# 使用优化的 1Panel Dockerfile（推荐）
docker build -f docker/1panel/Dockerfile.1panel -t your-app:latest .

# 或使用标准 Dockerfile
docker build --target runner -t your-app:latest .
```

#### 3. 运行容器

```bash
# 使用 docker-compose
docker-compose -f docker/1panel/docker-compose.simple.yml up -d

# 或直接使用 docker run
docker run -d \
  --name your-app \
  -p 3000:3000 \
  -e NODE_ENV=production \
  --restart unless-stopped \
  your-app:latest
```

### 方式二：使用 Docker Hub

```bash
# 1. 构建并推送镜像
docker build -f docker/1panel/Dockerfile.1panel -t username/your-app:latest .
docker push username/your-app:latest

# 2. 在服务器上拉取并运行
docker pull username/your-app:latest
docker run -d --name your-app -p 3000:3000 username/your-app:latest
```

---

## 🎯 1Panel 部署

### 完整部署流程

#### 步骤 1：服务器准备

```bash
# SSH 登录服务器
ssh root@your-server-ip

# 克隆项目
git clone https://github.com/your-username/next-wallpaper.git
cd next-wallpaper

# 构建镜像（避免架构问题）
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest .
```

#### 步骤 2：1Panel 配置

1. **创建编排项目**
   - 登录 1Panel → 容器 → 编排
   - 点击「创建编排」
   - 选择「编排」（手动输入）

2. **粘贴配置**（使用简化版）
   ```yaml
   version: '3'
   services:
     mockpix:
       image: mockpix:latest
       container_name: mockpix-production
       ports:
         - "3000:3000"
       environment:
         - NODE_ENV=production
         - PORT=3000
         - HOSTNAME=0.0.0.0
         - TZ=Asia/Shanghai
       env_file:
         - 1panel.env
       restart: unless-stopped
   ```

3. **配置环境变量**
   ```
   APP_URL=https://your-domain.com
   DOMAIN=your-domain.com
   SESSION_SECRET=your-secret-key
   ```

#### 步骤 3：创建反向代理网站

1. **创建网站**
   - 1Panel → 网站 → 创建网站
   - 类型：反向代理
   - 主域名：your-domain.com
   - 代理地址：http://127.0.0.1:3000

2. **配置 HTTPS**
   - 申请 Let's Encrypt 证书
   - 开启 HTTP 自动跳转

### 详细文档

更多 1Panel 部署细节，请查看：
- [快速部署指南](docker/1panel/快速部署指南.md)
- [1Panel 网站配置指南](docker/1panel/1Panel网站配置指南.md)
- [部署成功验证清单](docker/1panel/部署成功验证清单.md)

---

## 🏗️ 架构说明

### Docker 镜像架构

我们提供两个优化的 Dockerfile：

| 文件 | 用途 | 特点 | 镜像大小 |
|-----|------|------|---------|
| `Dockerfile` | 通用构建 | 多阶段构建，支持开发和生产 | ~150MB（生产） |
| `docker/1panel/Dockerfile.1panel` | 1Panel 优化 | standalone 模式，国内镜像加速 | ~311MB |

### 架构兼容性

#### Mac 开发环境（Apple Silicon）

```bash
# 构建 AMD64 架构镜像（用于部署到 x86 服务器）
docker buildx build --platform linux/amd64 \
  -f docker/1panel/Dockerfile.1panel \
  -t your-app:latest .
```

#### 避免架构问题的最佳实践

1. **在目标服务器上构建**（推荐）
2. **使用 Docker Hub 多架构镜像**
3. **使用 buildx 构建多平台镜像**

详见：[架构兼容性说明](docker/1panel/架构兼容性说明.md)

---

## 🔧 故障排查

### 常见问题

#### 1. 端口被占用

```bash
# 查看占用端口的进程
lsof -i :3000

# 停止占用的容器
docker stop $(docker ps -q --filter "publish=3000")
```

#### 2. 热重载不工作（开发环境）

确保环境变量配置：
```yaml
environment:
  WATCHPACK_POLLING: true
  CHOKIDAR_USEPOLLING: true
```

#### 3. 构建失败

```bash
# 清理并重新构建
docker system prune -a
docker-compose build --no-cache
```

#### 4. exec format error

这是架构不匹配问题，解决方案：
- 在服务器上直接构建镜像
- 使用 buildx 构建正确架构

#### 5. 网络超时（国内）

```bash
# 启用镜像加速
./scripts/local-start.sh --china-mirror

# 或手动配置 Docker 镜像加速
./scripts/setup-docker-mirror.sh
```

### 日志查看

```bash
# 实时日志
docker logs -f container-name

# 最近 100 行
docker logs --tail=100 container-name

# 带时间戳
docker logs -t container-name
```

---

## 📋 最佳实践

### 1. 环境变量管理

- ✅ 使用 `.env.docker` 作为模板
- ✅ 不同环境使用不同的 `.env` 文件
- ❌ 不要提交包含敏感信息的 `.env` 文件

### 2. 镜像优化

- ✅ 使用多阶段构建
- ✅ 使用 Alpine Linux 基础镜像
- ✅ 清理不必要的文件和缓存
- ✅ 使用 `.dockerignore` 排除不需要的文件

### 3. 安全建议

- ✅ 使用非 root 用户运行容器
- ✅ 定期更新基础镜像
- ✅ 扫描镜像漏洞
- ✅ 使用强密码和密钥

### 4. 性能优化

- ✅ 配置合适的资源限制
- ✅ 使用健康检查
- ✅ 配置日志轮转
- ✅ 使用 CDN 加速静态资源

### 5. 部署流程

```mermaid
graph LR
    A[本地开发] --> B[构建镜像]
    B --> C{部署方式}
    C -->|独立服务器| D[Docker Compose]
    C -->|1Panel| E[1Panel 编排]
    C -->|K8s| F[Kubernetes]
    D --> G[配置反向代理]
    E --> G
    F --> G
    G --> H[HTTPS/域名]
    H --> I[生产环境]
```

---

## 📊 资源配置建议

| 环境 | CPU | 内存 | 说明 |
|-----|-----|------|------|
| 开发 | 2核 | 2GB | 支持热重载和调试 |
| 测试 | 1核 | 1GB | 基础功能测试 |
| 生产（小型） | 1核 | 512MB | < 100 用户 |
| 生产（中型） | 2核 | 1GB | 100-1000 用户 |
| 生产（大型） | 4核+ | 2GB+ | > 1000 用户 |

---

## 🔄 更新部署

```bash
# 1. 拉取最新代码
git pull

# 2. 重新构建镜像
docker build -f docker/1panel/Dockerfile.1panel -t your-app:v2 .

# 3. 停止旧容器
docker stop your-app && docker rm your-app

# 4. 启动新容器
docker run -d --name your-app -p 3000:3000 your-app:v2
```

---

## 📖 相关文档

- [Docker 基础使用指南](DOCKER_README.md)
- [1Panel 快速部署指南](docker/1panel/快速部署指南.md)
- [架构兼容性说明](docker/1panel/架构兼容性说明.md)
- [环境变量配置](.env.docker)

---

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

如有问题，请查看 [GitHub Issues](https://github.com/gcordon/next_wallpaper/issues)

---

*最后更新：2025-08-18*