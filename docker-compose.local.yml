# =============================================================================
# Docker Compose 本地开发配置（简化版）
# =============================================================================
# 用途：本地快速开发，支持热重载
# 特性：
# - 简化配置，易于理解和维护
# - 支持源代码挂载和热重载
# - 与 1Panel 部署配置保持一致的结构
# =============================================================================

version: '3'

services:
  # ---------------------------------------------------------------------------
  # Next.js 本地开发服务
  # ---------------------------------------------------------------------------
  app:
    # 容器名称
    container_name: next-wallpaper-local
    
    # 构建配置
    build:
      context: .
      dockerfile: Dockerfile
      target: development  # 使用开发阶段
    
    # 镜像标签
    image: next-wallpaper:local
    
    # ---------------------------------------------------------------------------
    # 端口映射
    # ---------------------------------------------------------------------------
    ports:
      - "3000:3000"      # Next.js 开发服务器
      # - "9229:9229"    # Node.js 调试端口（需要时取消注释）
    
    # ---------------------------------------------------------------------------
    # 环境变量
    # ---------------------------------------------------------------------------
    environment:
      # Node.js 环境
      NODE_ENV: development
      
      # Next.js 配置
      NEXT_TELEMETRY_DISABLED: 1
      PORT: 3000
      HOSTNAME: "0.0.0.0"
      
      # 开发环境特定
      WATCHPACK_POLLING: true        # Docker 中启用文件监视
      CHOKIDAR_USEPOLLING: true     # 备用文件监视
      
      # 时区
      TZ: Asia/Shanghai
    
    # 从文件加载环境变量
    env_file:
      - .env.local                   # 本地环境变量（如果存在）
      - .env.docker                  # Docker 特定环境变量（如果存在）
    
    # ---------------------------------------------------------------------------
    # 文件挂载（实现热重载）
    # ---------------------------------------------------------------------------
    volumes:
      # 源代码目录
      - ./app:/app/app:delegated
      - ./public:/app/public:delegated
      
      # 配置文件
      - ./next.config.ts:/app/next.config.ts:delegated
      - ./tsconfig.json:/app/tsconfig.json:delegated
      - ./tailwind.config.ts:/app/tailwind.config.ts:delegated
      - ./postcss.config.mjs:/app/postcss.config.mjs:delegated
      
      # 包管理文件（只读）
      - ./package.json:/app/package.json:ro
      - ./pnpm-lock.yaml:/app/pnpm-lock.yaml:ro
      
      # 使用容器内的 node_modules 和构建缓存
      - /app/node_modules
      - /app/.next
      - /app/.turbo
    
    # ---------------------------------------------------------------------------
    # 其他配置
    # ---------------------------------------------------------------------------
    restart: unless-stopped
    stdin_open: true        # 保持 STDIN 开启
    tty: true              # 分配伪终端
    
    # 健康检查
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# =============================================================================
# 使用说明
# =============================================================================
# 1. 启动开发环境：
#    docker-compose -f docker-compose.local.yml up
#
# 2. 后台运行：
#    docker-compose -f docker-compose.local.yml up -d
#
# 3. 查看日志：
#    docker-compose -f docker-compose.local.yml logs -f
#
# 4. 重新构建：
#    docker-compose -f docker-compose.local.yml up --build
#
# 5. 停止服务：
#    docker-compose -f docker-compose.local.yml down
#
# 6. 进入容器：
#    docker exec -it next-wallpaper-local sh
# =============================================================================