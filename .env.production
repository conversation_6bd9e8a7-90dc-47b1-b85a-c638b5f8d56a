# Next.js 环境变量示例文件
# 复制此文件为 .env.local (开发) 或 .env.production (生产) 并填写实际值

# ===== 应用配置 =====
# Node 环境 (development | production)
NODE_ENV=production

# 应用端口
PORT=3000

# 应用 URL
NEXT_PUBLIC_APP_URL=https://your-domain.com

# ===== Next.js 配置 =====
# 禁用遥测数据收集
NEXT_TELEMETRY_DISABLED=1

# ===== API 配置 =====
# API 基础 URL
# NEXT_PUBLIC_API_BASE_URL=

# API 密钥（如果需要）
# API_SECRET_KEY=

# ===== 数据库配置（如果需要）=====
# DATABASE_URL=

# ===== 认证配置（如果需要）=====
# NEXTAUTH_URL=http://localhost:3000
# NEXTAUTH_SECRET=

# ===== 第三方服务（如果需要）=====
# 分析服务
# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=

# 图片存储服务
# NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=
# CLOUDINARY_API_KEY=
# CLOUDINARY_API_SECRET=

# ===== 开发工具配置 =====
# 启用 React 严格模式
# NEXT_PUBLIC_STRICT_MODE=true

# ===== Docker 特定配置 =====
# 监视文件变化（Docker 开发环境）
WATCHPACK_POLLING=true

# ===== 自定义配置 =====
# 在这里添加你的自定义环境变量

# 会话密钥（生产环境必须设置）
SESSION_SECRET=your-secret-key-change-this-in-production