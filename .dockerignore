# 依赖目录
node_modules
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# 构建输出
.next/
out/
build/
dist/

# 版本控制
.git/
.gitignore

# 环境变量
.env
.env.local
.env.production.local
.env.development.local
.env.test.local

# IDE 配置
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
logs/
*.log

# 测试覆盖率
coverage/
.nyc_output/

# TypeScript
*.tsbuildinfo

# 临时文件
tmp/
temp/

# Docker 文件（避免递归复制）
Dockerfile
docker-compose*.yml
.dockerignore

# 文档和辅助文件
*.md
optimizeReconstructMarkdown/
辅助函数/
__MARKDOWN_MD__/
doc.md

# 测试文件
test-*.ts
*.test.ts
*.spec.ts

# 其他
.vercel/
.turbo/

# Docker 构建产物
mockpix.tar.gz
mockpix-*.tar.gz
*.tar.gz

# 1Panel 部署文档
docker/docs/
docker/1panel/*.md
!docker/1panel/README.md

# 归档文件
docker/docs/archive/