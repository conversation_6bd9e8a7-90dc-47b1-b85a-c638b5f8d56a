{"name": "next_wallpaper", "version": "0.1.0", "private": true, "//": "=== 脚本说明 ===", "scripts": {"//dev-scripts": "=== 开发环境 ===", "dev": "next dev --turbopack --hostname 0.0.0.0", "build": "next build", "start": "next start", "//lint-scripts": "=== 代码质量 ===", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "//docker-scripts": "=== Docker 部署 ===", "docker:dev": "./scripts/local-start.sh", "docker:dev:rebuild": "./scripts/local-start.sh --rebuild", "docker:dev:china": "./scripts/local-start.sh --china-mirror", "docker:prod": "./scripts/prod.sh", "docker:1panel": "./scripts/1panel-deploy.sh", "//docker-manage": "=== Docker 管理 ===", "docker:stop": "docker-compose -f docker-compose.local.yml down", "docker:logs": "docker-compose -f docker-compose.local.yml logs -f", "docker:clean": "docker system prune -a"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@react-spring/web": "^9.7.5", "chroma-js": "^3.1.2", "colorthief": "^2.6.0", "copy-image-clipboard": "^2.1.2", "decimal.js": "^10.6.0", "dom-to-image": "^2.6.0", "html-to-image": "^1.11.13", "lucide-react": "^0.503.0", "next": "15.3.1", "p-limit": "^6.2.0", "pica": "^9.0.1", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.0", "react-icons": "^5.5.0", "react-resize-detector": "^12.0.2", "react-responsive": "^10.0.1", "react-use": "^17.6.0", "sonner": "^2.0.6", "vconsole": "^3.15.1", "weixin-js-sdk": "^1.6.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/chroma-js": "^3.1.1", "@types/decimal.js": "^7.4.3", "@types/node": "^20", "@types/pica": "^9.0.5", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "sass": "^1.87.0", "tailwindcss": "^4", "typescript": "^5"}}