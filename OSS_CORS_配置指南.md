# OSS CORS 配置指南

## 🔍 问题分析

您遇到的错误是典型的CORS（跨源资源共享）问题：
```
Access to fetch at 'https://mockpix-oss-bucket.oss-cn-guangzhou.aliyuncs.com/...' 
from origin 'http://localhost:3000' has been blocked by CORS policy
```

## 🛠️ 解决方案

### 方案1：配置OSS Bucket CORS规则（推荐）

#### 步骤1：登录阿里云OSS控制台
1. 访问：https://oss.console.aliyun.com/
2. 使用您的账号：`<EMAIL>` 登录

#### 步骤2：进入Bucket设置
1. 找到并点击存储空间：`mockpix-oss-bucket`
2. 在左侧菜单栏找到："权限管理" > "跨域设置"

#### 步骤3：添加CORS规则
点击"设置"，添加以下规则：

```json
{
  "来源(AllowedOrigins)": [
    "http://localhost:3000",
    "https://localhost:3000",
    "http://127.0.0.1:3000",
    "https://127.0.0.1:3000"
  ],
  "允许Methods": [
    "GET",
    "PUT",
    "POST",
    "DELETE",
    "HEAD"
  ],
  "允许Headers": "*",
  "暴露Headers": [
    "ETag",
    "x-oss-request-id",
    "x-oss-hash-sha1",
    "x-oss-hash-sha256"
  ],
  "缓存时间(MaxAgeSeconds)": 3600
}
```

#### 步骤4：保存配置
点击"确定"保存CORS规则。

### 方案2：临时修改Bucket权限（仅用于测试）

如果您想快速测试功能：

1. 在OSS控制台进入 `mockpix-oss-bucket`
2. 点击"概览" > "基础设置" > "读写权限"
3. **临时**改为"公共读写"
4. **测试完成后记得改回"私有"**

## 🔒 关于STS Token警告

OSS SDK建议使用STS临时凭证而不是永久AccessKey，这是安全最佳实践。

### 当前使用方式（不推荐用于生产环境）
```javascript
// 直接使用永久AccessKey（仅适用于开发测试）
accessKeyId: 'LTAI5tNr92DFCjZDm1Wt2juy'
accessKeySecret: '******************************'
```

### 生产环境推荐方式
```javascript
// 使用STS临时凭证（生产环境推荐）
accessKeyId: 'STS-临时AccessKey'
accessKeySecret: 'STS-临时Secret'
stsToken: 'STS-Token'
```

## 📋 测试步骤

1. **配置CORS规则**（按照上面的步骤）
2. **等待配置生效**（通常几分钟内）
3. **刷新网页重新测试**
4. **检查浏览器开发者工具Network选项卡**，确认请求成功

## 🆘 如果仍然有问题

1. **检查网络**：确认能正常访问阿里云服务
2. **检查区域**：确认region配置正确（oss-cn-guangzhou）
3. **检查权限**：确认AccessKey有OSS操作权限
4. **联系我**：提供完整的错误信息

## 🔍 验证CORS配置是否生效

配置完成后，可以在浏览器开发者工具中检查：
1. 打开Network选项卡
2. 尝试上传
3. 查看OSS请求的Response Headers是否包含：
   - `Access-Control-Allow-Origin: http://localhost:3000`
   - `Access-Control-Allow-Methods: GET,PUT,POST,DELETE,HEAD`

---

**注意**：CORS配置可能需要几分钟才能生效，请耐心等待。