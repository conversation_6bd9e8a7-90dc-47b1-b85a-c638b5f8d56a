# =============================================================================
# Docker Compose 基础配置文件
# =============================================================================
# 用途：定义共享的服务配置，可被其他环境继承
# 使用方法：
# - 开发环境: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# - 生产环境: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
# =============================================================================

# -----------------------------------------------------------------------------
# 通用服务定义
# -----------------------------------------------------------------------------
services:
  # Next.js 应用基础配置
  app:
    # 基础镜像配置
    image: next-wallpaper:${TAG:-latest}
    
    # 容器重启策略
    restart: unless-stopped
    
    # 基础端口映射
    ports:
      - "${PORT:-3000}:3000"
    
    # ---------------------------------------------------------------------------
    # 通用环境变量
    # ---------------------------------------------------------------------------
    environment:
      # Next.js 核心配置
      NEXT_TELEMETRY_DISABLED: 1
      PORT: ${PORT:-3000}
      
      # Node.js 配置
      TZ: ${TZ:-Asia/Shanghai}
      
    # ---------------------------------------------------------------------------
    # 基础健康检查
    # ---------------------------------------------------------------------------
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000', (res) => process.exit(res.statusCode === 200 ? 0 : 1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # ---------------------------------------------------------------------------
    # 基础日志配置
    # ---------------------------------------------------------------------------
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # ---------------------------------------------------------------------------
    # 网络配置
    # ---------------------------------------------------------------------------
    networks:
      - app-network

# -----------------------------------------------------------------------------
# 网络定义
# -----------------------------------------------------------------------------
networks:
  app-network:
    driver: bridge

# -----------------------------------------------------------------------------
# 卷定义（可选）
# -----------------------------------------------------------------------------
volumes:
  # Node modules 卷（用于开发环境缓存）
  node_modules:
    driver: local
  
  # 构建缓存卷
  build_cache:
    driver: local
  
  # 日志卷
  logs:
    driver: local

# =============================================================================
# 配置说明
# =============================================================================
# 环境变量：
# - TAG: Docker 镜像标签（默认: latest）
# - PORT: 应用端口（默认: 3000）
# - TZ: 时区设置（默认: Asia/Shanghai）
#
# 使用示例：
# 1. 设置环境变量：
#    export TAG=v1.0.0
#    export PORT=8080
#
# 2. 使用特定配置启动：
#    docker-compose up                      # 使用默认配置
#    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up  # 开发环境
#    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up # 生产环境
# =============================================================================