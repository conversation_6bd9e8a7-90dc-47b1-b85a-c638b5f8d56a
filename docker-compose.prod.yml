# =============================================================================
# Docker Compose 生产环境配置
# =============================================================================
# 用途：生产环境部署，优化资源使用和安全性
# 特性：
# - 使用优化的生产镜像
# - 严格的资源限制
# - 健康检查和自动重启
# - 日志管理
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # Next.js 生产服务
  # ---------------------------------------------------------------------------
  app:
    # 构建配置
    build:
      context: .
      dockerfile: Dockerfile
      target: runner  # 使用生产阶段
    
    # 容器配置
    container_name: next-wallpaper-prod
    image: next-wallpaper:prod
    
    # ---------------------------------------------------------------------------
    # 端口映射
    # ---------------------------------------------------------------------------
    ports:
      - "${PORT:-3000}:3000"
    
    # ---------------------------------------------------------------------------
    # 环境变量
    # ---------------------------------------------------------------------------
    environment:
      # Node.js 环境
      NODE_ENV: production
      
      # Next.js 配置
      NEXT_TELEMETRY_DISABLED: 1
      PORT: ${PORT:-3000}
      HOSTNAME: "0.0.0.0"
      
      # 应用配置
      NEXT_PUBLIC_APP_URL: ${APP_URL:-http://localhost:3000}
      
      # 安全配置
      SESSION_SECRET: ${SESSION_SECRET}
      
      # 时区
      TZ: ${TZ:-Asia/Shanghai}
    
    # 从文件加载环境变量
    env_file:
      - .env.production
    
    # ---------------------------------------------------------------------------
    # 健康检查
    # ---------------------------------------------------------------------------
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000', (res) => process.exit(res.statusCode === 200 ? 0 : 1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # ---------------------------------------------------------------------------
    # 资源限制
    # ---------------------------------------------------------------------------
    deploy:
      resources:
        limits:
          cpus: '${CPU_LIMIT:-1}'          # CPU 限制
          memory: ${MEMORY_LIMIT:-512M}    # 内存限制
        reservations:
          cpus: '${CPU_RESERVE:-0.25}'     # CPU 预留
          memory: ${MEMORY_RESERVE:-256M}  # 内存预留
    
    # ---------------------------------------------------------------------------
    # 其他配置
    # ---------------------------------------------------------------------------
    restart: unless-stopped
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "${LOG_MAX_SIZE:-10m}"
        max-file: "${LOG_MAX_FILES:-5}"
        labels: "service=next-wallpaper,env=production"
    
    # 网络配置
    networks:
      - app-network

# =============================================================================
# 网络定义
# =============================================================================
networks:
  app-network:
    driver: bridge
    name: next-wallpaper-prod-network

# =============================================================================
# 使用说明
# =============================================================================
# 1. 准备环境变量：
#    cp .env.example .env.production
#    vi .env.production
#
# 2. 构建并启动：
#    docker-compose -f docker-compose.prod.yml up --build -d
#
# 3. 查看日志：
#    docker-compose -f docker-compose.prod.yml logs -f
#
# 4. 停止服务：
#    docker-compose -f docker-compose.prod.yml down
#
# 5. 更新部署：
#    docker-compose -f docker-compose.prod.yml pull
#    docker-compose -f docker-compose.prod.yml up -d
# =============================================================================