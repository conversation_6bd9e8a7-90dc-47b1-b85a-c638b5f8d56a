#!/bin/bash

# =============================================================================
# 1Panel 部署脚本
# =============================================================================
# 功能：
# 1. 构建 Docker 镜像
# 2. 打包应用文件
# 3. 生成 1Panel 应用包
# 4. 可选：推送到镜像仓库
# =============================================================================

set -e  # 遇到错误立即退出

# -----------------------------------------------------------------------------
# 配置变量
# -----------------------------------------------------------------------------
# 项目路径
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DOCKER_DIR="${PROJECT_ROOT}/docker/1panel"
OUTPUT_DIR="${PROJECT_ROOT}/dist/1panel"

# 应用信息
APP_NAME="next-wallpaper"
APP_VERSION="1.0.0"
IMAGE_NAME="${APP_NAME}:1panel"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 工具函数
# -----------------------------------------------------------------------------
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# -----------------------------------------------------------------------------
# 检查依赖
# -----------------------------------------------------------------------------
check_requirements() {
    print_step "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "${DOCKER_DIR}/Dockerfile.1panel" ]; then
        print_error "找不到 Dockerfile.1panel"
        exit 1
    fi
    
    if [ ! -f "${DOCKER_DIR}/docker-compose.yml" ]; then
        print_error "找不到 docker-compose.yml"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# -----------------------------------------------------------------------------
# 构建镜像
# -----------------------------------------------------------------------------
build_image() {
    print_step "构建 Docker 镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    docker build \
        -f "${DOCKER_DIR}/Dockerfile.1panel" \
        -t "$IMAGE_NAME" \
        --build-arg APP_VERSION="$APP_VERSION" \
        . || {
        print_error "镜像构建失败"
        exit 1
    }
    
    # 显示镜像信息
    docker images | grep "$APP_NAME"
    
    print_success "镜像构建成功: $IMAGE_NAME"
}

# -----------------------------------------------------------------------------
# 导出镜像（可选）
# -----------------------------------------------------------------------------
export_image() {
    print_step "导出 Docker 镜像..."
    
    local image_file="${OUTPUT_DIR}/${APP_NAME}-${APP_VERSION}.tar"
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 导出镜像
    docker save "$IMAGE_NAME" -o "$image_file"
    
    # 压缩镜像
    gzip -f "$image_file"
    
    local compressed_file="${image_file}.gz"
    local file_size=$(ls -lh "$compressed_file" | awk '{print $5}')
    
    print_success "镜像导出成功: $compressed_file (大小: $file_size)"
}

# -----------------------------------------------------------------------------
# 创建应用包
# -----------------------------------------------------------------------------
create_app_package() {
    print_step "创建 1Panel 应用包..."
    
    # 创建临时目录
    local temp_dir="${OUTPUT_DIR}/temp"
    rm -rf "$temp_dir"
    mkdir -p "$temp_dir"
    
    # 复制必要文件
    cp "${DOCKER_DIR}/docker-compose.yml" "$temp_dir/"
    cp "${DOCKER_DIR}/app.json" "$temp_dir/"
    cp "${DOCKER_DIR}/.env.example" "$temp_dir/.env"
    cp "${DOCKER_DIR}/install.sh" "$temp_dir/" 2>/dev/null || true
    cp "${DOCKER_DIR}/uninstall.sh" "$temp_dir/" 2>/dev/null || true
    cp "${DOCKER_DIR}/logo.png" "$temp_dir/" 2>/dev/null || true
    
    # 创建 data 目录
    mkdir -p "$temp_dir/data/logs"
    mkdir -p "$temp_dir/data/uploads"
    
    # 创建 README
    cat > "$temp_dir/README.md" << EOF
# Next Wallpaper for 1Panel

## 安装说明
1. 将此目录上传到 1Panel 应用目录
2. 在 1Panel 应用商店中安装
3. 配置环境变量
4. 启动应用

## 版本信息
- 版本: $APP_VERSION
- 构建时间: $(date '+%Y-%m-%d %H:%M:%S')
- 镜像: $IMAGE_NAME

## 访问地址
安装后访问: http://your-server:3000
EOF
    
    # 打包应用
    local package_name="${APP_NAME}-1panel-${APP_VERSION}.tar.gz"
    cd "$temp_dir"
    tar -czf "${OUTPUT_DIR}/${package_name}" .
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    print_success "应用包创建成功: ${OUTPUT_DIR}/${package_name}"
}

# -----------------------------------------------------------------------------
# 推送到镜像仓库（可选）
# -----------------------------------------------------------------------------
push_image() {
    local registry="$1"
    
    if [ -z "$registry" ]; then
        print_warning "未指定镜像仓库，跳过推送"
        return
    fi
    
    print_step "推送镜像到仓库: $registry"
    
    # 标记镜像
    local remote_image="${registry}/${IMAGE_NAME}"
    docker tag "$IMAGE_NAME" "$remote_image"
    
    # 推送镜像
    docker push "$remote_image" || {
        print_error "镜像推送失败"
        return 1
    }
    
    print_success "镜像推送成功: $remote_image"
}

# -----------------------------------------------------------------------------
# 显示部署信息
# -----------------------------------------------------------------------------
show_deployment_info() {
    echo
    echo "========================================"
    echo "       1Panel 部署准备完成"
    echo "========================================"
    echo
    echo "镜像名称: $IMAGE_NAME"
    echo "应用版本: $APP_VERSION"
    echo "输出目录: $OUTPUT_DIR"
    echo
    echo "下一步操作:"
    echo "1. 将 ${OUTPUT_DIR} 目录下的文件上传到 1Panel 服务器"
    echo "2. 在 1Panel 应用商店中导入应用"
    echo "3. 配置环境变量并启动"
    echo
    echo "或者直接使用镜像:"
    echo "docker run -p 3000:3000 $IMAGE_NAME"
    echo
    echo "========================================"
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    print_step "开始 1Panel 部署流程..."
    echo
    
    # 解析参数
    local do_export=false
    local do_push=false
    local registry=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --export|-e)
                do_export=true
                shift
                ;;
            --push|-p)
                do_push=true
                registry="$2"
                shift 2
                ;;
            --version|-v)
                APP_VERSION="$2"
                IMAGE_NAME="${APP_NAME}:${APP_VERSION}"
                shift 2
                ;;
            --help|-h)
                echo "使用方法: $0 [选项]"
                echo "选项:"
                echo "  -e, --export         导出镜像文件"
                echo "  -p, --push <registry> 推送到镜像仓库"
                echo "  -v, --version <ver>  指定版本号"
                echo "  -h, --help           显示帮助"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署流程
    check_requirements
    build_image
    
    if [ "$do_export" = true ]; then
        export_image
    fi
    
    create_app_package
    
    if [ "$do_push" = true ]; then
        push_image "$registry"
    fi
    
    show_deployment_info
    
    print_success "部署准备完成！"
}

# -----------------------------------------------------------------------------
# 脚本入口
# -----------------------------------------------------------------------------
main "$@"