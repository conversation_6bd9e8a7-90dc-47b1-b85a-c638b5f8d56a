#!/bin/bash

# =============================================================================
# Docker 生产环境启动脚本
# =============================================================================
# 用途：部署和管理 Docker 生产环境
# 特性：
# - 生产级别的安全检查
# - 自动备份配置
# - 零停机部署支持
# - 健康检查和回滚机制
# =============================================================================

set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时报错

# -----------------------------------------------------------------------------
# 颜色定义
# -----------------------------------------------------------------------------
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 配置变量
# -----------------------------------------------------------------------------
COMPOSE_FILE="docker-compose.prod.yml"
CONTAINER_NAME="next-wallpaper-prod"
IMAGE_NAME="next-wallpaper:latest"
PORT="3000"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${PROJECT_ROOT}/backups"
MAX_BACKUPS=5

# -----------------------------------------------------------------------------
# 工具函数
# -----------------------------------------------------------------------------
print_message() {
    echo -e "${BLUE}[PROD]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_step() {
    echo -e "${MAGENTA}==>${NC} $1"
}

# -----------------------------------------------------------------------------
# 环境检查
# -----------------------------------------------------------------------------
check_requirements() {
    print_step "检查系统要求..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        print_info "请访问 https://docs.docker.com/get-docker/ 安装 Docker"
        exit 1
    fi
    
    # 检查 Docker 守护进程
    if ! docker info &> /dev/null; then
        print_error "Docker 守护进程未运行"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查可用磁盘空间（至少需要 2GB）
    available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then
        print_warning "磁盘空间不足（建议至少 2GB）"
    fi
    
    print_success "✓ 系统检查通过"
}

# -----------------------------------------------------------------------------
# 环境准备
# -----------------------------------------------------------------------------
prepare_environment() {
    print_step "准备生产环境..."
    
    cd "$PROJECT_ROOT"
    
    # 检查生产环境配置文件
    if [ ! -f .env.production ]; then
        if [ -f .env.example ]; then
            print_warning "未找到 .env.production，从 .env.example 创建"
            cp .env.example .env.production
            print_info "请编辑 .env.production 配置生产环境变量"
            read -p "是否继续？(y/n) " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        else
            print_error "未找到 .env.production 文件"
            exit 1
        fi
    fi
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    print_success "✓ 环境准备完成"
}

# -----------------------------------------------------------------------------
# 备份管理
# -----------------------------------------------------------------------------
create_backup() {
    print_step "创建备份..."
    
    local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
    local backup_path="${BACKUP_DIR}/${backup_name}"
    
    # 备份当前镜像
    if docker images | grep -q "$IMAGE_NAME"; then
        print_info "备份当前镜像..."
        docker save "$IMAGE_NAME" | gzip > "${backup_path}.tar.gz"
        print_success "✓ 镜像备份完成: ${backup_name}.tar.gz"
    fi
    
    # 清理旧备份
    local backup_count=$(ls -1 "$BACKUP_DIR" | wc -l)
    if [ "$backup_count" -gt "$MAX_BACKUPS" ]; then
        print_info "清理旧备份..."
        ls -1t "$BACKUP_DIR" | tail -n +$((MAX_BACKUPS + 1)) | xargs -I {} rm -f "${BACKUP_DIR}/{}"
    fi
}

# -----------------------------------------------------------------------------
# 容器管理
# -----------------------------------------------------------------------------
stop_existing_container() {
    print_step "检查现有容器..."
    
    if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_warning "发现运行中的生产容器"
        read -p "是否停止现有容器？(y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "优雅停止容器..."
            docker-compose -f "$COMPOSE_FILE" stop --timeout 30
            docker-compose -f "$COMPOSE_FILE" down
            print_success "✓ 容器已停止"
        else
            print_error "部署已取消"
            exit 1
        fi
    fi
}

build_production_image() {
    print_step "构建生产镜像..."
    
    # 清理构建缓存（可选）
    if [ "${CLEAN_BUILD:-false}" = "true" ]; then
        print_info "清理 Docker 构建缓存..."
        docker builder prune -f
    fi
    
    print_info "开始构建生产镜像..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache --pull || {
        print_error "镜像构建失败"
        exit 1
    }
    
    # 标记镜像版本
    local version_tag="v$(date +%Y%m%d.%H%M%S)"
    docker tag "$IMAGE_NAME" "${IMAGE_NAME%:*}:${version_tag}"
    
    print_success "✓ 生产镜像构建成功"
    print_info "镜像版本: ${version_tag}"
}

deploy_container() {
    print_step "部署生产容器..."
    
    docker-compose -f "$COMPOSE_FILE" up -d || {
        print_error "容器部署失败"
        docker-compose -f "$COMPOSE_FILE" logs --tail=50
        exit 1
    }
    
    print_success "✓ 容器部署成功"
}

# -----------------------------------------------------------------------------
# 健康检查
# -----------------------------------------------------------------------------
health_check() {
    print_step "执行健康检查..."
    
    local max_attempts=60  # 最多等待 2 分钟
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker inspect --format='{{.State.Health.Status}}' "$CONTAINER_NAME" 2>/dev/null | grep -q "healthy"; then
            print_success "✓ 健康检查通过"
            
            # 额外的应用级别检查
            if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$PORT" | grep -q "200"; then
                print_success "✓ 应用响应正常"
                return 0
            fi
        fi
        
        attempt=$((attempt + 1))
        printf "."
        sleep 2
    done
    
    print_error "健康检查失败"
    print_info "查看日志: docker-compose -f $COMPOSE_FILE logs --tail=100"
    return 1
}

# -----------------------------------------------------------------------------
# 回滚机制
# -----------------------------------------------------------------------------
rollback() {
    print_error "部署失败，开始回滚..."
    
    # 停止当前容器
    docker-compose -f "$COMPOSE_FILE" down
    
    # 查找最新的备份
    local latest_backup=$(ls -1t "$BACKUP_DIR" | head -n 1)
    if [ -n "$latest_backup" ]; then
        print_info "恢复备份: $latest_backup"
        gunzip -c "${BACKUP_DIR}/${latest_backup}" | docker load
        docker-compose -f "$COMPOSE_FILE" up -d
        print_success "✓ 回滚完成"
    else
        print_error "没有可用的备份"
        exit 1
    fi
}

# -----------------------------------------------------------------------------
# 显示成功信息
# -----------------------------------------------------------------------------
show_success_info() {
    echo
    echo -e "${GREEN}============================================================${NC}"
    echo -e "${GREEN}       🚀 生产环境部署成功！${NC}"
    echo -e "${GREEN}============================================================${NC}"
    echo
    echo -e "${CYAN}访问地址:${NC} http://localhost:$PORT"
    echo -e "${CYAN}容器名称:${NC} $CONTAINER_NAME"
    echo -e "${CYAN}镜像版本:${NC} $(docker inspect --format='{{.Config.Image}}' $CONTAINER_NAME)"
    echo
    echo -e "${YELLOW}监控命令:${NC}"
    echo -e "  ${MAGENTA}查看日志:${NC} docker-compose -f $COMPOSE_FILE logs -f"
    echo -e "  ${MAGENTA}查看状态:${NC} docker-compose -f $COMPOSE_FILE ps"
    echo -e "  ${MAGENTA}查看资源:${NC} docker stats $CONTAINER_NAME"
    echo -e "  ${MAGENTA}进入容器:${NC} docker exec -it $CONTAINER_NAME sh"
    echo
    echo -e "${YELLOW}管理命令:${NC}"
    echo -e "  ${MAGENTA}停止服务:${NC} docker-compose -f $COMPOSE_FILE stop"
    echo -e "  ${MAGENTA}重启服务:${NC} docker-compose -f $COMPOSE_FILE restart"
    echo -e "  ${MAGENTA}销毁服务:${NC} docker-compose -f $COMPOSE_FILE down"
    echo
    echo -e "${GREEN}============================================================${NC}"
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean|-c)
                CLEAN_BUILD=true
                shift
                ;;
            --no-backup)
                SKIP_BACKUP=true
                shift
                ;;
            --force|-f)
                FORCE_DEPLOY=true
                shift
                ;;
            --help|-h)
                echo "使用方法: $0 [选项]"
                echo "选项:"
                echo "  -c, --clean      清理构建缓存"
                echo "  --no-backup      跳过备份"
                echo "  -f, --force      强制部署（跳过确认）"
                echo "  -h, --help       显示此帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                echo "使用 $0 --help 查看帮助"
                exit 1
                ;;
        esac
    done
    
    print_info "开始生产环境部署..."
    echo
    
    # 确认部署
    if [ "${FORCE_DEPLOY:-false}" != "true" ]; then
        print_warning "即将部署到生产环境"
        read -p "确认继续？(yes/no) " -r
        if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            print_info "部署已取消"
            exit 0
        fi
    fi
    
    # 执行部署流程
    check_requirements
    prepare_environment
    
    # 创建备份（除非明确跳过）
    if [ "${SKIP_BACKUP:-false}" != "true" ]; then
        create_backup
    fi
    
    stop_existing_container
    build_production_image
    deploy_container
    
    # 健康检查
    if health_check; then
        show_success_info
    else
        rollback
        exit 1
    fi
}

# -----------------------------------------------------------------------------
# 脚本入口
# -----------------------------------------------------------------------------
main "$@"