#!/bin/bash
# =============================================================================
# MockPix 快速部署脚本
# 用于自动化本地准备步骤
# =============================================================================

set -e  # 遇到错误立即退出

# -----------------------------------------------------------------------------
# 配置
# -----------------------------------------------------------------------------
SERVER_IP="*************"
PROJECT_NAME="mockpix"
BRANCH_NAME="story-1_完全使用shots样式-release-v1.1.0-1"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 辅助函数
# -----------------------------------------------------------------------------
print_step() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${GREEN}➜${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
    exit 1
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[提示]${NC} $1"
}

# -----------------------------------------------------------------------------
# 主流程
# -----------------------------------------------------------------------------
echo "========================================"
echo "   MockPix 快速部署脚本"
echo "   目标服务器: $SERVER_IP"
echo "   域名: www.mockpix.com"
echo "========================================"
echo ""

# 步骤 1: 检查 Git 状态
print_step "检查 Git 状态..."
if [ -n "$(git status --porcelain)" ]; then
    print_warning "检测到未提交的更改:"
    git status --short
    echo ""
    read -p "是否继续提交这些更改？(y/n): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "用户取消操作"
    fi
fi

# 步骤 2: 提交代码
print_step "提交代码到 GitHub..."
git add .
git commit -m "feat: 部署到 mockpix.com - $(date '+%Y-%m-%d %H:%M:%S')" || true
git push origin "$BRANCH_NAME"
print_success "代码已推送到 GitHub"

# 步骤 3: 构建 Docker 镜像
print_step "构建 Docker 镜像..."
echo "这可能需要 2-3 分钟，请耐心等待..."
docker build -f docker/1panel/Dockerfile.1panel -t mockpix:latest . || print_error "镜像构建失败"
print_success "镜像构建完成"

# 步骤 4: 导出镜像
print_step "导出 Docker 镜像..."
docker save mockpix:latest | gzip > mockpix-latest.tar.gz
IMAGE_SIZE=$(ls -lh mockpix-latest.tar.gz | awk '{print $5}')
print_success "镜像导出完成，大小: $IMAGE_SIZE"

# 步骤 5: 生成服务器部署脚本
print_step "生成服务器部署脚本..."
cat > deploy-on-server.sh << 'SCRIPT_EOF'
#!/bin/bash
# 服务器端部署脚本

set -e

echo "开始在服务器上部署 MockPix..."

# 1. 准备目录
echo "准备应用目录..."
mkdir -p /opt/1panel/apps/local/mockpix
cd /opt/1panel/apps/local/mockpix

# 2. 克隆代码
echo "克隆代码库..."
if [ ! -d ".git" ]; then
    git clone https://github.com/gcordon/next_wallpaper.git .
else
    git pull origin main
fi

# 切换到正确的分支
git checkout story-1_完全使用shots样式-release-v1.1.0-1 || git checkout -b story-1_完全使用shots样式-release-v1.1.0-1 origin/story-1_完全使用shots样式-release-v1.1.0-1

# 3. 配置环境
echo "配置环境变量..."
cp docker/1panel/.env.production .env
SESSION_SECRET=$(openssl rand -base64 32)
sed -i "s|please_change_this_to_a_random_string_in_production|$SESSION_SECRET|g" .env

# 4. 加载镜像
echo "加载 Docker 镜像..."
gunzip -c /tmp/mockpix-latest.tar.gz | docker load

# 5. 启动应用
echo "启动应用..."
docker-compose -f docker/1panel/docker-compose.yml down 2>/dev/null || true
docker-compose -f docker/1panel/docker-compose.yml up -d

# 6. 验证
echo "验证部署..."
sleep 5
if docker ps | grep -q mockpix; then
    echo "✅ 容器运行正常"
    curl -f http://localhost:3000 > /dev/null 2>&1 && echo "✅ 应用响应正常" || echo "⚠️  应用可能还在启动中"
else
    echo "❌ 容器启动失败"
    docker logs mockpix-production --tail=50
    exit 1
fi

echo ""
echo "========================================"
echo "   部署完成！"
echo "   请在 1Panel 中配置反向代理:"
echo "   代理地址: http://127.0.0.1:3000"
echo "========================================"
SCRIPT_EOF

chmod +x deploy-on-server.sh
print_success "服务器部署脚本已生成"

# 步骤 6: 上传文件到服务器
print_step "上传文件到服务器..."
echo ""
print_warning "即将上传以下文件到服务器 $SERVER_IP:"
echo "  1. mockpix-latest.tar.gz (镜像文件)"
echo "  2. deploy-on-server.sh (部署脚本)"
echo ""
read -p "按回车键继续上传，或按 Ctrl+C 取消: "

scp mockpix-latest.tar.gz root@$SERVER_IP:/tmp/ || print_error "镜像上传失败"
scp deploy-on-server.sh root@$SERVER_IP:/tmp/ || print_error "脚本上传失败"
print_success "文件上传完成"

# 完成提示
echo ""
echo "========================================"
echo -e "${GREEN}   本地准备完成！${NC}"
echo "========================================"
echo ""
echo "接下来请执行以下步骤:"
echo ""
echo "1. 连接到服务器:"
echo -e "   ${BLUE}ssh root@$SERVER_IP${NC}"
echo ""
echo "2. 执行部署脚本:"
echo -e "   ${BLUE}bash /tmp/deploy-on-server.sh${NC}"
echo ""
echo "3. 在 1Panel 中配置反向代理"
echo "   - 代理地址: http://127.0.0.1:3000"
echo "   - WebSocket: 开启"
echo ""
echo "4. 访问网站验证:"
echo -e "   ${BLUE}https://www.mockpix.com${NC}"
echo ""
echo "========================================"

# 询问是否自动连接服务器
echo ""
read -p "是否立即连接到服务器？(y/n): " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_step "正在连接服务器..."
    ssh root@$SERVER_IP
fi