#!/bin/bash
# =============================================================================
# Docker 部署文件清理脚本
# =============================================================================
# 功能：清理项目中冗余的Docker部署相关文件
# 说明：根据《DOCKER_部署文件整理与使用指南》的建议进行清理
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 辅助函数
# -----------------------------------------------------------------------------
print_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# -----------------------------------------------------------------------------
# 确认操作
# -----------------------------------------------------------------------------
confirm_action() {
    echo "========================================"
    echo "   Docker 部署文件清理工具"
    echo "========================================"
    echo
    echo "本脚本将清理以下冗余文件："
    echo
    echo "1. 归档目录："
    echo "   - docker/docs/archive/ (包含旧版docker-compose文件)"
    echo
    echo "2. 重复嵌套目录："
    echo "   - docker/1panel/docker/ (路径混乱的重复文件)"
    echo
    echo "3. 建议手动检查的文件："
    echo "   - scripts/build.sh (如未使用)"
    echo "   - scripts/dev.sh (如未使用)"
    echo "   - scripts/prod.sh (如未使用)"
    echo
    print_warning "此操作不可逆，建议先备份重要文件"
    echo
    read -p "是否继续清理？(y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_warning "操作已取消"
        exit 0
    fi
}

# -----------------------------------------------------------------------------
# 备份重要文件
# -----------------------------------------------------------------------------
backup_files() {
    print_step "创建备份..."
    
    BACKUP_DIR="docker-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份可能需要的文件
    if [ -d "docker/docs/archive" ]; then
        cp -r docker/docs/archive "$BACKUP_DIR/" 2>/dev/null || true
        print_success "已备份 docker/docs/archive 到 $BACKUP_DIR/"
    fi
    
    if [ -d "docker/1panel/docker" ]; then
        cp -r docker/1panel/docker "$BACKUP_DIR/" 2>/dev/null || true
        print_success "已备份 docker/1panel/docker 到 $BACKUP_DIR/"
    fi
    
    echo
    print_success "备份完成，文件保存在: $BACKUP_DIR/"
}

# -----------------------------------------------------------------------------
# 清理冗余文件
# -----------------------------------------------------------------------------
cleanup_files() {
    print_step "开始清理冗余文件..."
    echo
    
    # 清理归档目录
    if [ -d "docker/docs/archive" ]; then
        print_step "删除归档目录: docker/docs/archive/"
        rm -rf docker/docs/archive
        print_success "已删除归档目录"
    else
        print_warning "归档目录不存在，跳过"
    fi
    
    # 清理重复嵌套目录
    if [ -d "docker/1panel/docker" ]; then
        print_step "删除重复目录: docker/1panel/docker/"
        rm -rf docker/1panel/docker
        print_success "已删除重复目录"
    else
        print_warning "重复目录不存在，跳过"
    fi
    
    # 检查并提示其他可能的冗余文件
    echo
    print_step "检查其他可能的冗余文件..."
    
    # 检查未使用的脚本
    UNUSED_SCRIPTS=""
    [ -f "scripts/build.sh" ] && UNUSED_SCRIPTS="$UNUSED_SCRIPTS\n  - scripts/build.sh"
    [ -f "scripts/dev.sh" ] && UNUSED_SCRIPTS="$UNUSED_SCRIPTS\n  - scripts/dev.sh"
    [ -f "scripts/prod.sh" ] && UNUSED_SCRIPTS="$UNUSED_SCRIPTS\n  - scripts/prod.sh"
    
    if [ -n "$UNUSED_SCRIPTS" ]; then
        echo
        print_warning "发现可能未使用的脚本文件："
        echo -e "$UNUSED_SCRIPTS"
        echo
        echo "建议手动检查这些文件是否需要保留"
    fi
}

# -----------------------------------------------------------------------------
# 优化文件结构
# -----------------------------------------------------------------------------
optimize_structure() {
    print_step "优化文件结构..."
    echo
    
    # 确保必要的目录存在
    mkdir -p docker/scripts 2>/dev/null || true
    mkdir -p docker/docs 2>/dev/null || true
    
    # 创建 .dockerignore 如果不存在
    if [ ! -f ".dockerignore" ]; then
        print_step "创建 .dockerignore 文件..."
        cat > .dockerignore << 'EOF'
# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage
.nyc_output

# Next.js
.next
out
build
dist

# Misc
.DS_Store
*.pem
.vscode
.idea

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env.development
.env.test

# Vercel
.vercel

# Typescript
*.tsbuildinfo

# Docker
docker-backup-*
EOF
        print_success "已创建 .dockerignore 文件"
    fi
    
    print_success "文件结构优化完成"
}

# -----------------------------------------------------------------------------
# 显示清理结果
# -----------------------------------------------------------------------------
show_result() {
    echo
    echo "========================================"
    echo "   清理完成"
    echo "========================================"
    echo
    echo "当前Docker相关文件结构："
    echo
    tree -L 3 docker/ 2>/dev/null || {
        echo "docker/"
        find docker -type f -name "*.yml" -o -name "*.yaml" -o -name "Dockerfile*" | head -20
    }
    echo
    print_success "清理工作已完成！"
    echo
    echo "建议后续操作："
    echo "1. 查看备份目录确认没有误删重要文件"
    echo "2. 运行 ./scripts/local-start.sh 测试本地开发环境"
    echo "3. 查看 docker/DOCKER_部署文件整理与使用指南.md 了解详情"
    echo
}

# -----------------------------------------------------------------------------
# 主流程
# -----------------------------------------------------------------------------
main() {
    # 检查是否在项目根目录
    if [ ! -f "package.json" ] || [ ! -d "docker" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 确认操作
    confirm_action
    
    # 执行清理
    backup_files
    cleanup_files
    optimize_structure
    show_result
}

# -----------------------------------------------------------------------------
# 执行脚本
# -----------------------------------------------------------------------------
main "$@"