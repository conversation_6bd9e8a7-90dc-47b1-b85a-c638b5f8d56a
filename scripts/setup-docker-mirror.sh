#!/bin/bash

# Docker 镜像加速配置脚本
# 用于配置 Docker Desktop 使用国内镜像加速器

echo "正在配置 Docker 镜像加速器..."

# Docker Desktop 配置文件路径
CONFIG_FILE="$HOME/.docker/daemon.json"

# 创建备份
if [ -f "$CONFIG_FILE" ]; then
    cp "$CONFIG_FILE" "$CONFIG_FILE.backup"
    echo "已备份原配置文件到: $CONFIG_FILE.backup"
fi

# 创建或更新配置文件
cat > "$CONFIG_FILE" << 'EOF'
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerpull.com",
    "https://docker.1panel.live",
    "https://hub.rat.dev"
  ],
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  },
  "experimental": false
}
EOF

echo "Docker 镜像加速器配置完成！"
echo ""
echo "配置的镜像源："
echo "  - https://docker.m.daocloud.io (DaoCloud)"
echo "  - https://dockerpull.com"
echo "  - https://docker.1panel.live (1Panel官方)"
echo "  - https://hub.rat.dev"
echo ""
echo "请重启 Docker Desktop 使配置生效"
echo ""
echo "重启后可使用以下命令构建镜像："
echo "  docker build -f docker/1panel/Dockerfile.1panel.cn -t mockpix:latest ."