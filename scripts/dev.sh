#!/bin/bash

# =============================================================================
# Docker 开发环境启动脚本
# =============================================================================
# 用途：快速启动和管理 Docker 开发环境
# 特性：
# - 自动检查依赖
# - 环境准备和验证
# - 友好的错误提示
# - 实时日志支持
# =============================================================================

set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时报错

# -----------------------------------------------------------------------------
# 颜色定义
# -----------------------------------------------------------------------------
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 配置变量
# -----------------------------------------------------------------------------
COMPOSE_FILE="docker-compose.local.yml"
CONTAINER_NAME="next-wallpaper-dev"
PORT="3000"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# -----------------------------------------------------------------------------
# 工具函数
# -----------------------------------------------------------------------------
# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[DEV]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_step() {
    echo -e "${BLUE}==>${NC} $1"
}

# 显示旋转加载动画
show_spinner() {
    local pid=$1
    local delay=0.1
    local spinstr='|/-\\'
    while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
        local temp=${spinstr#?}
        printf " [%c]  " "$spinstr"
        local spinstr=$temp${spinstr%"$temp"}
        sleep $delay
        printf "\b\b\b\b\b\b"
    done
    printf "    \b\b\b\b"
}

# -----------------------------------------------------------------------------
# 环境检查
# -----------------------------------------------------------------------------
check_requirements() {
    print_step "检查系统要求..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装"
        print_info "请访问 https://docs.docker.com/get-docker/ 安装 Docker"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 守护进程未运行"
        print_info "请启动 Docker Desktop 或运行: sudo systemctl start docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装"
        print_info "请访问 https://docs.docker.com/compose/install/ 安装 Docker Compose"
        exit 1
    fi
    
    # 检查端口占用
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口 $PORT 已被占用"
        print_info "运行 'lsof -i :$PORT' 查看占用进程"
        read -p "是否继续？(y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_message "✓ 所有依赖检查通过"
}

# -----------------------------------------------------------------------------
# 环境准备
# -----------------------------------------------------------------------------
prepare_environment() {
    print_step "准备环境..."
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 检查 .env.local 文件
    if [ ! -f .env.local ]; then
        print_warning ".env.local 文件不存在"
        if [ -f .env.example ]; then
            print_info "从 .env.example 创建 .env.local"
            cp .env.example .env.local
        else
            print_info "创建空的 .env.local 文件"
            touch .env.local
        fi
    fi
    
    # 检查 Docker Compose 文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "找不到 $COMPOSE_FILE 文件"
        exit 1
    fi
    
    print_message "✓ 环境准备完成"
}

# -----------------------------------------------------------------------------
# 容器管理
# -----------------------------------------------------------------------------
stop_existing_container() {
    print_step "检查现有容器..."
    
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_warning "发现运行中的容器，正在停止..."
        docker-compose -f "$COMPOSE_FILE" down --remove-orphans 2>/dev/null || true
        print_message "✓ 旧容器已停止"
    fi
}

build_image() {
    print_step "构建 Docker 镜像..."
    
    # 检查是否需要重新构建
    if [ "${FORCE_BUILD:-false}" = "true" ] || [ ! "$(docker images -q next-wallpaper:dev 2> /dev/null)" ]; then
        print_info "开始构建镜像（这可能需要几分钟）..."
        docker-compose -f "$COMPOSE_FILE" build --progress=plain || {
            print_error "镜像构建失败"
            exit 1
        }
        print_message "✓ 镜像构建成功"
    else
        print_info "使用已存在的镜像，跳过构建"
        print_info "使用 --build 参数强制重新构建"
    fi
}

start_container() {
    print_step "启动容器..."
    
    docker-compose -f "$COMPOSE_FILE" up -d || {
        print_error "容器启动失败"
        docker-compose -f "$COMPOSE_FILE" logs --tail=50
        exit 1
    }
    
    print_message "✓ 容器启动成功"
}

# -----------------------------------------------------------------------------
# 健康检查
# -----------------------------------------------------------------------------
wait_for_healthy() {
    print_step "等待服务就绪..."
    
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker inspect --format='{{.State.Health.Status}}' "$CONTAINER_NAME" 2>/dev/null | grep -q "healthy"; then
            print_message "✓ 服务健康检查通过"
            return 0
        fi
        
        attempt=$((attempt + 1))
        printf "."
        sleep 2
    done
    
    print_error "服务启动超时"
    return 1
}

# -----------------------------------------------------------------------------
# 显示信息
# -----------------------------------------------------------------------------
show_success_info() {
    echo
    echo -e "${GREEN}============================================================${NC}"
    echo -e "${GREEN}       🚀 开发环境启动成功！${NC}"
    echo -e "${GREEN}============================================================${NC}"
    echo
    echo -e "${CYAN}访问地址:${NC} http://localhost:$PORT"
    echo -e "${CYAN}容器名称:${NC} $CONTAINER_NAME"
    echo -e "${CYAN}网络名称:${NC} next-wallpaper-dev-network"
    echo
    echo -e "${YELLOW}常用命令:${NC}"
    echo -e "  ${MAGENTA}查看日志:${NC} docker-compose -f $COMPOSE_FILE logs -f"
    echo -e "  ${MAGENTA}停止服务:${NC} docker-compose -f $COMPOSE_FILE down"
    echo -e "  ${MAGENTA}重启服务:${NC} docker-compose -f $COMPOSE_FILE restart"
    echo -e "  ${MAGENTA}进入容器:${NC} docker exec -it $CONTAINER_NAME sh"
    echo -e "  ${MAGENTA}查看状态:${NC} docker-compose -f $COMPOSE_FILE ps"
    echo
    echo -e "${GREEN}============================================================${NC}"
}

# -----------------------------------------------------------------------------
# 主函数
# -----------------------------------------------------------------------------
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build|-b)
                FORCE_BUILD=true
                shift
                ;;
            --logs|-l)
                SHOW_LOGS=true
                shift
                ;;
            --help|-h)
                echo "使用方法: $0 [选项]"
                echo "选项:"
                echo "  -b, --build    强制重新构建镜像"
                echo "  -l, --logs     启动后显示日志"
                echo "  -h, --help     显示此帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                echo "使用 $0 --help 查看帮助"
                exit 1
                ;;
        esac
    done
    
    # 执行启动流程
    print_info "开始启动 Docker 开发环境..."
    echo
    
    check_requirements
    prepare_environment
    stop_existing_container
    build_image
    start_container
    wait_for_healthy
    show_success_info
    
    # 如果指定了 --logs 参数，显示日志
    if [ "${SHOW_LOGS:-false}" = "true" ]; then
        print_info "显示容器日志（按 Ctrl+C 退出）..."
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# -----------------------------------------------------------------------------
# 脚本入口
# -----------------------------------------------------------------------------
main "$@"