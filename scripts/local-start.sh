#!/bin/bash
# =============================================================================
# 本地 Docker 开发环境快速启动脚本
# =============================================================================
# 功能：
# - 自动检测系统架构
# - 构建适合的 Docker 镜像
# - 启动本地开发环境
# - 支持热重载
# =============================================================================

set -e  # 遇到错误立即退出

# -----------------------------------------------------------------------------
# 配置
# -----------------------------------------------------------------------------
COMPOSE_FILE="docker-compose.local.yml"
CONTAINER_NAME="next-wallpaper-local"
IMAGE_NAME="next-wallpaper:local"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# -----------------------------------------------------------------------------
# 辅助函数
# -----------------------------------------------------------------------------
print_step() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} ${GREEN}➜${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
    exit 1
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[提示]${NC} $1"
}

# -----------------------------------------------------------------------------
# 检查依赖
# -----------------------------------------------------------------------------
check_dependencies() {
    print_step "检查 Docker 环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker"
    fi
    
    print_success "Docker 环境检查通过"
}

# -----------------------------------------------------------------------------
# 检测系统架构
# -----------------------------------------------------------------------------
detect_architecture() {
    print_step "检测系统架构..."
    
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            PLATFORM="linux/amd64"
            print_success "检测到 AMD64 架构"
            ;;
        arm64|aarch64)
            PLATFORM="linux/arm64"
            print_success "检测到 ARM64 架构"
            ;;
        *)
            print_warning "未知架构: $ARCH，使用默认配置"
            PLATFORM=""
            ;;
    esac
}

# -----------------------------------------------------------------------------
# 检查端口占用
# -----------------------------------------------------------------------------
check_port() {
    print_step "检查端口 3000..."
    
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口 3000 已被占用"
        read -p "是否停止占用端口的服务？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 尝试停止可能的容器
            docker stop $(docker ps -q --filter "publish=3000") 2>/dev/null || true
            sleep 2
            if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
                print_error "无法释放端口 3000，请手动停止占用的服务"
            fi
        else
            print_error "请先释放端口 3000"
        fi
    fi
    
    print_success "端口 3000 可用"
}

# -----------------------------------------------------------------------------
# 清理旧容器
# -----------------------------------------------------------------------------
cleanup_old_container() {
    print_step "清理旧容器..."
    
    if docker ps -a | grep -q $CONTAINER_NAME; then
        print_warning "发现旧容器，正在清理..."
        docker-compose -f $COMPOSE_FILE down 2>/dev/null || true
        docker rm -f $CONTAINER_NAME 2>/dev/null || true
    fi
    
    print_success "清理完成"
}

# -----------------------------------------------------------------------------
# 构建镜像
# -----------------------------------------------------------------------------
build_image() {
    print_step "构建 Docker 镜像..."
    
    # 检查是否需要使用国内镜像加速
    if [[ -n "$USE_CHINA_MIRROR" ]]; then
        print_warning "启用国内镜像加速..."
        # 临时修改 Dockerfile 启用镜像加速
        sed -i.bak 's/# RUN npm config set registry/RUN npm config set registry/g' Dockerfile
        sed -i.bak 's/# RUN pnpm config set registry/RUN pnpm config set registry/g' Dockerfile
        sed -i.bak "s/# RUN sed -i 's\/dl-cdn/RUN sed -i 's\/dl-cdn/g" Dockerfile
    fi
    
    # 构建镜像
    if [[ -n "$PLATFORM" ]]; then
        docker-compose -f $COMPOSE_FILE build --build-arg BUILDPLATFORM=$PLATFORM
    else
        docker-compose -f $COMPOSE_FILE build
    fi
    
    # 恢复 Dockerfile
    if [[ -n "$USE_CHINA_MIRROR" ]]; then
        mv Dockerfile.bak Dockerfile 2>/dev/null || true
    fi
    
    print_success "镜像构建完成"
}

# -----------------------------------------------------------------------------
# 启动服务
# -----------------------------------------------------------------------------
start_services() {
    print_step "启动开发环境..."
    
    docker-compose -f $COMPOSE_FILE up -d
    
    print_success "服务启动成功"
    echo
    echo "========================================"
    echo "   🚀 本地开发环境已启动"
    echo "========================================"
    echo
    echo "访问地址: http://localhost:3000"
    echo
    echo "常用命令:"
    echo "  查看日志: docker-compose -f $COMPOSE_FILE logs -f"
    echo "  进入容器: docker exec -it $CONTAINER_NAME sh"
    echo "  停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  重新构建: docker-compose -f $COMPOSE_FILE up --build"
    echo
    echo "提示: 代码修改会自动热重载"
    echo "========================================"
}

# -----------------------------------------------------------------------------
# 显示日志
# -----------------------------------------------------------------------------
show_logs() {
    read -p "是否查看日志？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_step "显示日志（按 Ctrl+C 退出）..."
        docker-compose -f $COMPOSE_FILE logs -f
    fi
}

# -----------------------------------------------------------------------------
# 主流程
# -----------------------------------------------------------------------------
main() {
    echo "========================================"
    echo "   Next Wallpaper 本地开发环境"
    echo "========================================"
    echo
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --china-mirror)
                USE_CHINA_MIRROR=1
                print_warning "将使用国内镜像加速"
                shift
                ;;
            --rebuild)
                FORCE_REBUILD=1
                print_warning "强制重新构建镜像"
                shift
                ;;
            --help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --china-mirror  使用国内镜像加速"
                echo "  --rebuild       强制重新构建镜像"
                echo "  --help          显示帮助信息"
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                ;;
        esac
    done
    
    # 执行主要步骤
    check_dependencies
    detect_architecture
    check_port
    cleanup_old_container
    
    # 检查是否需要构建镜像
    if [[ -n "$FORCE_REBUILD" ]] || ! docker images | grep -q "$IMAGE_NAME"; then
        build_image
    else
        print_step "使用现有镜像"
    fi
    
    start_services
    show_logs
}

# -----------------------------------------------------------------------------
# 执行脚本
# -----------------------------------------------------------------------------
main "$@"