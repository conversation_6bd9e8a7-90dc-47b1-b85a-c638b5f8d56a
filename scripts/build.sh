#!/bin/bash

# Docker 镜像构建脚本
# 用于构建和推送 Docker 镜像

set -e  # 遇到错误立即退出

# 默认配置
IMAGE_NAME="next-wallpaper"
DEFAULT_TAG="latest"
REGISTRY=""  # 如果需要推送到私有仓库，在这里设置

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${BLUE}[BUILD]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示使用帮助
show_help() {
    echo "使用方法: ./build.sh [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --tag <tag>      指定镜像标签 (默认: latest)"
    echo "  -p, --push           构建后推送到镜像仓库"
    echo "  -r, --registry <url> 指定镜像仓库地址"
    echo "  -d, --dev            构建开发镜像"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./build.sh                     # 构建生产镜像"
    echo "  ./build.sh -t v1.0.0          # 构建带特定标签的镜像"
    echo "  ./build.sh -d                  # 构建开发镜像"
    echo "  ./build.sh -t v1.0.0 -p       # 构建并推送镜像"
}

# 解析命令行参数
TAG=$DEFAULT_TAG
PUSH=false
BUILD_TARGET="runner"
FULL_IMAGE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -d|--dev)
            BUILD_TARGET="development"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

# 构建完整的镜像名称
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${TAG}"
fi

# 显示构建信息
print_message "==================== 构建信息 ===================="
print_message "镜像名称: $FULL_IMAGE_NAME"
print_message "构建目标: $BUILD_TARGET"
print_message "推送镜像: $PUSH"
print_message "================================================="
echo ""

# 开始构建
print_message "开始构建 Docker 镜像..."
docker build \
    --target "$BUILD_TARGET" \
    --tag "$FULL_IMAGE_NAME" \
    --file Dockerfile \
    .

# 检查构建是否成功
if [ $? -eq 0 ]; then
    print_success "镜像构建成功: $FULL_IMAGE_NAME"
    
    # 显示镜像信息
    print_message "镜像详情:"
    docker images | grep "$IMAGE_NAME" | grep "$TAG"
    
    # 如果需要推送
    if [ "$PUSH" = true ]; then
        print_message "推送镜像到仓库..."
        docker push "$FULL_IMAGE_NAME"
        if [ $? -eq 0 ]; then
            print_success "镜像推送成功!"
        else
            print_error "镜像推送失败"
            exit 1
        fi
    fi
    
    # 显示后续步骤
    echo ""
    print_message "后续步骤:"
    if [ "$BUILD_TARGET" = "development" ]; then
        print_message "  运行开发容器: docker run -p 3000:3000 -v \$(pwd):/app $FULL_IMAGE_NAME"
    else
        print_message "  运行生产容器: docker run -p 3000:3000 $FULL_IMAGE_NAME"
    fi
    print_message "  或使用 docker-compose: docker-compose -f docker-compose.prod.yml up"
else
    print_error "镜像构建失败"
    exit 1
fi