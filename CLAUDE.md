# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Claude交互规则

- 一直使用中文回复我答案

## 代码风格

- 使用 "if else"语法 不要使用 "switch"语法
- 使用 "4个空格代码"
- 单引号，不使用分号
- 多行时使用尾随逗号

## Quick Start Commands

```bash
# Development
pnpm dev          # Start development server with Turbopack (port 3000)
pnpm build        # Build for production
pnpm start        # Start production server

# Code Quality
pnpm lint         # Run ESLint
pnpm lint:fix     # Auto-fix ESLint issues
pnpm format       # Format with Prettier
pnpm format:check # Check formatting
```

## Build Configuration

- **Output Mode**: Standalone (optimized for Docker/1Panel deployment)
- **ESLint**: Configured to ignore during production builds
- **TypeScript**: Strict mode enabled, ignores errors in production builds
- **Port**: Development server runs on port 3000

## Code Formatting Rules (Prettier)

```json
{
    "semi": false,           // 不使用分号
    "tabWidth": 4,           // 4个空格缩进
    "singleQuote": true,     // 使用单引号
    "trailingComma": "all",  // 尾随逗号
    "printWidth": 100,       // 每行最大100字符
    "jsxSingleQuote": true,  // JSX中使用单引号
}
```

## Architecture Overview

### Tech Stack

- **Next.js 15.3.1** with App Router and Turbopack
- **React 19.0.0** with TypeScript 5 (strict mode)
- **Zustand 5.0.6** for state management
- **Tailwind CSS v4** + **Sass** for styling
- **React Spring** for animations
- **html-to-image** for export functionality

### Core Structure

```
app/
├── ImageMange/          # Centralized image management system
├── components/          # Reusable UI components
│   ├── CanvasContainer/ # Main canvas wrapper
│   ├── DisplayContainer/# Device layout engine
│   └── ExportPopver/    # Export functionality
├── features/            # Feature-specific modules
│   └── viewDimensions/  # View calculations
├── hooks/               # State management hooks
├── utils/               # Utility functions
└── config/              # Configuration files
```

### State Management Architecture

1. **useAppStore** (`features/viewDimensions/utils/重构/状态管理.ts`)
   - View dimensions and canvas sizing
   - Export settings and format options
   - Background and layout preferences

2. **useImageStore** (`ImageMange/imageMangeIndex.tsx`)
   - Image storage and management
   - Device-image binding relationships
   - Upload state and conflict resolution

3. **Custom Hooks**
   - `useAppState.ts`: Simplified state access
   - `usePasteImage.ts`: Clipboard handling
   - `useViewDimensions.ts`: Responsive calculations

### Component Architecture

- **CanvasContainer**: Main responsive wrapper with dimension calculations
- **DisplayContainer**: Core rendering engine with SVG masking support
- **DeviceContainer**: Individual device rendering with 3D transforms
- **ImageUpload**: Drag & drop + clipboard integration
- **ExportPopover**: Multi-format export with quality presets

## Key Features Implementation

### Image Management System

- **Binding Logic**: One-to-many relationships in `ImageMange/imageMangeIndex.tsx`
- **Conflict Resolution**: Automatic handling when reassigning images
- **Upload Methods**: Dropzone + paste event listeners
- **Validation**: File type checking and size limits

### Responsive Design

- **Breakpoint**: 800px (mobile/desktop)
- **Mobile Layout**: Stacked with tab navigation
- **Desktop Layout**: Three-panel (sidebars + canvas)
- **Canvas Scaling**: Dynamic based on viewport

### Export System

- **Formats**: PNG (default), JPEG
- **Quality Presets**: 
  - Low (1x), Medium (2x), High (3x) scaling
- **Implementation**: `utils/imageExport.ts` using html-to-image
- **Clipboard**: Direct copy functionality

### Keyboard Shortcuts

- **Q/W**: Switch between tabs (mobile)
- **A/S/D**: Switch layout modes

## Development Patterns

### File Organization

- **Feature-based**: Each feature in its own directory
- **Component structure**: `Component.tsx` + `Component.module.scss`
- **Shared utilities**: Centralized in `utils/`
- **Type definitions**: Co-located with features

### Styling Approach

- **CSS Modules**: Component-scoped styles
- **Tailwind**: Utility classes for rapid development
- **CSS Variables**: Theme and dynamic values
- **Dark Mode**: Integrated throughout with CSS variables

### Performance Optimizations

- **React.memo**: For expensive components
- **useMemo/useCallback**: Strategic memoization
- **Image optimization**: Lazy loading and compression
- **Zustand devtools**: Redux DevTools integration

## Critical Files Reference

- `app/page.tsx`: Main application entry
- `app/hooks/useAppState.ts`: Primary state access
- `app/ImageMange/imageMangeIndex.tsx`: Image store
- `app/components/DisplayContainer/DisplayConfig.ts`: Layout configurations
- `app/utils/imageExport.ts`: Export implementation
- `app/features/viewDimensions/utils/重构/算法.ts`: Dimension calculations

## Development Philosophy

- 你是一位推崇代码清晰易读的程序员。请在写代码时，为所有函数和关键逻辑添加中文注释。特别是计算部分，必须用具体数值举例说明
- 如果你（ai）需要启动pnpm dev运行，可以先检测本地3000端口是否已运行，如未运行可告知我，我自行运行 pnpm dev

## Important Notes

- **No test framework** currently configured
- **TypeScript strict mode** is enabled - maintain type safety
- **ESLint unused vars** rule is disabled for development flexibility
- **Path alias**: Use `@/*` for imports from project root
- **Package Manager**: Use `pnpm` for all dependencies management