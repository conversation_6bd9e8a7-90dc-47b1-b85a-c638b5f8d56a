import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
    // 启用 standalone 输出模式
    // 这会创建一个独立的生产构建，包含所有必要的依赖
    // 非常适合容器化部署（Docker、1Panel 等）
    output: 'standalone',
    
    // 图片优化配置
    images: {
        // 如果使用外部图片服务，在这里配置域名
        domains: [],
    },
    
    // ESLint 配置
    // 在生产构建时忽略 ESLint 错误
    eslint: {
        // 在生产构建时忽略 ESLint 错误
        ignoreDuringBuilds: true,
    },
    
    // TypeScript 配置
    // 在生产构建时忽略 TypeScript 错误
    typescript: {
        // 在生产构建时忽略 TypeScript 错误
        ignoreBuildErrors: true,
    },
}

export default nextConfig
